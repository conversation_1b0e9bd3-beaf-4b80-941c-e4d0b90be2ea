<?php

namespace App\Http\Controllers\Web;

use App\Models\User;
use App\Models\Config;
use Antto\Sms\Facades\Sms;
use App\Admin\Repositories\User as RepositoriesUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Artesaos\SEOTools\Facades\SEOMeta;

class AuthController extends WebController
{
    /**
     * 登录
     */
    public function login()
    {
        SEOMeta::setTitle('登录');
        return view('auth.login');
    }

    /**
     * 登录表单提交
     */
    public function loginSubmit(Request $request)
    {
        SEOMeta::setTitle('登录');
        $this->validate($request, [
            'mobile'   => 'required',
            'password' => 'required',
        ]);

        $credentials = $request->only('mobile', 'password');

        if (auth()->attempt($credentials)) {

            $request->session()->regenerate();

            // 判断用户角色
            if (auth()->user()->role != RepositoriesUser::ROLE_NURSINGHOME && auth()->user()->role != RepositoriesUser::ROLE_INSTITUTION_STAFF) {
                auth()->logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route('login')->with('warning', '账号权限错误');
            }

            // 判断用户状态
            if (auth()->user()->status == 0) {
                auth()->logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route('login')->with('warning', '账号未激活');
            }

            return redirect()->intended('/dashboard')->with('success', '登录成功');
        }

        return redirect()->route('login')->withErrors([
            'mobile' => '登录失败，请检查账号或密码。',
        ]);
    }

    /**
     * 发送验证码
     */
    public function sendSmsCode(Request $request)
    {
        $request->validate(
            [
                'mobile' => 'required|is_mobile|can_send',
            ],
            [
                'mobile.required' => '手机号不能为空',
                'mobile.is_mobile' => '手机号格式不正确',
                'mobile.can_send' => '验证码发送过于频繁',
            ]
        );

        $mobile = $request->mobile;

        // 测试手机号不发短信 START
        $whitelist = [];
        $result = Config::where('key', 'whitelist')->first();
        if ($result) {
            $whitelist = array_map('trim', explode("\n", $result->value1));
        }
        if (in_array($mobile, $whitelist)) {
            return response()->json(['success' => true, 'message' => '发送成功（白名单）']);
        }
        // 测试手机号不发短信 END

        // 限制每天每个手机号只能发送6次
        $today = date('Y-m-d');
        $count = \App\Models\SmsLog::where('mobile', $mobile)
            ->whereDate('created_at', $today)
            ->count();
        if ($count >= 8) {
            return response()->json(['success' => false, 'message' => '今天发送验证码次数已达上限']);
        }

        if (Sms::sendVerifyCode($mobile)) {
            return response()->json(['success' => true, 'message' => '发送成功']);
        } else {
            return response()->json(['success' => false, 'message' => '发送失败，请稍后重试']);
        }
    }

    /**
     * 验证码登录
     */
    public function smsLogin(Request $request)
    {
        $request->validate(
            [
                'mobile' => 'required|is_mobile',
                'code' => 'required',
            ],
            [
                'mobile.required' => '手机号不能为空',
                'mobile.is_mobile' => '手机号格式不正确',
                'code.required' => '验证码不能为空',
            ]
        );

        $mobile = $request->mobile;
        $code = $request->code;

        // 测试手机号白名单验证
        $whitelist = [];
        $result = Config::where('key', 'whitelist')->first();
        if ($result) {
            $whitelist = array_map('trim', explode("\n", $result->value1));
        }

        $user = null;

        if (in_array($mobile, $whitelist)) {
            // 白名单用户，任意验证码都可以登录
            $user = User::where('mobile', $mobile)->first();
            if (!$user) {
                return redirect()->route('login')->withErrors([
                    'mobile' => '用户不存在',
                ]);
            }
        } else {
            // 使用自定义验证规则验证验证码
            try {
                $request->validate(
                    [
                        'code' => 'required|verify_code:' . $mobile
                    ],
                    [
                        'code.verify_code' => '验证码错误或已过期'
                    ]
                );
            } catch (\Illuminate\Validation\ValidationException $e) {
                return redirect()->route('login')->withErrors($e->errors());
            }

            $user = User::where('mobile', $mobile)->first();
            if (!$user) {
                return redirect()->route('login')->withErrors([
                    'mobile' => '用户不存在',
                ]);
            }
        }

        if (!$user) {
            return redirect()->route('login')->withErrors([
                'mobile' => '用户不存在',
            ]);
        }

        if ($user->status == 0) {
            return redirect()->route('login')->withErrors([
                'mobile' => '账号未激活',
            ]);
        }

        // 仅限机构用户登录
        if ($user->role != RepositoriesUser::ROLE_NURSINGHOME && $user->role != RepositoriesUser::ROLE_INSTITUTION_STAFF) {
            return redirect()->route('login')->withErrors([
                'mobile' => '账号权限错误',
            ]);
        }

        // 登录用户
        Auth::login($user, true);
        $request->session()->regenerate();

        return redirect()->intended('/dashboard')->with('success', '登录成功');
    }

    /**
     * 注册
     */
    public function register(Request $request)
    {
        SEOMeta::setTitle('注册');
        return view('auth.register');
    }

    /**
     * 注册表单提交
     */
    public function registerSubmit(Request $request)
    {
        $this->validate($request, [
            'name'                  => 'required',
            'mobile'                => 'required|unique:users,mobile|phone',
            'password'              => 'required|confirmed',
            'password_confirmation' => 'required|same:password',
        ], [
            'name.required'      => '姓名不能为空',
            'mobile.required'    => '手机号不能为空',
            'mobile.unique'      => '手机号已存在',
            'mobile.phone'       => '手机号格式不对',
            'password.required'  => '密码不能为空',
            'password.confirmed' => '两次密码不一致',
        ]);

        $user = new User();
        $user->nickname = $request->input('name');
        $user->mobile = $request->input('mobile');
        $user->password = bcrypt($request->input('password'));

        // 注册后默认未激活
        $user->status = 0;
        $user->save();

        return redirect()->route('auth.register.success');
    }

    /**
     * 手机号验证码修改密码
     */
    public function password()
    {
        SEOMeta::setTitle('修改密码');
        if (!auth()->check()) {
            return redirect()->route('login');
        }
        return view('auth.password');
    }

    /**
     * 手机号验证码修改密码提交
     */
    public function passwordSubmit(Request $request)
    {
        $this->validate(
            $request,
            [
                'mobile'                => 'required|exists:users,mobile|is_mobile',
                'verifyCode'            => 'required|verify_code:' . $request->mobile,
                'password'              => 'required|confirmed',
                'password_confirmation' => 'required|same:password',
            ],
            [
                'mobile.required'        => '手机号不能为空',
                'mobile.exists'          => '用户不存在',
                'mobile.is_mobile'       => '手机号格式不正确',
                'verifyCode.required'    => '验证码不能为空',
                'verifyCode.verify_code' => '验证码错误',
                'password.required'      => '密码不能为空',
                'password.confirmed'     => '两次密码不一致',
            ]
        );

        $mobile = $request->input('mobile');
        $user = User::where('mobile', $mobile)->first();
        if ($user) {
            $user->update([
                'mobile' => $mobile,
                'password' => bcrypt($request->input('password'))
            ]);
        }
        return redirect()->route('login');
    }
    /**
     * 旧密码修改密码
     */
    public function changePassword()
    {
        SEOMeta::setTitle('修改密码');
        if (!auth()->check()) {
            return redirect()->route('login');
        }
        return view('auth.change-password');
    }

    /**
     * 旧密码修改密码提交
     */
    public function changePasswordSubmit(Request $request)
    {
        $this->validate(
            $request,
            [
                'old_password'          => 'required',
                'password'              => 'required|confirmed',
                'password_confirmation' => 'required|same:password',
            ],
            [
                'old_password.required'  => '密码不能为空',
                'password.required'      => '密码不能为空',
                'password.confirmed'     => '两次密码不一致',
            ]
        );
        $user = $request->user();
        if ($user) {
            if (\Hash::check($request->input('old_password'), $user->password)) {
                $user->update([
                    'password' => bcrypt($request->input('password'))
                ]);
                auth()->logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                return redirect()->route('login')->with(['message' => '密码修改成功，请重新登录']);
            }
            return redirect()->route('auth.change.password')->withErrors(['message' => '旧密码不正确']);
        }
        return redirect()->route('auth.change.password')->withErrors(['message' => '请先登录']);
    }


    /**
     * 发送验证码
     */
    public function sendmsg(Request $request)
    {
        $request->validate([
            'sendMobile' => 'required|exists:users,mobile|is_mobile|can_send',
        ], [
            'sendMobile.required'  => '手机号不能为空',
            'sendMobile.exists'    => '用户不存在',
            'sendMobile.is_mobile' => '手机号格式不正确',
            'sendMobile.can_send'  => '请勿频繁发送验证码',
        ]);

        $sendmobile = $request->post('sendMobile');
        $test_mobile = User::where('mobile', $sendmobile)->first();

        if (!$test_mobile) {
            return [
                'code' => 500,
                'msg' => '手机号不存在！',
                'data' => ''
            ];
        }

        if (Sms::sendVerifyCode($request->sendMobile)) {
            return response()->json([
                'code' => 200,
                'msg' => '发送成功',
            ]);
        } else {
            return response()->json([
                'code' => 500,
                'msg' => '发送失败',
            ], 500);
        }
    }

    /**
     * 注册成功
     */
    public function registrationSuccess()
    {
        SEOMeta::setTitle('注册成功');
        return view('auth.registration-success');
    }

    /**
     * 个人资料
     */
    public function personalCenter()
    {
        SEOMeta::setTitle('个人中心');
        if (!auth()->check()) {
            return redirect()->route('login');
        }
        return view('auth.personal-center', ['user' => auth()->user()]);
    }

    /**
     * 修改个人信息
     */
    public function updateProfile(Request $request)
    {
        SEOMeta::setTitle('个人中心');
        if (!auth()->check()) {
            return redirect()->route('login');
        }
        // 获取用户信息
        $user = $request->user();
        return view('auth.update-profile', ['user' => $user]);
    }

    /**
     * 修改个人信息提交
     */
    public function updateProfileSubmit(Request $request)
    {
        $user = $request->user();
        $this->validate($request, [
            'nickname'                  => 'required',
            'mobile' => [
                'required',
                Rule::unique('users')->ignore($user->id),
                'phone'
            ],
            'email'                 => 'nullable|email',
        ], [
            'nickname.required'  => '昵称不能为空',
            'mobile.required'    => '手机号不能为空',
            'mobile.unique'      => '手机号已存在',
            'mobile.phone'       => '手机号格式不对',
            'email.email'        => '邮箱格式不对',
        ]);

        $user->nickname = $request->input('nickname');
        $user->real_name = $request->input('name');
        $user->mobile = $request->input('mobile');
        $user->email = $request->input('email');
        $user->gender = $request->input('gender');
        $user->birthdate = $request->input('birthdate');
        $user->save();

        return redirect()->route('auth.update.profile')->with('successMessage', '修改成功');
    }

    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        auth()->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home')->with('successMessage', '退出成功');
    }

    /**
     * 第三方登录 - 微信
     */
    public function redirectToWechat()
    {
        return redirect()->to(\Socialite::create('wechat')->redirect());
    }

    /**
     * 第三方登录 - 微信回调
     */
    public function handleWechatCallback(Request $request)
    {
        $user = \Socialite::create('wechat')->userFromCode($request->query('code'));
        \Log::info('wechat login', $user->toArray());
    }

}
