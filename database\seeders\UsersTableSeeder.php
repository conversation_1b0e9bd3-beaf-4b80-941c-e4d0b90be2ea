<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class UsersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        \DB::table('users')->delete();
        \DB::table('users')->insert(array (
            0 =>
            array (
                'id' => 1,
                'mobile' => '18888888888',
                'email' => '<EMAIL>',
                'password' => '$2y$10$Mu5hW.np.9fctMn9WCcjFeq0TMjxK/p1NXKr.mXiD5DVVFsSjObXm',
                'avatar_url' => 'avatars/default-avatar.png',
                'email_verified_at' => NULL,
                'nickname' => '用户1681885267',
                'real_name' => NULL,
                'gender' => 1,
                'birthdate' => '2000-01-01',
                'province' => '江苏',
                'city' => '昆山市',
                'district' => '花桥镇',
                'address' => '中寰广场',
                'role' => 1,
                'status' => 1,
                'wx_nickname' => NULL,
                'wx_avatar' => NULL,
                'wx_openid' => NULL,
                'xcx_openid' => NULL,
                'unionid' => NULL,
                'oauth_scope' => NULL,
                'remember_token' => NULL,
                'created_at' => '2022-11-02 16:31:41',
                'updated_at' => '2022-11-02 16:31:41',
            ),
        ));
        PersonalAccessToken::insert([
            'tokenable_type' => 'App\\Models\\User',
            'tokenable_id' => 1,
            'name' => 'token-name',
            'token' => 'f732b0fc5b989e787b435e915dc38b4a65849de0da01e6457b47f87e9b97f0c8',
            'abilities' => '["*"]',
        ]);
    }
}
