<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UserNursingHome;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserNursingHomeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserNursingHome(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('nursing_home_id');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserNursingHome(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('nursing_home_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserNursingHome(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('nursing_home_id');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
