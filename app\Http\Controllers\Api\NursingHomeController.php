<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Diary;
use App\Models\UserLike;
use App\Models\UserFollow;
use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\UserNursingHome;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\DiaryResource;
use App\Models\InstitutionUserPermission;
use App\Http\Resources\NursingHomeResource;
use App\Admin\Repositories\User as UserRepo;

class NursingHomeController extends ApiController
{
    /**
     * 列表
     * @param  request
     */
    public function index(Request $request)
    {
        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        $limit = $request->limit ?? 10;
        $province = $request->province ?? '';
        $city = $request->city ?? '';
        $district = $request->district ?? '';
        $result = NursingHome::with(['milestones'])->where('status', 1)
            ->orderBy('created_at', 'desc')
            ->when($province, function ($query) use ($province) {
                $query->where('province', $province);
            })
            ->when($city, function ($query) use ($city) {
                $query->where('city', $city);
            })
            ->when($district, function ($query) use ($district) {
                $query->where('district', $district);
            })
            ->paginate($limit);
        foreach ($result as $item) {
            if (auth('api')->check()) {
                // 是否关注
                $item->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $item->id)->exists();
                // 是否为我的
                $item->is_my = UserNursingHome::where('user_id', $user->id)->where('nursing_home_id', $item->id)->exists();
            }
            // 关注数量
            $item->follow_number = UserFollow::where('nursing_home_id', $item->id)->count();
        }
        return $this->success(NursingHomeResource::collection($result));
    }

    /**
     * 养老院基本信息
     */
    public function basic(Request $request)
    {
        $id = $request->id;
        $result = NursingHome::with(['milestones'])
            ->where('status', 1)
            ->find($id);
        return $this->success(new NursingHomeResource($result));
    }

    /**
     * 详情
     *
     * @param  int  $id
     */
    public function show($id, Request $request)
    {
        // Log::info("养老院：" . $id);

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        $result = NursingHome::with(['milestones'])
            ->where('status', 1)
            ->find($id);

        // 排除当前养老院的其他随机4个日记
        if ($result) {
            $diaries = $result->diaries()->where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get();
            // $otherDiaries = Diary::where('status', 3)
            //     ->where('nursing_home_id', '!=', $result->id)
            //     ->inRandomOrder()
            //     ->orderBy('created_at', 'desc')
            //     ->limit(4)
            //     ->get();
            if (auth('api')->check()) {
                // 是否关注
                $result->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $result->id)->exists();
                // 是否为我的
                $result->is_my = UserNursingHome::where('user_id', $user->id)->where('nursing_home_id', $result->id)->exists();
            }
            // 关注数量
            $result->follow_number = UserFollow::where('nursing_home_id', $result->id)->count();

            if (auth('api')->check()) {
                // 是否关注
                foreach ($diaries as $value) {
                    // 是否点赞
                    $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                }
            }

            return $this->success([
                'nursing_home' => new NursingHomeResource($result),
                'diaries' => DiaryResource::collection($diaries),
                // 'other_diaries' => DiaryResource::collection($otherDiaries),
            ]);
        }
        $this->errorBadRequest('暂无数据');
    }


    /**
     * 养老院下的日记
     */
    public function diaries(Request $request)
    {
        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        $limit = $request->limit ?? 12;
        $id = $request->nursing_home_id;
        $result = Diary::where('status', 3)
            ->where(function ($query) {
                $query->whereNull('publish_at')
                    ->orWhere('publish_at', '<=', now());
            })
            ->where('nursing_home_id', $id)
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        if (auth('api')->check()) {
            // 是否关注
            foreach ($result as $value) {
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)
                    ->where('diary_id', $value->id)
                    ->exists();
            }
        }
        return $this->success(DiaryResource::collection($result));
    }


    /**
     * 列表
     */
    public function tree(Request $request)
    {
        // 上海市的所有区
        $districts = [
            '黄浦区',
            '徐汇区',
            '长宁区',
            '静安区',
            '普陀区',
            '虹口区',
            '杨浦区',
            '闵行区',
            '宝山区',
            '嘉定区',
            '浦东新区',
            '金山区',
            '松江区',
            '青浦区',
            '奉贤区',
            '崇明区'
        ];

        // 查询上海市的所有机构
        $result = NursingHome::with(['milestones'])
            ->where('status', 1)
            ->where('province', '上海市')
            ->where('city', '上海市')
            ->orderBy('created_at', 'desc')
            ->get();

        // 将结果按照 district 分组
        $treeData = [];
        foreach ($districts as $district) {
            $filteredItems = $result->filter(function ($item) use ($district) {
                return $item->district === $district;
            })->map(function ($item) use ($request) {
                // 添加额外字段
                if (auth('api')->check()) {
                    // $item->is_follow = UserFollow::where('user_id', $request->user()->id)
                    //     ->where('nursing_home_id', $item->id)->exists();
                    $item->is_my = UserNursingHome::where('user_id', $request->user()->id)
                        ->where('nursing_home_id', $item->id)->exists();
                }
                // $item->follow_number = UserFollow::where('nursing_home_id', $item->id)->count();

                // 返回精简后的数据
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'address' => $item->address,
                    // 'is_follow' => $item->is_follow ?? false,
                    'is_my' => $item->is_my ?? false,
                    // 'follow_number' => $item->follow_number,
                ];
            })->values()->all(); // 转换为数组

            // 如果该区有数据，则添加到树形结构中
            if (!empty($filteredItems)) {
                $treeData[] = [
                    'district' => "上海{$district}",
                    'list' => $filteredItems,
                ];
            }
        }
        return $this->success(['list' => $treeData]);
    }

    /**
     * 关注
     *
     * @param  $request
     * @return \Illuminate\Http\Response
     */
    public function follow(Request $request)
    {
        $request->validate(
            [
                'nursing_home_id' => 'required|integer',
            ],
            [
                'nursing_home_id.required' => '养老院ID不能为空',
                'nursing_home_id.integer' => '养老院ID格式错误',
            ]
        );
        $user = $request->user();
        $nursingHomeId = $request->nursing_home_id;
        $nursingHome = NursingHome::find($nursingHomeId);
        if ($nursingHome) {
            $follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $nursingHomeId)->first();
            if ($follow) {
                $follow->delete();
                return $this->ok('取消关注成功');
            } else {
                UserFollow::create([
                    'user_id' => $user->id,
                    'nursing_home_id' => $nursingHome->id,
                ]);
                return $this->ok('关注成功');
            }
        }
        $this->errorBadRequest('养老院不存在');
    }

    // 用户关注列表
    public function followList(Request $request)
    {
        $user = $request->user();
        $limit = $request->limit ?? 10;
        $result = NursingHome::whereHas('userFollows', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->paginate($limit);
        foreach ($result as $item) {
            // 是否关注
            $item->is_follow = true;
        }
        return $this->success(NursingHomeResource::collection($result));
    }

    /**
     * 确定为我的养老院
     *
     * @param  $request
     */
    public function myNursingHome(Request $request)
    {
        $request->validate(
            [
                'nursing_home_id' => 'required|integer',
            ],
            [
                'nursing_home_id.required' => '养老院ID不能为空',
                'nursing_home_id.integer' => '养老院ID格式错误',
            ]
        );
        $user = $request->user();
        $nursingHomeId = $request->nursing_home_id;
        $nursingHome = NursingHome::find($nursingHomeId);
        if ($nursingHome) {
            $follow = UserNursingHome::where('user_id', $user->id)
                ->where('nursing_home_id', $nursingHomeId)
                ->first();
            if ($follow) {
                $follow->delete();
                return $this->ok('取消加入我的养老院');
            } else {
                // UserNursingHome::create([
                //     'user_id' => $user->id,
                //     'nursing_home_id' => $nursingHome->id,
                // ]);

                // 确保用户只有一个“我的”养老院。如果存在则更新，否则创建新记录
                UserNursingHome::updateOrCreate(
                    ['user_id' => $user->id],
                    [
                        'nursing_home_id' => $nursingHome->id,
                    ]
                );

                return $this->ok('加入我的养老院成功');
            }
        }
        $this->errorBadRequest('养老院不存在');
    }

    // 我的养老院列表
    public function myNursingHomeList(Request $request)
    {
        $user = $request->user();
        $limit = (int) ($request->limit ?? 10);
        $result = NursingHome::with(['milestones'])->whereHas('UserNursingHomes', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->paginate($limit);
        foreach ($result as $item) {
            // 是否关注
            $item->is_my = true;
        }
        return $this->success(NursingHomeResource::collection($result));
    }

    /**
     * 同意邀请
     * 员工通过二维码邀请加入养老院
     */
    public function handleQrInvite(Request $request)
    {
        // 获取当前扫描二维码的用户
        $user = $request->user();

        $nursing_home_id = $request->nursing_home_id;
        $timestamp = $request->timestamp;
        $name = $request->name;
        $mobile = $request->mobile;

        if (!$nursing_home_id) {
            return $this->errorBadRequest('邀请用户未绑定机构。');
        }

        // 获取养老院信息
        $nursingHome = NursingHome::find($nursing_home_id);

        if (!$nursingHome) {
            return $this->errorBadRequest('机构信息不存在。');
        }

        // 检查当前用户是否已经是该机构的员工
        $existingPermission = InstitutionUserPermission::where('user_id', $user->id)
            ->where('nursing_home_id', $nursing_home_id)
            ->first();

        if ($existingPermission) {
            return $this->errorBadRequest('您已经是该机构的员工。');
        }

        // 绑定用户到机构
        $user->nursingHomes()->attach($nursing_home_id);

        // 设定当前用户的管理机构
        $user->manage_nursing_home_id = $nursing_home_id;
        $user->nursing_home_id = $nursing_home_id;
        $user->role = UserRepo::ROLE_NURSINGHOME;
        if ($name) {
            $user->nickname = $name;
        }
        if ($mobile) {
            $user->mobile = $mobile;
        }
        $user->save();

        // 创建员工权限记录
        InstitutionUserPermission::create([
            'user_id'            => $user->id,
            'nursing_home_id'    => $nursing_home_id,
            'can_manage_staff'   => false,
            'can_manage_info'    => false,
            'can_manage_videos'  => false,
            'can_view_data'      => false,
            'can_manage_finance' => false,
        ]);

        return $this->ok('成功加入机构：' . $nursingHome->name);
    }
}
