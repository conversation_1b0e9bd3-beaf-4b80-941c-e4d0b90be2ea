<?php
namespace App\Http\Controllers\Web;

use App\Models\Page;
use Artesaos\SEOTools\Facades\SEOMeta;
use App\Http\Controllers\Web\WebController;

class PageController extends WebController
{
    public function index($permalink1, $permalink2 = '', $permalink3 = '')
    {
        $permalink = $permalink3?:$permalink2?:$permalink1;
        $page = Page::with('parent.parent')->where('permalink', $permalink)->first();
        if($page) {
            if($page->seo_title){
                SEOMeta::setTitle($page->seo_title);
            }
            if($page->seo_keywords){
                SEOMeta::addKeyword($page->seo_keywords);
            }
            if($page->seo_description){
                SEOMeta::setDescription($page->seo_description);
            }
            return view('templates.global', ['page' => $page]);
        }
        return view('templates.page-not-found');
    }
}
