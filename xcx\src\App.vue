<script>
import api from "@/common/api";
export default {
  onLaunch: function (options) {
    let user_id = "";
    if (options.query.scene) {
      let scanUrl = decodeURIComponent(options.query.scene);
      user_id = scanUrl.split("user_id=")[1];
    }

    // 小程序静默登录
    if (!uni.getStorageSync("token")) {
      if (options.path == "pages/auth/fake") {
        return;
      } else {
        console.log("尝试静默登录");
        let pagePath = "/" + options.path;
        // 拼接Query参数
        if (options.query) {
          let query = Object.keys(options.query)
            .map((key) => `${key}=${options.query[key]}`)
            .join("&");
          pagePath += "?" + query;
        }
        this.login(pagePath, user_id);
      }
    } else {
      this.completeUserInfo();
    }
  },
  onShow: function () {
    // console.log("App Show");
    // uni.setStorageSync("systemInfo", this.$u.sys());
  },
  onHide: function () {
    // console.log("App Hide");
  },
  methods: {
    completeUserInfo() {
      const storeUser = this.$store.getters.getUser;
      const storageUser = uni.getStorageSync("user");
      const userInfo = uni.getStorageSync("userInfo");

      // 优先使用store中的数据，然后是storage，最后是userInfo
      let user = storeUser;
      if (!user || Object.keys(user).length === 0) {
        user = storageUser;
      }
      if (!user || Object.keys(user).length === 0) {
        user = userInfo;
      }
      // 获取completeUserInfoTime
      let completeUserInfoTime = uni.getStorageSync("completeUserInfoTime");
      if (
        completeUserInfoTime &&
        Date.now() - completeUserInfoTime < 1000 * 60 * 60 * 24
      ) {
        return;
      }
      // 检查用户信息是否需要补全
      const needCompleteInfo =
        !user.avatar_url ||
        user.avatar_url ===
          "https://diary.laoyangapp.com/images/placeholder-avatar.png" ||
        !user.nickname ||
        user.nickname.startsWith("用户");
      if (needCompleteInfo) {
        uni.setStorageSync("completeUserInfoTime", Date.now());
        this.$store.commit("TOGGLE_USER_INFO_MODAL", true);
      }
    },

    async login(pagePath, user_id) {
      let login = await uni.login();
      if (login[1].code) {
        uni.showToast({
          title: "登录中...",
          icon: "loading",
          mask: true,
        });
        let res = await api.getTokenXcx({
          data: {
            code: login[1].code,
            sales_id: user_id,
          },
          method: "POST",
        });
        if (res.data.code == 200) {
          let resData = res.data.data;
          uni.setStorageSync("token", resData.token);
          uni.setStorageSync("is_new", resData.is_new);
          this.$store.commit("SET_USER", resData.user);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      }
    },
  },
};
</script>

<style lang="scss">
@import "uview-ui/index.scss";
@import "static/css/global.scss";
</style>
