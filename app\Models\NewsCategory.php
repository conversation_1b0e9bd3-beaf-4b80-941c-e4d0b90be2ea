<?php

namespace App\Models;

use Dcat\Admin\Traits\ModelTree;
use Spatie\EloquentSortable\Sortable;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewsCategory extends Model implements Sortable
{
	use HasDateTimeFormatter, ModelTree;
    use SoftDeletes;
    protected $table = 'news_categories';

    protected $fillable = [
        'parent_id',
        'title',
        'sort_order',
        'status',
        'deleted_at',
    ];

    // 排序
    protected $orderColumn = 'sort_order';
    protected $sortable = [
        // 设置排序字段名称
        'order_column_name' => 'sort_order',
        // 是否在创建时自动排序，此参数建议设置为true
        'sort_when_creating' => true,
    ];

    protected $titleColumn = 'title';
    protected $parentColumn = 'parent_id';

    public function parent()
    {
        return $this->belongsTo(NewsCategory::class, 'parent_id');
    }
}
