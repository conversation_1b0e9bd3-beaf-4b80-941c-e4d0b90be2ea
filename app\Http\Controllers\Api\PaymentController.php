<?php
namespace App\Http\Controllers\Api;

use App\Models\Order;
use App\Http\Controllers\Api\ApiController;
use App\Admin\Repositories\Order as OrderRepo;

/**
 * 支付
 */
class PaymentController extends ApiController
{
    public function payByWechat($id)
    {
        $order = Order::with('user')->find($id);
        if(!$order){
            return $this->errorBadRequest('订单不存在');
        }
        if ($order->total_amount > 0) {
            if ($order->paid_at) {
                return $this->errorBadRequest('订单状态不正确');
            }

            $orders = [
                'body'         => '支付 ' . env('APP_NAME') . ' 的订单：' . $order->no,
                'out_trade_no' => $order->no,
                'total_fee'    => $order->total_amount * 100, // 总价 单位：分
                'trade_type'   => 'JSAPI',  // 必须为JSAPI
                'openid'       => $order->user->wx_openid
            ];
            // 发起请求
            $payment = \EasyWeChat::payment();
            $result = $payment->order->unify($orders);
            \Log::info("微信支付请求：", $result);
            $prepayId = null;
            if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
                $prepayId = $result['prepay_id']; // 这个很重要。有了这个才能调用支付。
            } else {
                return $this->errorBadRequest('支付请求失败，请重试');
            }

            $jssdk = $payment->jssdk;
            $config = $jssdk->bridgeConfig($prepayId, false); // 返回数组
            \Log::info("微信支付请求配置：", $config);
            if ($config) {
                return $this->success($config);
            } else {
                return $this->errorBadRequest('支付设置错误，请重试');
            }
        } else {
            // 0元课程不支付
            return $this->success(['nopay' => 1]);
        }
    }

    /**
     * 微信支付回调
     */
    public function wechatNotify()
    {
        $payment = \EasyWeChat::payment(); // 微信支付
        $response = $payment->handlePaidNotify(function ($message, $fail) {
            \Log::info("微信支付回执：", $message);
            // 查询订单
            $order = Order::with('user')->where('no', $message['out_trade_no'])->first();

            if (!$order || $order->paid_at) { // 如果订单不存在 或者 订单已经支付过了
                return true; // 告诉微信，我已经处理完了，订单没找到，别再通知我了
            }

            ///////////// <- 建议在这里调用微信的【订单查询】接口查一下该笔订单的情况，确认是已经支付 /////////////
            if ($message['return_code'] === 'SUCCESS') { // return_code 表示通信状态，不代表支付状态
                // 用户是否支付成功
                if ($message['result_code'] === 'SUCCESS') {
                    $fields = [
                        'paid_at'        => $message['time_end'],
                        'transaction_id' => $message['transaction_id'],
                        'status'         => OrderRepo::STATUS_PAID
                    ];
                    $order->update($fields);
                } elseif ($message['result_code'] === 'FAIL') {
                    // 用户支付失败
                }
            } else {
                return $fail('通信失败');
            }

            return true; // 返回处理完成
        });

        $response->send();
    }
}
