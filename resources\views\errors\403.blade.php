@extends('layouts.web')

@section('title', '访问被拒绝')

@section('content')
<div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
  <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">403</h1>
      <h2 class="text-xl font-semibold text-gray-700 mb-4">访问被拒绝</h2>
      <div class="text-gray-600 mb-6">
        <p>抱歉，您没有权限访问此页面。</p>
        <p>如需访问，请联系管理员获取相应权限。</p>
      </div>

      <div class="py-5 text-center">
        <svg style="width: 4rem; height: 4rem;" class="d-inline-block text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4 mt-6">
        <a href="{{ route('home') }}" class="btn btn-primary px-4 py-2 transition">
          返回首页
        </a>
        <button onclick="history.back()" class="btn btn-outline-primary px-4 py-2 transition">
          返回上页
        </button>
      </div>
    </div>
  </div>
</div>
@endsection
