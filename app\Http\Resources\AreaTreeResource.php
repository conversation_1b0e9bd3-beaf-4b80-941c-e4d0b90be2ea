<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AreaTreeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'       => $this->id,
            'pid'      => $this->pid,
            'name'     => $this->name,
            'cities'   => self::collection($this->whenLoaded('cities')),
            'counties' => self::collection($this->whenLoaded('counties')),
        ];
    }
}
