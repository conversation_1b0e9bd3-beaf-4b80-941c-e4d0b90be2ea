<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Box;
use App\Admin\Repositories\Common;
use App\Admin\Repositories\SmsLog;
use App\Models\SmsLog as SmsLogModel;
use Dcat\Admin\Http\Controllers\AdminController;

class SmsLogController extends AdminController
{
    /**
     * Index interface.
     *
     * @param Content $content
     *
     * @return Content
     */
    public function index(Content $content)
    {
        return $content
            ->title('短信发送日志')
            ->description('查看所有短信发送记录和统计信息')
            ->body($this->statisticsCards())
            ->body($this->grid());
    }

    /**
     * 统计卡片
     */
    protected function statisticsCards()
    {
        // 今日统计
        $todayTotal = SmsLogModel::whereDate('created_at', today())->count();
        $todaySuccess = SmsLogModel::whereDate('created_at', today())->where('is_sent', 1)->count();
        $todayFailed = $todayTotal - $todaySuccess;
        $todaySuccessRate = $todayTotal > 0 ? round(($todaySuccess / $todayTotal) * 100, 2) : 0;

        // 本月统计
        $monthTotal = SmsLogModel::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)->count();
        $monthSuccess = SmsLogModel::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->where('is_sent', 1)->count();
        $monthFailed = $monthTotal - $monthSuccess;
        $monthSuccessRate = $monthTotal > 0 ? round(($monthSuccess / $monthTotal) * 100, 2) : 0;

        // 总计统计
        $totalCount = SmsLogModel::count();
        $totalSuccess = SmsLogModel::where('is_sent', 1)->count();
        $totalFailed = $totalCount - $totalSuccess;
        $totalSuccessRate = $totalCount > 0 ? round(($totalSuccess / $totalCount) * 100, 2) : 0;

        return new Box('短信发送统计', view('admin.sms-log.statistics', compact(
            'todayTotal', 'todaySuccess', 'todayFailed', 'todaySuccessRate',
            'monthTotal', 'monthSuccess', 'monthFailed', 'monthSuccessRate',
            'totalCount', 'totalSuccess', 'totalFailed', 'totalSuccessRate'
        )));
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SmsLog(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->model()->orderBy('id', 'desc');

            // 手机号列，点击可跳转到用户管理页面
            $grid->column('mobile', '手机号')->link(function ($mobile) {
                return admin_url('users?mobile=' . $mobile);
            })->help('点击可查看该手机号对应的用户');

            // 短信内容/数据
            $grid->column('data', '短信内容')->display(function ($data) {
                $decoded = json_decode($data, true);
                if (isset($decoded['content'])) {
                    return $decoded['content'];
                } elseif (isset($decoded['template'])) {
                    return '模板: ' . $decoded['template'];
                } else {
                    return '查看详情';
                }
            })->limit(30, '...')->modal('短信数据详情', function () {
                return '<pre>' . json_encode(json_decode($this->data, true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            });

            // 发送状态
            $grid->column('is_sent', '发送状态')->using([
                0 => '发送失败',
                1 => '发送成功'
            ])->badge([
                0 => 'danger',
                1 => 'success'
            ]);

            // 发送结果
            $grid->column('result', '发送结果')->display(function ($result) {
                if (empty($result)) {
                    return '-';
                }
                $decoded = json_decode($result, true);
                if (isset($decoded['message'])) {
                    return $decoded['message'];
                } elseif (isset($decoded['error'])) {
                    return $decoded['error'];
                } else {
                    return '查看详情';
                }
            })->limit(20, '...')->modal('发送结果详情', function () {
                if (empty($this->result)) {
                    return '无结果数据';
                }
                return '<pre>' . json_encode(json_decode($this->result, true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            });

            // 发送时间
            $grid->column('created_at', '发送时间')->sortable();

            // 搜索功能
            $grid->quickSearch('mobile')
                ->placeholder('输入手机号搜索')
                ->auto(false);

            // 筛选器
            $grid->filter(function ($filter) {
                $filter->disableIdFilter();

                $filter->like('mobile', '手机号');

                $filter->equal('is_sent', '发送状态')->select([
                    '' => '全部',
                    1 => '发送成功',
                    0 => '发送失败'
                ]);

                $filter->between('created_at', '发送时间')->datetime();
            });

            // 工具栏
            $grid->tools(function ($tools) {
                $tools->append('<div class="btn-group">
                    <button type="button" class="btn btn-sm btn-info" onclick="location.reload()">
                        <i class="fa fa-refresh"></i> 刷新
                    </button>
                </div>');
            });

            // 导出功能
            $grid->export()->rows(function ($rows) {
                foreach ($rows as $index => &$row) {
                    // 解析短信内容
                    $data = json_decode($row['data'], true);
                    if (isset($data['content'])) {
                        $row['data'] = $data['content'];
                    } elseif (isset($data['template'])) {
                        $row['data'] = '模板: ' . $data['template'];
                    }

                    // 解析发送结果
                    if (!empty($row['result'])) {
                        $result = json_decode($row['result'], true);
                        if (isset($result['message'])) {
                            $row['result'] = $result['message'];
                        } elseif (isset($result['error'])) {
                            $row['result'] = $result['error'];
                        }
                    } else {
                        $row['result'] = '无结果';
                    }

                    // 转换发送状态
                    $row['is_sent'] = $row['is_sent'] ? '发送成功' : '发送失败';
                }
                return $rows;
            })->filename('短信发送日志_' . date('Y-m-d_H-i-s'));

            $grid->setActionClass(Grid\Displayers\Actions::class);
            // 禁止新增
            $grid->disableCreateButton();
            // 禁止编辑
            $grid->disableEditButton();
            // 禁止删除
            $grid->disableDeleteButton();

            // 设置每页显示数量
            $grid->paginate(20);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SmsLog(), function (Show $show) {
            $show->field('id', 'ID');

            $show->field('mobile', '手机号')->link(function ($mobile) {
                return admin_url('users?mobile=' . $mobile);
            }, '查看用户');

            $show->field('data', '短信数据')->as(function ($data) {
                return '<pre>' . json_encode(json_decode($data, true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            })->unescape();

            $show->field('is_sent', '发送状态')->using([
                0 => '发送失败',
                1 => '发送成功'
            ])->badge([
                0 => 'danger',
                1 => 'success'
            ]);

            $show->field('result', '发送结果')->as(function ($result) {
                if (empty($result)) {
                    return '无结果数据';
                }
                return '<pre>' . json_encode(json_decode($result, true), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
            })->unescape();

            $show->field('created_at', '发送时间');
            $show->field('updated_at', '更新时间');

            // 禁用编辑和删除按钮
            $show->disableEditButton();
            $show->disableDeleteButton();
        });
    }

}
