<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'     => $this->id,
            'pid'    => $this->pid,
            'name'   => $this->name,
            'pinyin' => $this->pinyin,
            'lat'    => $this->lat,
            'lng'    => $this->lng,
        ];
    }
}
