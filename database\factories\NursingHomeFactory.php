<?php

namespace Database\Factories;

use App\Models\NursingHome;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NursingHome>
 */
class NursingHomeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = NursingHome::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $provinces = ['北京市', '上海市', '广东省', '江苏省', '浙江省', '山东省', '河南省', '四川省', '湖北省', '湖南省'];
        $cities = ['北京市', '上海市', '广州市', '深圳市', '南京市', '杭州市', '济南市', '郑州市', '武汉市', '长沙市'];
        $districts = ['朝阳区', '海淀区', '西城区', '东城区', '丰台区', '石景山区', '通州区', '昌平区', '大兴区', '顺义区'];

        return [
            'name' => $this->faker->company() . '养老院',
            'avatar_url' => $this->faker->imageUrl(600, 600, 'business'),
            'province' => $this->faker->randomElement($provinces),
            'city' => $this->faker->randomElement($cities),
            'district' => $this->faker->randomElement($districts),
            'address' => $this->faker->streetAddress(),
            'longitude' => $this->faker->longitude(116.0, 117.0), // 北京经度范围
            'latitude' => $this->faker->latitude(39.5, 40.5), // 北京纬度范围
            'mobile' => $this->faker->phoneNumber(),
            'description' => $this->faker->paragraph(3),
            'image_urls' => [
                $this->faker->imageUrl(635, 400, 'business'),
                $this->faker->imageUrl(635, 400, 'business'),
                $this->faker->imageUrl(635, 400, 'business'),
            ],
            'price_image_urls' => [
                $this->faker->imageUrl(635, 400, 'business'),
                $this->faker->imageUrl(635, 400, 'business'),
            ],
            'transportation_image_urls' => [
                $this->faker->imageUrl(635, 400, 'transport'),
            ],
            'honor_image_urls' => [
                $this->faker->imageUrl(635, 400, 'abstract'),
            ],
            'other_image_urls' => [
                $this->faker->imageUrl(635, 400, 'nature'),
            ],
            'upload_video_url' => 'https://example.com/nursing-home-video.mp4',
            'video_url' => 'https://example.com/nursing-home-video.mp4',
            'video_thumb_url' => $this->faker->imageUrl(640, 480, 'business'),
            'status' => 1, // 默认启用
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * 表示养老院为禁用状态
     */
    public function disabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * 表示养老院为启用状态
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1,
        ]);
    }

    /**
     * 指定特定的地理位置
     */
    public function inBeijing(): static
    {
        return $this->state(fn (array $attributes) => [
            'province' => '北京市',
            'city' => '北京市',
            'district' => $this->faker->randomElement(['朝阳区', '海淀区', '西城区', '东城区', '丰台区']),
            'longitude' => $this->faker->longitude(116.0, 117.0),
            'latitude' => $this->faker->latitude(39.5, 40.5),
        ]);
    }

    /**
     * 指定特定的地理位置
     */
    public function inShanghai(): static
    {
        return $this->state(fn (array $attributes) => [
            'province' => '上海市',
            'city' => '上海市',
            'district' => $this->faker->randomElement(['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区']),
            'longitude' => $this->faker->longitude(121.0, 122.0),
            'latitude' => $this->faker->latitude(31.0, 32.0),
        ]);
    }
}
