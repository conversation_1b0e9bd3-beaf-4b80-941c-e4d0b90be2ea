<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 媒体处理 MPS 转码任务查询
        $schedule->command('cron:mps-listjob')->everyMinute();
        // 媒体处理 MPS 截图任务查询
        $schedule->command('cron:mps-snaplistjob')->everyMinute();
        // 机构媒体处理 MPS 转码任务查询
        $schedule->command('cron:nursing-home-mps-listjob')->everyMinute();
        // 机构媒体处理 MPS 截图任务查询
        $schedule->command('cron:nursing-home-mps-snaplistjob')->everyMinute();

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
