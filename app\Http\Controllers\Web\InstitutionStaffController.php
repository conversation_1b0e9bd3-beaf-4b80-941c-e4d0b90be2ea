<?php

namespace App\Http\Controllers\Web;

use App\Models\User;
use App\Models\NursingHome;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Overtrue\LaravelWeChat\EasyWeChat;
use App\Models\InstitutionUserPermission;
use App\Http\Controllers\Web\WebController;

class InstitutionStaffController extends WebController
{
    /**
     * 员工管理列表页面
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取当前机构
        $nursingHome = NursingHome::find($nursing_home_id);

        if (!$nursingHome) {
            return redirect()->route('home')->with(['message' => '机构信息不存在。']);
        }

        // 获取与机构关联的员工及其权限
        $staffMembers = InstitutionUserPermission::with(['user'])
            ->where('nursing_home_id', $nursing_home_id)
            ->paginate(20);

        return view('nursing-home.staff.staff', compact('staffMembers', 'nursingHome'));
    }

    /**
     * 邀请员工页面
     */
    public function invite(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取当前机构
        $nursingHome = NursingHome::find($nursing_home_id);

        if (!$nursingHome) {
            return redirect()->route('home')->with(['message' => '机构信息不存在。']);
        }

        // 生成新的二维码（每次打开页面都重新生成）
        $qrCodeUrl = $this->generateQrCode($user);

        // 传递当前用户信息和二维码URL到视图
        $currentUser = $user;

        return view('nursing-home.staff.staff-invite', compact('nursingHome', 'currentUser', 'qrCodeUrl'));
    }

    /**
     * 生成员工邀请二维码
     */
    private function generateQrCode($user)
    {
        $id = $user->id;
        $page = 'pages/institution/invite';
        $timestamp = time();
        $scene = 'n_id=' . $user->manage_nursing_home_id . '&ts=' . $timestamp;

        $app = EasyWeChat::miniApp();
        try {
            // 获取不限制的小程序码
            $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                'scene' => $scene,
                'page'  => $page,
                'width' => 800,
                'check_path' => config('app.env') == 'production', // 默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用
                'env_version' => config('app.env') == 'production' ? 'release' : 'trial', // 判断环境 production 生成正式版
            ]);
            $name = $id . now()->format('YmdHis') . Str::random(5) . '.png';
            // 判断是否有文件夹，没有则创建
            if (!file_exists(storage_path('app/public/xcxcode'))) {
                mkdir(storage_path('app/public/xcxcode'), 0777, true);
            }
            $response->saveAs(storage_path('app/public/xcxcode/' . $name));

            return getImageUrl('/xcxcode/' . $name);
        } catch (\Throwable $e) {
            Log::error('生成二维码失败: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 编辑员工权限页面
     */
    public function edit(Request $request, $permissionId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取权限记录
        $permission = InstitutionUserPermission::with(['user', 'nursingHome'])
            ->where('id', $permissionId)
            ->where('nursing_home_id', $nursing_home_id)
            ->first();

        if (!$permission) {
            return redirect()->route('institution.staff.index')->with(['error' => '权限记录不存在。']);
        }

        return view('nursing-home.staff.staff-edit', compact('permission'));
    }

    /**
     * 更新员工权限
     */
    public function update(Request $request, $permissionId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取权限记录
        $permission = InstitutionUserPermission::where('id', $permissionId)
            ->where('nursing_home_id', $nursing_home_id)
            ->first();

        if (!$permission) {
            return redirect()->route('institution.staff.index')->with(['error' => '权限记录不存在。']);
        }

        $request->validate([
            'can_manage_staff'   => 'boolean',
            'can_manage_info'    => 'boolean',
            'can_manage_videos'  => 'boolean',
            'can_view_data'      => 'boolean',
            'can_manage_finance' => 'boolean',
        ]);

        // 更新权限 - 处理复选框未选中时的情况
        $permission->update([
            'can_manage_staff'   => $request->boolean('can_manage_staff'),
            'can_manage_info'    => $request->boolean('can_manage_info'),
            'can_manage_videos'  => $request->boolean('can_manage_videos'),
            'can_view_data'      => $request->boolean('can_view_data'),
            'can_manage_finance' => $request->boolean('can_manage_finance'),
        ]);

        return redirect()->route('institution.staff.index')->with(['message' => '权限更新成功。']);
    }

    /**
     * 移除员工
     */
    public function remove(Request $request, $permissionId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取权限记录
        $permission = InstitutionUserPermission::where('id', $permissionId)
            ->where('nursing_home_id', $nursing_home_id)
            ->first();

        if (!$permission) {
            return redirect()->route('institution.staff.index')->with(['error' => '权限记录不存在。']);
        }

        // 删除权限记录
        $permission->delete();

        return redirect()->route('institution.staff.index')->with(['message' => '员工移除成功。']);
    }
}
