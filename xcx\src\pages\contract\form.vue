<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />
    <bind-phone-modal />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="true"></u-loading-page>
    </view>
    <view class="content" v-else>
      <view class="u-text-center">
        <image
          src="/static/images/cover.png"
          class="cover-image"
          mode="aspectFill"
        ></image>
        <view class="form-title u-m-b-20 u-m-t-35">开通机构账号</view>
        <view class="u-font-16 u-line-height3 fc-666 u-px-80"
          >我是养老机构高管，我们要用老邻日记，做好我们的私域营销。</view
        >
      </view>
      <view class="u-demo-block u-p-30">
        <view class="u-demo-block__content">
          <u-form :model="info" ref="infoForm" :rules="rules">
            <view class="global-form-wrap">
              <view class="u-p-t-20 u-p-b-20 border-bottom">
                <u-form-item
                  label="机构名称"
                  prop="nursing_home_name"
                  label-width="140"
                  :required="true"
                >
                  <u-input
                    inputAlign="right"
                    v-model="info.nursing_home_name"
                    border="none"
                    :clearable="false"
                    placeholder="请填写机构名称"
                  />
                </u-form-item>
              </view>
              <view class="u-p-t-20 u-p-b-20">
                <u-form-item
                  label="手机号码"
                  prop="mobile"
                  label-width="140"
                  :required="true"
                >
                  <view class="input-wrapper">
                    <u-input
                      inputAlign="right"
                      v-model="info.mobile"
                      border="none"
                      :clearable="false"
                      placeholder="请点击授权手机号码"
                      :disabled="btnLoading"
                    />
                    <button
                      class="wechat-phone-btn"
                      open-type="getPhoneNumber"
                      @getphonenumber="handleGetPhone"
                    ></button>
                  </view>
                </u-form-item>
              </view>
            </view>
          </u-form>
        </view>
      </view>
      <u-button
        color="#F2AE1E"
        text="提 交"
        shape="circle"
        @click="submit"
        :loading="btnLoading"
        :customStyle="{ marginTop: '30rpx' }"
      ></u-button>

      <view class="u-font-13 u-text-center u-line-height3 fc-999 u-p-t-40 u-px-40"
        >*开通机构账号是很重要的一步，为了安全，我们需要获得你的手机号码。在全面使用机构账号前，我们将需要更多的企业信息验证。</view>
    </view>
  </view>
</template>

<script>
import api from "@/common/api";
export default {
  data() {
    return {
      loading: true,
      btnLoading: false,
      info: {
        nursing_home_name: "",
        mobile: "",
      },
      rules: {
        nursing_home_name: [
          {
            required: true,
            message: "请输入机构名称",
            trigger: ["blur", "change"],
          },
        ],
        mobile: [
          {
            required: true,
            message: "请点击授权手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  async onLoad(options) {
    this.id = options.id;
    // 检查是否已绑定手机号
    this.checkMobileBound();
    // 加载已存在的签约数据
    await this.loadExistingContract();
  },
  onReady() {
    this.$nextTick(() => {
      if (this.$refs.infoForm && this.$refs.infoForm.setRules) {
        this.$refs.infoForm.setRules(this.rules);
      }
    });
  },
  methods: {
    // 检查是否已绑定手机号
    checkMobileBound() {
      const user = uni.getStorageSync("user");
      if (user && user.mobile) {
        this.info.mobile = user.mobile;
        return true;
      }
      return false;
    },

    async loadExistingContract() {
      this.loading = true;
      try {
        const res = await api.getContracts();
        if (res.data.status === "success" && res.data.data) {
          const contract = res.data.data;

          // 如果已签约，直接跳转到成功页
          if (contract.status === 1) {
            uni.redirectTo({
              url: "/pages/contract/success",
            });
            return;
          } else {
            // 如果有数据，自动填入
            if (contract.nursing_home_name) {
              this.info.nursing_home_name = contract.nursing_home_name;
            }
            if (contract.mobile) {
              this.info.mobile = contract.mobile;
            }
            this.loading = false;
          }
        }
      } catch (error) {
        console.error("加载签约信息失败:", error);
        this.loading = false;
      }
    },

    async handleGetPhone(e) {
      console.log("获取手机号事件:", e.detail);
      this.btnLoading = true;

      try {
        if (e.detail.errMsg !== "getPhoneNumber:ok") {
          throw new Error("用户拒绝授权");
        }

        const { code } = e.detail;
        console.log("手机号code:", code);

        // 获取本地存储的用户信息
        const user = uni.getStorageSync("user") || {};

        if (!user || !user.xcx_openid) {
          throw new Error("登录状态失效，请重新登录");
        }

        // 调用绑定手机号API（使用code方式）
        const res = await api.bindMobileByOpenid({
          data: {
            code: code,
            openid: user.xcx_openid,
          },
          method: "POST",
        });

        this.btnLoading = false;

        if (res.data.status === "success") {
          if (res.data.data && res.data.data.user) {
            const userData = res.data.data;

            // 更新手机号显示
            if (userData.user.mobile) {
              this.info.mobile = userData.user.mobile;
            }

            // 更新用户信息
            uni.setStorageSync("user", userData.user);

            // 更新token
            if (userData.token) {
              uni.setStorageSync("token", userData.token);
            }

            // 触发自定义事件
            this.$emit("mobile-bound", userData.user.mobile);
          } else {
            throw new Error("手机号获取失败");
          }
        } else {
          throw new Error(res.data.message || "绑定失败");
        }
      } catch (error) {
        this.btnLoading = false;
        console.error("获取手机号失败:", error);

        // 显示错误提示
        if (error.message.includes("用户拒绝授权")) {
          uni.showToast({
            title: "需要授权才能获取手机号",
            icon: "none",
            duration: 2000,
          });
        } else {
          this.$refs.uToast.show({
            message: error.message || "手机号绑定失败",
            duration: 3000,
          });
        }
      }
    },

    async postRegister() {
      try {
        const formData = {
          mobile: this.info.mobile,
          nursing_home_name: this.info.nursing_home_name,
        };

        const res = await api.saveContractStep1(formData);

        if (res.data.status === "success") {
          uni.showToast({
            title: "提交成功",
            icon: "success",
            duration: 2000,
          });

          // 跳转到签约第二步
          setTimeout(() => {
            uni.navigateTo({
              url: "/pages/contract/sign",
            });
          }, 1500);
        } else {
          throw new Error(res.data.message || "提交失败");
        }
      } catch (error) {
        console.error("提交失败:", error);

        // 显示错误信息
        if (error.response && error.response.data) {
          const errorMsg = error.response.data.message || "提交失败，请重试";
          this.$refs.uToast.show({
            message: errorMsg,
            duration: 3000,
          });
        } else {
          this.$refs.uToast.show({
            message: error.message || "网络错误，请重试",
            duration: 3000,
          });
        }
      } finally {
        this.btnLoading = false;
      }
    },
    submit() {
      this.btnLoading = true;
      this.$refs.infoForm
        .validate()
        .then((res) => {
          console.log("提交数据:", this.info);
          this.postRegister();
        })
        .catch((errors) => {
          uni.$u.toast("请完善表单信息");
          this.btnLoading = false;
        });
    },
  },
};
</script>

<style lang="scss">
.content {
  padding: 30upx;
  min-height: 100vh;
}

.border-bottom {
  border-bottom: 1rpx solid #eaeaea;
}

.cover-image {
  width: 400upx;
  height: 400upx;
}

.form-title {
  font-size: 60upx;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.wechat-phone-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0; // 完全透明
  z-index: 2; // 确保盖在输入框上
  background-color: transparent;
}
.input-wrapper .u-input {
  background-color: transparent !important;
}
</style>
