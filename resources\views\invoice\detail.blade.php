@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/invoice/index',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">发票详情</h5>
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <table class="table table-bordered">
                  <tbody>
                    <tr>
                      <td><strong>发票编号:</strong></td>
                      <td>{{ $invoice->no }}</td>
                    </tr>
                    <tr>
                      <td><strong>关联订单号:</strong></td>
                      <td>{{ $invoice->payment->no }}</td>
                    </tr>
                    <tr>
                      <td><strong>发票抬头:</strong></td>
                      <td>{{ $invoice->invoice_title }}</td>
                    </tr>
                    <tr>
                      <td><strong>税号:</strong></td>
                      <td>{{ $invoice->tax_number ?? '无' }}</td>
                    </tr>
                    <tr>
                      <td><strong>金额:</strong></td>
                      <td>￥{{ number_format($invoice->amount, 2) }}</td>
                    </tr>
                    <tr>
                      <td><strong>状态:</strong></td>
                      <td>
                        @if($invoice->status == \App\Admin\Repositories\Invoice::STATUS_PENDING)
                        <span class="badge badge-warning">待审核</span>
                        @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_APPROVED)
                        <span class="badge badge-info">已审核</span>
                        @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_ISSUED)
                        <span class="badge badge-success">已开票</span>
                        @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_REJECTED)
                        <span class="badge badge-danger">已拒绝</span>
                        @endif
                      </td>
                    </tr>
                    <tr>
                      <td><strong>申请时间:</strong></td>
                      <td>{{ $invoice->created_at->format('Y-m-d H:i:s') }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div class="col-md-6">
                <table class="table table-bordered">
                  <tbody>
                    <tr>
                      <td><strong>地址:</strong></td>
                      <td>{{ $invoice->address ?? '无' }}</td>
                    </tr>
                    <tr>
                      <td><strong>电话:</strong></td>
                      <td>{{ $invoice->phone ?? '无' }}</td>
                    </tr>
                    <tr>
                      <td><strong>开户行:</strong></td>
                      <td>{{ $invoice->bank_name ?? '无' }}</td>
                    </tr>
                    <tr>
                      <td><strong>银行账户:</strong></td>
                      <td>{{ $invoice->bank_account ?? '无' }}</td>
                    </tr>
                    <tr>
                      <td><strong>备注:</strong></td>
                      <td>{{ $invoice->remark ?? '无' }}</td>
                    </tr>
                    @if($invoice->invoice_file)
                    <tr>
                      <td><strong>发票文件:</strong></td>
                      <td><a href="{{ $invoice->invoice_file }}" target="_blank" class="btn btn-success btn-sm">下载发票</a></td>
                    </tr>
                    @endif
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="card-footer">
            <a href="{{ route('invoice.index') }}" class="btn btn-secondary">返回列表</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
