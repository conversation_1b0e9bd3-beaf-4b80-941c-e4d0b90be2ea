<?php

namespace Database\Factories;

use App\Models\UserShareRecord;
use App\Models\User;
use App\Models\Diary;
use App\Models\NursingHome;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserShareRecord>
 */
class UserShareRecordFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserShareRecord::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'from_user_id' => User::factory(),
            'diary_id' => Diary::factory(),
            'nursing_home_id' => NursingHome::factory(),
            'to_user_id' => null, // 默认为空，表示还没有人点击
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * 表示分享被点击的状态
     */
    public function clicked(): static
    {
        return $this->state(fn (array $attributes) => [
            'to_user_id' => User::factory(),
        ]);
    }

    /**
     * 指定特定的转发用户
     */
    public function fromUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'from_user_id' => $user->id,
        ]);
    }

    /**
     * 指定特定的日记
     */
    public function forDiary(Diary $diary): static
    {
        return $this->state(fn (array $attributes) => [
            'diary_id' => $diary->id,
            'nursing_home_id' => $diary->nursing_home_id,
        ]);
    }

    /**
     * 指定特定的养老院
     */
    public function forNursingHome(NursingHome $nursingHome): static
    {
        return $this->state(fn (array $attributes) => [
            'nursing_home_id' => $nursingHome->id,
        ]);
    }

    /**
     * 指定创建时间为今天
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => now(),
        ]);
    }

    /**
     * 指定创建时间为本月
     */
    public function thisMonth(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween(now()->startOfMonth(), now()),
        ]);
    }
}
