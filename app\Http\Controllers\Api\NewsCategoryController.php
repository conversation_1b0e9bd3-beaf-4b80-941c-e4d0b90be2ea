<?php

namespace App\Http\Controllers\Api;

use App\Models\NewsCategory;
use Illuminate\Http\Request;
use App\Http\Resources\NewsCategoryResource;

class NewsCategoryController extends ApiController
{
    /**
     * 列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 20;
        $result = NewsCategory::where('status', 1)
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
        return $this->success(NewsCategoryResource::collection($result));
    }

    /**
     * 详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $result = NewsCategory::find($id);
        if ($result) {
            return $this->success(new NewsCategoryResource($result));
        }
        $this->errorBadRequest('暂无数据');
    }
}
