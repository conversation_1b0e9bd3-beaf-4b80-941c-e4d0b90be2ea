@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/payment/index',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">付款记录</h5>
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>订单号</th>
                  <th>支付方式</th>
                  <th>总金额</th>
                  <th>支付时间</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                @forelse($payments as $payment)
                <tr>
                  <td>{{ $payment->id }}</td>
                  <td>{{ $payment->no }}</td>
                  <td>{{ \App\Admin\Repositories\Payment::$payMethodMap[$payment->pay_method] ?? '-' }}</td>
                  <td>￥{{ number_format($payment->total_amount, 2) }}</td>
                  <td>{{ $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : '-' }}</td>
                  <td>
                    @if($payment->status == \App\Admin\Repositories\Payment::STATUS_PENDING)
                    <span class="badge text-bg-secondary">待支付</span>
                    @elseif($payment->status == \App\Admin\Repositories\Payment::STATUS_PAID)
                    <span class="badge text-bg-success">已支付</span>
                    @elseif($payment->status == \App\Admin\Repositories\Payment::STATUS_CLOSED)
                    <span class="badge text-bg-warning">已关闭</span>
                    @endif
                  </td>
                  <td>
                    @if($payment->status == \App\Admin\Repositories\Payment::STATUS_PAID && !$payment->invoice)
                    <a href="{{ route('invoice.create') }}?payment_id={{ $payment->id }}" class="btn btn-primary btn-sm">申请发票</a>
                    @elseif($payment->invoice)
                    <a href="{{ route('invoice.detail', $payment->invoice->id) }}" class="btn btn-info btn-sm">查看发票</a>
                    @else
                    <span>-</span>
                    @endif
                  </td>
                </tr>
                @empty
                <tr>
                  <td colspan="8" class="text-center">暂无付款记录</td>
                </tr>
                @endforelse
              </tbody>
            </table>

            <div class="mt-3">
              {{ $payments->links() }}
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
@endsection
