<?php

namespace App\Admin\Repositories;

use Dcat\Admin\Repositories\EloquentRepository;

class Common extends EloquentRepository
{

    /**
     * 设定布尔型常量
     */
    const STATUS_FALSE = 0;
    const STATUS_TRUE = 1;

    /**
     * 状态颜色集合
     */
    public static $statusColor = [
        self::STATUS_FALSE => 'danger',
        self::STATUS_TRUE => 'success'
    ];

    /**
     * 激活状态集合
     */
    public static $activeMap = [
        self::STATUS_FALSE => '禁用',
        self::STATUS_TRUE => '激活',
    ];

    /**
     * 是否状态集合
     */
    public static $statusMap = [
        self::STATUS_FALSE => '禁用',
        self::STATUS_TRUE => '启用',
    ];

    /**
     * 是否状态集合
     */
    public static $yesNoMap = [
        self::STATUS_FALSE => '否',
        self::STATUS_TRUE => '是',
    ];

    /**
     * 是否显示
     */
    public static $visibleMap = [
        self::STATUS_FALSE  => '隐藏',
        self::STATUS_TRUE => '显示',
    ];

    /**
     * 一周的星期
     */
    public static $weekMap = [
        1 => '一',
        2 => '二',
        3 => '三',
        4 => '四',
        5 => '五',
        6 => '六',
        0 => '日',
    ];

    /**
     * 周一-周日
     */
    public static $weekFullMap = [
        1 => '周一',
        2 => '周二',
        3 => '周三',
        4 => '周四',
        5 => '周五',
        6 => '周六',
        0 => '周日',
    ];
}
