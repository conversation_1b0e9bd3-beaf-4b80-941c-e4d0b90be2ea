<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\NursingHome;
use Illuminate\Support\Str;
use Dcat\Admin\Widgets\Metrics\Card;
use App\Admin\Repositories\NursingHomeContact;
use Dcat\Admin\Http\Controllers\AdminController;

class NursingHomeContactController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(NursingHomeContact::with(['nursingHome']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('reason');
            $grid->column('name');
            $grid->column('phone');
            $grid->column('message')->display(function ($message) {
                return Str::limit($message, 20);
            })->expand(function () {
                return new Card(nl2br($this->message));
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name')->width(3);
                $filter->like('phone')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NursingHomeContact(), function (Show $show) {
            $show->field('id');
            $show->field('reason');
            $show->field('name');
            $show->field('phone');
            $show->field('message');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(NursingHomeContact::with(['nursingHome']), function (Form $form) {
            $form->select('nursing_home_id')->options(NursingHome::all()->pluck('name', 'id'))->required();
            $form->text('reason');
            $form->text('name');
            $form->text('phone');
            $form->textarea('message');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
