<template>
  <view v-if="showPrivacy" :class="privacyClass">
    <view :class="contentClass">
      <view class="title">隐私保护指引</view>
      <view class="des">
        感谢选择我们的小程序，我们非常重视您的个人信息安全和隐私保护。根据最新法律要求，使用我们的产品前，请仔细阅读“
        <text class="link" @click="goToPrivacyPolicy">
          老邻日记小程序隐私保护指引 </text
        >”，以便我们向您提供更优质的服务！<br />我们将尽全力保护您的个人信息及合法权益，感谢您的信任！
        <br />
      </view>
      <view class="btns">
        <button class="item reject" @tap="exitMiniProgram">拒绝</button>
        <button
          id="agree-btn"
          class="item agree"
          @tap="handleAgreePrivacyAuthorization"
        >
          同意
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "PrivacyPopupModal",
  data() {
    return {
      privacyContractName: "",
      showPrivacy: false,
      isRead: false,
      resolvePrivacyAuthorization: null,
    };
  },
  props: {
    position: {
      type: String,
      default: "center",
    },
  },
  computed: {
    privacyClass() {
      return this.position === "bottom" ? "privacy privacy-bottom" : "privacy";
    },
    contentClass() {
      return this.position === "bottom" ? "content content-bottom" : "content";
    },
  },
  mounted() {
    if (wx.onNeedPrivacyAuthorization) {
      wx.onNeedPrivacyAuthorization((resolve) => {
        this.resolvePrivacyAuthorization = resolve;
      });
    }
  },

  methods: {
    // 检查隐私政策同意状态
    checkPrivacyAgreementStatus() {
      const is_new = uni.getStorageSync("is_new");
      console.log("检查隐私政策状态 - is_new:", is_new);

      // 只有新用户才显示隐私弹窗
      if (is_new === 1) {
        this.showPrivacy = true;
      } else {
        this.showPrivacy = false;
      }
    },
    goToPrivacyPolicy() {
      uni.navigateTo({
        url: "/pages/page/index?permalink=privacy-policy",
      });
    },
    openPrivacyContract() {
      wx.openPrivacyContract({
        success: () => {
          this.isRead = true;
        },
        fail: () => {
          uni.showToast({
            title: "遇到错误",
            icon: "error",
          });
        },
      });
    },
    exitMiniProgram() {
      wx.exitMiniProgram();
    },
    handleAgreePrivacyAuthorization() {
      // 用户同意隐私政策，保存同意状态到本地缓存
      const agreementData = {
        agreed: true,
        timestamp: Date.now(), // 记录同意时间
      };
      uni.setStorageSync("isAgreedPrivacy", agreementData);
      console.log("用户同意隐私政策，保存到本地缓存:", agreementData);

      // 设置is_new为0，标记为非新用户
      uni.setStorageSync("is_new", 0);

      // 关闭弹窗
      this.showPrivacy = false;

      // 处理微信隐私授权回调（如果存在）
      if (typeof this.resolvePrivacyAuthorization === "function") {
        this.resolvePrivacyAuthorization({
          buttonId: "agree-btn",
          event: "agree",
        });
      }

      // 显示同意成功提示
      uni.showToast({
        title: "已同意隐私政策",
        icon: "success",
        duration: 1500,
      });
    },
  },
};
</script>

<style scoped>
.privacy {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privacy-bottom {
  align-items: flex-end;
}

.content {
  width: 632rpx;
  padding: 48rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  min-height: auto;
}

.content-bottom {
  position: absolute;
  bottom: 0;
  width: 96%;
  padding: 36rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 16rpx 16rpx 0 0;
}

.content .title {
  text-align: center;
  color: #333;
  font-weight: bold;
  font-size: 32rpx;
}

.content .des {
  font-size: 26rpx;
  color: #666;
  margin-top: 40rpx;
  text-align: justify;
  line-height: 1.6;
}

.content .des .link {
  color: #07c160;
  text-decoration: underline;
}

.btns {
  margin-top: 48rpx;
  margin-bottom: 12rpx;
  display: flex;
}

.btns .item {
  width: 200rpx;
  height: 72rpx;
  overflow: visible;
  display: flex;
  align-items: center;

  justify-content: center;
  /* border-radius: 16rpx; */
  box-sizing: border-box;
  border: none !important;
}

.btns .reject {
  background: #f4f4f5;
  color: #666;
  font-size: 14px;
  font-weight: 300;
  margin-right: 16rpx;
}

.btns .agree {
  width: 320rpx;
  background: #07c160;
  color: #fff;
  font-size: 16px;
}

.privacy-bottom .btns .agree {
  width: 440rpx;
}
</style>
