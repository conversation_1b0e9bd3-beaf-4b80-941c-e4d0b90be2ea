<?php

namespace App\Models;

use Dcat\Admin\Traits\ModelTree;
use Spatie\EloquentSortable\Sortable;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class Page extends Model implements Sortable
{
    use HasDateTimeFormatter, ModelTree;
    protected $table = 'pages';

    use SoftDeletes;
    protected $fillable = [
        'title',
        'seo_title',
        'seo_keywords',
        'seo_description',
        'banner_url',
        'parent_id',
        'sort_order',
        'permalink',
        'content',
        'status',
        'deleted_at'
    ];

    // 排序
    protected $orderColumn = 'sort_order';
    protected $sortable = [
        // 设置排序字段名称
        'order_column_name' => 'sort_order',
        // 是否在创建时自动排序，此参数建议设置为true
        'sort_when_creating' => true,
    ];

    protected $titleColumn = 'title';
    protected $parentColumn = 'parent_id';

    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function getFullBannerUrlAttribute()
    {
        return getImageUrl($this->banner_url);
    }

    public function getFullThumbUrlAttribute()
    {
        return getThumbUrl($this->banner_url, '1920', '300');
    }

    public function getFullPermalinkAttribute()
    {
        $parent = $this->parent;
        if ($parent) {
            $parent_permalink = $parent->getFullPermalinkAttribute();
            return $parent_permalink . '/' . $this->permalink;
        } else {
            return $this->permalink;
        }
    }
}
