<?php

namespace App\Services;

use Qcloud\Cos\Client;

class QcloudService
{
    /**
     * 腾讯数据万象文本审核
     */
    public function detectText($content)
    {

        // 本地环境
        if (config('app.env') == 'local') {
            return true;
        }

        if (empty($content)) {
            return false;
        }

        //替换为用户的 secretId，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
        $secretId = config("app.qcloud_secret_id");
        //替换为用户的 secretKey，请登录访问管理控制台进行查看和管理，https://console.cloud.tencent.com/cam/capi
        $secretKey = config("app.qcloud_secret_key");
        //替换为用户的 region，已创建桶归属的region可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
        $region = config("app.qcloud_region");

        $cosClient = new Client(array(
            'region' => $region,
            // 审核时必须为https
            'schema' => 'https',
            'credentials' => array(
                'secretId'  => $secretId,
                'secretKey' => $secretKey
            )
        ));

        try {
            $result = $cosClient->detectText(array(
                //存储桶名称，由BucketName-Appid 组成，可以在COS控制台查看 https://console.cloud.tencent.com/cos5/bucket
                'Bucket' => config("app.qcloud_bucket"),
                'Input' => array(
                    // 文本需base64_encode
                    'Content' => base64_encode($content),
                ),
            ));
            $jobsDetail = $result['JobsDetail'];

            // 检查敏感词分数，如果高于等于90，则禁止创建
            if (
                $jobsDetail['PornInfo']['Score'] >= 90
                || $jobsDetail['AdsInfo']['Score'] >= 90
                || $jobsDetail['IllegalInfo']['Score'] >= 90
                || $jobsDetail['AbuseInfo']['Score'] >= 90
                || $jobsDetail['PoliticsInfo']['Score'] >= 90
                || $jobsDetail['TerrorismInfo']['Score'] >= 90
            ) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            // 请求失败
            echo ($e);
            return false;
        }
    }
}
