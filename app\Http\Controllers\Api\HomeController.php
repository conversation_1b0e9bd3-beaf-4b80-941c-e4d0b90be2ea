<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Diary;
use App\Models\Comment;
use App\Models\UserLike;
use App\Models\UserFollow;
use App\Models\NursingHome;
use App\Models\UserNursingHome;
use Illuminate\Http\Request;
use App\Models\UserCollection;
use App\Models\UserBrowseRecord;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\DiaryResource;
use App\Http\Resources\NursingHomeResource;
use Illuminate\Support\Facades\Redis;
use App\Http\Resources\CommentResource;
use App\Http\Resources\DiaryListResource;
use App\Http\Resources\DiaryShortResource;
use Illuminate\Pagination\LengthAwarePaginator;

class HomeController extends ApiController
{

    /**
     * 公用的查询方法
     */
    private function getDiaries()
    {
        return Diary::published()
            ->with('nursingHome');
    }

    /**
     * 新首页列表(直接获取列表、默认为推荐)
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function homePlayList(Request $request)
    {
        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // type 1-关注 2-推荐 3-附近 4-我的
        $type = $request->type ?? 2;

        if ($type == 1) {
            return $this->followList($request);
        }

        // 推荐
        if ($type == 2) {
            return $this->recommendList($request);
        }

        // 我的养老院
        if ($type == 4) {
            return $this->myList($request);
        }

        // 分享
        if ($type == 7) {
            return $this->shareList($request);
        }

        // 护老好办法
        if ($type == 5) {
            return $this->ideaList($request);
        }

        // 机构主页（未绑定时，显示教学视频）
        if ($type == 6) {
            return $this->nursingHomeIndex($request);
        }

        $limit           = $request->limit ?? 20;
        $keyword         = $request->keyword;
        $longitude       = $request->longitude;
        $latitude        = $request->latitude;
        $is_user_collect = $request->is_user_collect ?? 0;
        $nursing_home_id = $request->nursing_home_id ?? 0;
        $is_random       = $request->is_random ?? 0;
        $nursingHomes    = [];


        if ($type == 3 && $longitude && $latitude) {
            $nursingHomes = NursingHome::whereNotNull('longitude')->whereNotNull('latitude')
                ->selectRaw('*, round((
                    6370.996 * acos (
                    cos ( radians( ? ) )
                    * cos( radians( latitude ) )
                    * cos( radians( longitude ) - radians( ? ) )
                    + sin ( radians( ? ) )
                    * sin( radians( latitude ) )
                    )
                    ), 1) AS distance', [$latitude, $longitude, $latitude])
                ->orderBy('distance')->limit(100)->pluck('id')->toArray();
        }

        // 通过关键字查询养老院
        $keyword_nursingHome_ids = [];
        if ($keyword) {
            $keyword_nursingHome_ids = NursingHome::where('name', 'like', "%$keyword%")->pluck('id')->toArray();
        }

        $result = Diary::where('status', 3)
            ->where(function ($query) {
                $query->whereNull('publish_at')
                    ->orWhere('publish_at', '<=', now());
            })
            ->with('nursingHome')
            ->when($keyword, function ($query) use ($keyword, $keyword_nursingHome_ids) {
                return $query->where(function ($query) use ($keyword, $keyword_nursingHome_ids) {
                    $query->where('title', 'like', "%$keyword%")
                        ->orWhereIn('nursing_home_id', $keyword_nursingHome_ids);
                });
            })
            ->when($type == 2, function ($query) {
                return $query->orderBy('is_recommend', 'desc');
            })
            ->when($type == 3, function ($query) use ($nursingHomes) {
                return $query->whereIn('nursing_home_id', $nursingHomes);
            })
            ->when($is_user_collect, function ($query) use ($user) {
                return $query->whereIn('id', $user->collections()->pluck('diary_id')->toArray());
            })
            ->when($nursing_home_id, function ($query) use ($nursing_home_id) {
                return $query->where('nursing_home_id', $nursing_home_id);
            })
            ->when($is_random, function ($query) {
                return $query->inRandomOrder();
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        foreach ($result as $key => $value) {
            if (auth('api')->check()) {
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }
        return $this->success(DiaryListResource::collection($result));
    }

    /**
     * 已废弃。备用接口。
     * 【我的】 列表。支持排除阅读历史
     * 按时间顺序，从新到旧播放
     */
    public function myListWithHistory(Request $request)
    {
        $limit = $request->limit ?? 20;
        $currentPage = $request->input('page', 1);  // 当前页码

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 如果未登录，播放默认视频：如何设定我的养老机构
        $default_video_id = 21;
        if (!auth('api')->check()) {
            $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);
        } else {
            $read_history = collect([]);
            $redis_key = 'history_mine:' . $user->id;
            $read_history = collect(Redis::get($redis_key) ? explode(',', Redis::get($redis_key)) : []);

            $diaries = $this->getDiaries()
                // ->whereIn('nursing_home_id', $user->nursingHomes()->pluck('nursing_home_id')->toArray())
                ->where('nursing_home_id', $user->nursing_home_id)
                ->whereNotIn('id', $read_history)
                ->orderBy('created_at', 'desc')
                ->take($limit)
                ->get();

            // 加入$read_history缓存
            $read_history = $read_history->merge($diaries->pluck('id'))->unique()->values();
            if ($diaries->count() < $limit) {
                Redis::del($redis_key);
            } else {
                Redis::set($redis_key, implode(',', $read_history->toArray()));
                Redis::expire($redis_key, 60 * 60 * 48); // 48小时
            }
            $result = new LengthAwarePaginator(
                $diaries,
                $diaries->count() + 1000,
                $limit,
                $currentPage,
                ['path' => $request->url()]
            );
        }

        // 如果无数据，播放默认视频：如何设定我的养老机构
        if ($result->isEmpty()) {
            $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);
        }

        if ($user) {
            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }

        return $this->success(DiaryListResource::collection($result));
    }

    /**
     * 【我的】 列表
     * 按时间顺序，从新到旧播放
     */
    public function myList(Request $request)
    {
        $limit = $request->limit ?? 20;

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 如果未登录，播放默认视频：如何设定我的养老机构
        $default_video_id = 21;
        if (!auth('api')->check()) {
            $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);
        } else {
            $result = $this->getDiaries()
                // ->whereIn('nursing_home_id', $user->nursingHomes()->pluck('nursing_home_id')->toArray())
                ->where('nursing_home_id', $user->nursing_home_id)
                ->orderBy('is_recommend', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($limit);
        }

        // 如果无数据，播放默认视频：如何设定我的养老机构
        if ($result->isEmpty()) {
            $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);
        }

        if ($user) {
            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }

        return $this->success(DiaryListResource::collection($result));
    }


    /**
     * 【分享】 列表
     */
    public function shareList(Request $request)
    {
        $limit = $request->limit ?? 20;
        $diary_id = $request->input('diary_id') ?? 0;

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 如果 diary_id 有值，则先获取该记录
        $diary = null;
        if ($diary_id > 0) {
            $diary = $this->getDiaries()->where('id', $diary_id)->first();
            if ($diary) {
                $nursing_home_id = $diary->nursing_home_id;
            }
        }

        $query = $this->getDiaries()
            ->where('nursing_home_id', $nursing_home_id);

        $result = $query->orderBy('is_recommend', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        // 如果指定了 diary_id 且该记录存在，则将其放在列表的第一个位置
        if ($diary && $result->isNotEmpty()) {
            $items = $result->items();
            // 从列表中移除该记录（如果已存在）
            $items = array_filter($items, function($item) use ($diary_id) {
                return $item->id != $diary_id;
            });
            // 将指定的记录放在第一个位置
            array_unshift($items, $diary);
            // 重新设置分页结果
            $result->setCollection(collect($items));
        }

        // 如果无数据，播放默认视频：如何设定我的养老机构
        if ($result->isEmpty()) {
            $default_video_id = 21;
            $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);
        }

        if ($user) {
            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }

        return $this->success(DiaryListResource::collection($result));
    }

    /**
     * 推荐 列表
     * 前三条为【我的机构】中，最近的三条
     * 之后，是【关注的机构】中，随机播放三条，不重复（简单起见，只考虑这一次的登录，不考虑之前的登录中，是否已经播放过）
     * 之后，是【所有机构】中，随机播放，不重复（简单起见，只考虑这一次的登录，不考虑之前的登录中，是否已经播放过）
     * 在没有养老机构上传视频时，只播放“护老好办法”上线一个月的两条视频
     * 所有视频已播放，从头开始
     */
    public function recommendList(Request $request)
    {
        $limit       = $request->limit ?? 20;
        $currentPage = $request->input('page', 1);  // 当前页码

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 未登录用户，按时间顺序，从新到旧播放
        if (!auth('api')->check()) {
            $result = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->orderBy('created_at', 'desc')
                ->paginate($limit);
        } else {

            $read_history = collect([]);
            $redis_key = 'read_history:' . $user->id;
            $read_history = collect(Redis::get($redis_key) ? explode(',', Redis::get($redis_key)) : []);

            // 已登录用户 前3条为【我的机构】中，最近的3条
            // \Log::info("我的机构:", $user->nursingHomes()->pluck('nursing_home_id')->toArray());
            $resultMy = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                // ->whereIn('nursing_home_id', $user->nursingHomes()->pluck('nursing_home_id')->toArray())
                ->where('nursing_home_id', $user->nursing_home_id)
                ->whereNotIn('id', $read_history)
                ->orderBy('created_at', 'desc')
                ->take(3)
                ->get();
            // \Log::info("我的机构的日记:", $resultMy->pluck('id')->toArray());

            // 之后，是【关注的机构】中，随机播放3条，排除以上的3条
            // \Log::info("关注机构:", $user->follows()->pluck('nursing_home_id')->toArray());
            $resultFollows = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->whereIn('nursing_home_id', $user->follows()->pluck('nursing_home_id')->toArray())
                ->whereNotIn('id', $read_history)
                ->whereNotIn('id', $resultMy->pluck('id')->toArray())
                ->inRandomOrder()
                ->take(3)
                ->get();
            // \Log::info("关注机构的日记:", $resultFollows->pluck('id')->toArray());

            $result1 = $resultMy->merge($resultFollows);

            // 之后，是【所有机构】中，随机播放，排除以上的6条
            $resultAll = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->whereNotIn('id', $read_history)
                ->inRandomOrder()
                ->take($limit)
                ->get();
            $diaries = $result1->merge($resultAll);

            if ($diaries->count() == 0) {
                $result = Diary::where('status', 3)
                    ->where(function ($query) {
                        $query->whereNull('publish_at')
                            ->orWhere('publish_at', '<=', now());
                    })
                    ->with('nursingHome')
                    ->take($limit)
                    ->get();
            }

            // 加入$read_history缓存
            $read_history = $read_history->merge($diaries->pluck('id'))->unique()->values();
            // \Log::info($resultAll->count());

            if ($resultAll->count() < $limit) {
                Redis::del($redis_key);
            } else {
                Redis::set($redis_key, implode(',', $read_history->toArray()));
                Redis::expire($redis_key, 60 * 60 * 48); // 48小时
            }

            $result = new LengthAwarePaginator(
                $diaries,
                $diaries->count() + 1000,
                $limit,
                $currentPage,
                ['path' => $request->url()]
            );

            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }

            // \Log::info("result:", $result->pluck('id')->toArray());
        }

        return $this->success(DiaryListResource::collection($result));
    }


    /**
     * 关注列表
     * 按时间顺序，从新到旧播放
     */
    public function followList(Request $request)
    {
        $limit = $request->limit ?? 20;
        $currentPage = $request->input('page', 1);  // 当前页码

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        if (!auth('api')->check()) {
            $result = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->where('id', 18)
                ->paginate($limit);
        } else {

            $read_history = collect([]);
            $redis_key = 'follow_history:' . $user->id;
            $read_history = collect(Redis::get($redis_key) ? explode(',', Redis::get($redis_key)) : []);

            $diaries = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->whereIn('nursing_home_id', $user->follows()->pluck('nursing_home_id')->toArray())
                ->whereNotIn('id', $read_history)
                ->orderBy('created_at', 'desc')
                ->take($limit)
                ->get();

            // 加入$read_history缓存
            $read_history = $read_history->merge($diaries->pluck('id'))->unique()->values();
            if ($diaries->count() < $limit) {
                Redis::del($redis_key);
            } else {
                Redis::set($redis_key, implode(',', $read_history->toArray()));
                Redis::expire($redis_key, 60 * 60 * 48); // 48小时
            }
            $result = new LengthAwarePaginator(
                $diaries,
                $diaries->count() + 1000,
                $limit,
                $currentPage,
                ['path' => $request->url()]
            );
        }

        if ($result->isEmpty()) {
            $result = Diary::where('status', 3)
                ->where(function ($query) {
                    $query->whereNull('publish_at')
                        ->orWhere('publish_at', '<=', now());
                })
                ->with('nursingHome')
                ->where('id', 18)
                ->paginate($limit);
        }

        if (auth('api')->check()) {
            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }
        return $this->success(DiaryListResource::collection($result));
    }


    /**
     * 护老好办法 列表
     * 只播放“护老好办法”的视频
     * 所有视频已播放，从头开始
     */
    public function ideaList(Request $request)
    {
        $limit       = $request->limit ?? 20;
        $currentPage = $request->input('page', 1);  // 当前页码

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 未登录用户，按时间顺序，从新到旧播放
        if (!auth('api')->check()) {
            $result = Diary::where('nursing_home_id', 1)
                ->published()
                ->with('nursingHome')
                ->orderBy('created_at', 'desc')
                ->paginate($limit);
        } else {

            $read_history = collect([]);
            $redis_key = 'read_history_idea:' . $user->id;
            $read_history = collect(Redis::get($redis_key) ? explode(',', Redis::get($redis_key)) : []);

            // 机构是【护老好办法】中，随机播放
            $diaries = Diary::where('nursing_home_id', 1)
                ->published()
                ->with('nursingHome')
                ->whereNotIn('id', $read_history)
                ->inRandomOrder()
                ->take($limit)
                ->get();

            if ($diaries->count() == 0) {
                $result = Diary::where('nursing_home_id', 1)
                    ->published()
                    ->with('nursingHome')
                    ->take($limit)
                    ->get();
            }

            // 加入$read_history缓存
            $read_history = $read_history->merge($diaries->pluck('id'))->unique()->values();

            if ($diaries->count() < $limit) {
                Redis::del($redis_key);
            } else {
                Redis::set($redis_key, implode(',', $read_history->toArray()));
                Redis::expire($redis_key, 60 * 60 * 48); // 48小时
            }

            $result = new LengthAwarePaginator(
                $diaries,
                $diaries->count() + 1000,
                $limit,
                $currentPage,
                ['path' => $request->url()]
            );

            foreach ($result as $key => $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }

            // \Log::info("result:", $result->pluck('id')->toArray());
        }

        return $this->success(DiaryListResource::collection($result));
    }

    /**
     * 机构主页（未绑定时，显示教学视频）
     */
    public function nursingHomeIndex(Request $request)
    {
        $limit = $request->limit ?? 20;

        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        // 检查用户是否绑定了养老机构
        if ($user) {
            $userNursingHome = UserNursingHome::where('user_id', $user->id)->first();

            if ($userNursingHome) {
                // 用户已绑定养老机构，返回type和nursing_home信息
                $nursingHome = NursingHome::find($userNursingHome->nursing_home_id);

                if ($nursingHome) {
                    return $this->success([
                        'type' => 'nursing_home',
                        'nursing_home' => new NursingHomeResource($nursingHome)
                    ]);

                }
            }
        }

        // 用户未绑定养老机构或未登录，返回默认教学视频
        $default_video_id = 21;
        $result = $this->getDiaries()
                ->where('id', $default_video_id)
                ->paginate($limit);

        if ($user) {
            foreach ($result as $value) {
                // 增加播放量
                $value->increment('browse_number');
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }

        return $this->success(DiaryListResource::collection($result));
    }



}
