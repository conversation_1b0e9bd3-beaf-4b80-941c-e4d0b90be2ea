<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PagesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('pages')->delete();
        
        \DB::table('pages')->insert(array (
            0 => 
            array (
                'id' => 1,
                'title' => '测试页面',
                'seo_title' => '测试页面',
                'seo_keywords' => '测试页面',
                'seo_description' => '测试页面',
                'parent_id' => 0,
                'sort_order' => 1,
                'permalink' => 'test',
                'content' => '<div>HTML的全称为超文本标记语言，是一种标记语言。它包括一系列标签．通过这些标签可以将网络上的文档格式统一，使分散的Internet资源连接为一个逻辑整体。HTML文本是由HTML命令组成的描述性文本，HTML命令可以说明文字，图形、动画、声音、表格、链接等。</div>
<div>&nbsp;</div>
<div>超文本是一种组织信息的方式，它通过超级链接方法将文本中的文字、图表与其他信息媒体相关联。这些相互关联的信息媒体可能在同一文本中，也可能是其他文件，或是地理位置相距遥远的某台计算机上的文件。这种组织信息方式将分布在不同位置的信息资源用随机方式进行连接，为人们查找，检索信息提供方便。 </div>',
                'status' => 1,
                'created_at' => '2022-11-02 16:36:25',
                'updated_at' => '2022-11-02 16:36:25',
            ),
        ));
        
        
    }
}