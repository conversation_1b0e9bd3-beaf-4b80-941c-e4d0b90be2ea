<?php

namespace App\Admin\Actions\Refund;

use App\Models\Order;
use Illuminate\Http\Request;
use Dcat\Admin\Actions\Action;
use Dcat\Admin\Actions\Response;
use App\Admin\Repositories\Order as OrderRepo;

class RefundForm extends Action
{

    /**
     * 按钮标题
     *
     * @var string
     */
    protected $title = '<i class="fa fa-jpy"></i>&nbsp;&nbsp;退款';

    /**
     * @var array
     */
    protected $htmlClasses = ['btn', 'btn-danger', 'text-white', 'btn-sm', 'order-refund-btn'];

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        $order = Order::find($id);
        if (!$order) {
            // 返回为空提示
            return $this->response()->error('退款失败！')->refresh();
        }

        if (!$order->paid_at) {
            return $this->response()->error('订单未支付！')->refresh();
        }

        if ($order->refund_no) {
            return $this->response()->error('订单已退款！')->refresh();
        }

        $refund_no = date('YmdHis') . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        try {
            // 退款操作
            $app = \EasyWeChat::payment();
            // 参数分别为：微信订单号、商户退款单号、订单金额、退款金额、其他参数
            $result = $app->refund->byOutTradeNumber($order->no, $refund_no, $order->total_amount * 100, $order->total_amount * 100, [
                'refund_desc' => '报名退款',
            ]);
            if ($result['result_code'] == "FAIL") {
                return $this->response()->error($result['err_code_des']);
            }
            // 返回结果示例
            // array:18 [
            //     "return_code" => "SUCCESS"
            //     "return_msg" => "OK"
            //     "appid" => "wx6c629bf6fdffda43"
            //     "mch_id" => "1635265006"
            //     "nonce_str" => "8ukZjJeopEtR53wJ"
            //     "sign" => "EEA2B3B931F0857C0FDD6F41D09443B8"
            //     "result_code" => "SUCCESS"
            //     "transaction_id" => "4200001711202211294828060733"
            //     "out_trade_no" => "20221129113600205108"
            //     "out_refund_no" => "xxxx434134343"
            //     "refund_id" => "50301304092022112927780772404"
            //     "refund_channel" => null
            //     "refund_fee" => "10"
            //     "coupon_refund_fee" => "0"
            //     "total_fee" => "10"
            //     "cash_fee" => "10"
            //     "coupon_refund_count" => "0"
            //     "cash_refund_fee" => "10"
            // ]
            \Log::info("报名编号：" . $order->no . "，微信支付退款：" . json_encode($result));

            $query_refund = $app->refund->queryByOutTradeNumber($order->no);

            if($query_refund['result_code'] == 'SUCCESS'){
                $order->update([
                    'refund_no' => $refund_no,
                    'refund_id' => $result['refund_id'],
                    'status'    => OrderRepo::STATUS_CLOSED,
                ]);
                return $this->response()->success('退款成功')->refresh();
            } else {
                return $this->response()->error('退款失败')->refresh();
            }
        } catch (\Throwable $th) {
            // 报告异常，记录到日志
            report($th);
            // 返回转换失败提示
            return $this->response()->error('退款失败');
        }
    }
    public function confirm()
    {
        return '确定退款吗？';
    }
}
