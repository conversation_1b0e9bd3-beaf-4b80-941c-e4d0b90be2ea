/******
 ****** Global font
 ******/
html body {
    font-size: 13px !important;
    font-family: "pingfang SC", "helvetica neue", arial, "hiragino sans gb", "microsoft yahei ui", "microsoft yahei", simsun, sans-serif;
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    -moz-text-size-adjust: 100% !important;
}

body,
html {
    margin: 0;
    padding: 0;
}

:root {
    --bs-breakpoint-xs: 0;
    --bs-breakpoint-sm: 576px;
    --bs-breakpoint-md: 768px;
    --bs-breakpoint-lg: 992px;
    --bs-breakpoint-xl: 1320px;
    --bs-breakpoint-xxl: 1400px;
}

/******
 ****** Globa font size
 ******/
.site-wrapper h1 {
    font-size: 28px;
}

.site-wrapper h2 {
    font-size: 24px;
}

.site-wrapper h3 {
    font-size: 20px;
}

.site-wrapper h4 {
    font-size: 16px;
}

.site-wrapper h5 {
    font-size: 15px;
}

.site-wrapper h6 {
    font-size: 14px;
}

.site-wrapper h1,
.site-wrapper h2,
.site-wrapper h3,
.site-wrapper h4,
.site-wrapper h5 {
    color: #000;
    font-weight: bold;
}

@media (min-width: 768px) {
    .site-wrapper h1 {
        font-size: 32px;
    }

    .site-wrapper h2 {
        font-size: 28px;
    }

    .site-wrapper h3 {
        font-size: 24px;
    }

    .site-wrapper h4 {
        font-size: 20px;
    }

    .site-wrapper h5 {
        font-size: 16px;
    }

    .site-wrapper h6 {
        font-size: 15px;
    }
}

@media (min-width: 1200px) {
    .site-wrapper h1 {
        font-size: 36px;
    }

    .site-wrapper h2 {
        font-size: 32px;
    }

    .site-wrapper h3 {
        font-size: 28px;
    }

    .site-wrapper h4 {
        font-size: 24px;
    }

    .site-wrapper h5 {
        font-size: 20px;
    }

    .site-wrapper h6 {
        font-size: 16px;
    }
}

p:last-child {
    margin-bottom: 0;
}

.bold {
    font-weight: bold !important;
}

a:hover .text {
    color: #222;
}

a:hover {
    text-decoration-line: none !important;
}

.page-item.active .page-link {
    background-color: #222 !important;
    color: #fff;
}

.page-link {
    color: #222;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
}

/******
 ****** Globa color
 ******/
.bg-primary {
    background-color: #F2AE1E;
}

.text-primary {
    color: #F2AE1E !important;
}

.text-black {
    color: #000;
}

.text-grey {
    color: #333;
}

/******
 ****** Globa button
 ******/
a {
    color: #333;
    cursor: pointer;
    outline: none;
    text-decoration: none;
}

a:hover,
a:focus {
    color: #F2AE1E;
    text-decoration: none;
    transition: all 0.5s;
}

.nav-link:hover, .nav-link:focus {
    color: #F2AE1E;
}

.btn-primary {
    --bs-btn-color: #fff;
    --bs-btn-bg: #F2AE1E;
    --bs-btn-border-color: #F2AE1E;
    --bs-btn-hover-color: #F2AE1E;
    --bs-btn-hover-bg: transparent;
    --bs-btn-hover-border-color: #F2AE1E;
    --bs-btn-focus-shadow-rgb: 49, 132, 253;
    --bs-btn-active-color: #F2AE1E;
    --bs-btn-active-bg: transparent;
    --bs-btn-active-border-color: #F2AE1E;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #a6a6a6;
    --bs-btn-disabled-bg: rgba(166, 166, 166, 0.15);
    --bs-btn-disabled-border-color: rgba(166, 166, 166, 0.15);
}

.btn-outline-primary {
    --bs-btn-color: #F2AE1E;
    --bs-btn-border-color: #F2AE1E;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #F2AE1E;
    --bs-btn-hover-border-color: #F2AE1E;
    --bs-btn-focus-shadow-rgb: 13, 110, 253;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #F2AE1E;
    --bs-btn-active-border-color: #F2AE1E;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #F2AE1E;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #F2AE1E;
    --bs-gradient: none;
}

.nav-underline {
    --bs-nav-underline-gap: 2rem;
}

.nav-underline .nav-link,
.nav-underline .show>.nav-link{
    color: #000;
    font-weight: bold;
    font-size: 20px;
    padding: 0;
}

.nav-underline .nav-link.active {
    color: #f2ae1e;
}

@media (min-width: 576px) {
    .container, .container-sm {
        max-width: 100%;
    }
}

@media (min-width: 768px) {
    .container, .container-md, .container-sm {
        max-width: 100%;
    }
}

@media (min-width: 992px) {
    .container, .container-lg, .container-md, .container-sm {
        max-width: 100%;
    }
}

@media (min-width: 1200px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl {
        max-width: 1140px;
    }
}

@media (min-width: 1400px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1320px;
    }
}


/******
 ****** Globa round
 ******/
.round-3 {
    border-radius: 2px;
}

.round-10 {
    border-radius: 6px;
}

.round-16 {
    border-radius: 8px;
}

.round-20 {
    border-radius: 10px;
}

.round-25 {
    border-radius: 12px;
}

.round-30 {
    border-radius: 15px;
}

.round-40 {
    border-radius: 30px;
}

@media (min-width: 768px) {
    .round-3 {
        border-radius: 3px;
    }

    .round-10 {
        border-radius: 8px;
    }

    .round-16 {
        border-radius: 10px;
    }

    .round-20 {
        border-radius: 15px;
    }

    .round-25 {
        border-radius: 18px;
    }

    .round-30 {
        border-radius: 20px;
    }

    .round-40 {
        border-radius: 30px;
    }
}

@media (min-width: 1200px) {
    .round-10 {
        border-radius: 10px;
    }

    .round-16 {
        border-radius: 16px;
    }

    .round-20 {
        border-radius: 20px;
    }

    .round-25 {
        border-radius: 25px;
    }

    .round-30 {
        border-radius: 30px;
    }

    .round-40 {
        border-radius: 40px;
    }
}

/******
 ****** Globa image
 ******/
img {
    width: auto;
    height: auto;
    max-width: 100%;
}

.zoom-out,
.zoom-in {
    position: relative;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.zoom-out img,
.zoom-in img {
    transition: all 0.3s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.zoom-in:hover img {
    -webkit-transform: scale(1.15);
    transform: scale(1.15);
}

.zoom-out:hover img {
    -webkit-transform: scale(0.85);
    transform: scale(0.85);
}

.content-float {
    transition: all 0.2s linear;
}

.content-float:hover {
    transform: translate3d(0, -3px, 0);
    -webkit-transform: translate3d(0, -3px, 0);
}

.content-float:hover {
    box-shadow: 0 20px 20px 0 rgba(0, 0, 0, 0.1);
}

.shadow-hover {
    transition: all 0.3s ease-in-out;
}

.shadow-hover:hover {
    background-color: #fff;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.08);
}

/******
 ****** Add ratio style
 ******/
.ratio {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

.ratio::before {
    display: block;
    content: "";
}

.ratio>.ratio-area {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

.ratio-1by1::before {
    padding-top: 100%;
}

.ratio-4by3::before {
    padding-top: 75%;
}

.ratio-2by1::before {
    padding-top: 50%;
}

.ratio-16by9::before {
    padding-top: 56.25%;
}

.ratio-21by9::before {
    padding-top: 42.857143%;
}

@media (min-width: 576px) {
    .ratio-sm-1by1::before {
        padding-top: 100%;
    }

    .ratio-sm-4by3::before {
        padding-top: 75%;
    }

    .ratio-sm-2by1::before {
        padding-top: 50%;
    }

    .ratio-sm-16by9::before {
        padding-top: 56.25%;
    }

    .ratio-sm-21by9::before {
        padding-top: 42.857143%;
    }
}

@media (min-width: 768px) {
    .ratio-md-1by1::before {
        padding-top: 100%;
    }

    .ratio-md-4by3::before {
        padding-top: 75%;
    }

    .ratio-md-2by1::before {
        padding-top: 50%;
    }

    .ratio-md-16by9::before {
        padding-top: 56.25%;
    }

    .ratio-md-21by9::before {
        padding-top: 42.857143%;
    }
}

@media (min-width: 992px) {
    .ratio-lg-1by1::before {
        padding-top: 100%;
    }

    .ratio-lg-4by3::before {
        padding-top: 75%;
    }

    .ratio-lg-2by1::before {
        padding-top: 50%;
    }

    .ratio-lg-16by9::before {
        padding-top: 56.25%;
    }

    .ratio-lg-21by9::before {
        padding-top: 42.857143%;
    }
}

@media (min-width: 1200px) {
    .ratio-xl-1by1::before {
        padding-top: 100%;
    }

    .ratio-xl-4by3::before {
        padding-top: 75%;
    }

    .ratio-xl-2by1::before {
        padding-top: 50%;
    }

    .ratio-xl-16by9::before {
        padding-top: 56.25%;
    }

    .ratio-xl-21by9::before {
        padding-top: 42.857143%;
    }
}

/******
 ****** Add grid style
 ******/
.row-0 {
    --bs-gutter-x: 0;
}

.row-10 {
    --bs-gutter-x: 10px;
}

.row-20 {
    --bs-gutter-x: 20px;
}

.row-30 {
    --bs-gutter-x: 30px;
}

.row-40 {
    --bs-gutter-x: 40px;
}

.row-50 {
    --bs-gutter-x: 50px;
}

.row-60 {
    --bs-gutter-x: 60px;
}

.row-70 {
    --bs-gutter-x: 70px;
}

.row-80 {
    --bs-gutter-x: 80px;
}

.row-90 {
    --bs-gutter-x: 90px;
}

.row-100 {
    --bs-gutter-x: 100px;
}

.row-110 {
    --bs-gutter-x: 110px;
}

.row-120 {
    --bs-gutter-x: 120px;
}

.row-130 {
    --bs-gutter-x: 130px;
}

.row-140 {
    --bs-gutter-x: 140px;
}

.row-150 {
    --bs-gutter-x: 150px;
}

.row-160 {
    --bs-gutter-x: 160px;
}

.row-170 {
    --bs-gutter-x: 170px;
}

.row-180 {
    --bs-gutter-x: 180px;
}

.row-190 {
    --bs-gutter-x: 190px;
}

.row-200 {
    --bs-gutter-x: 200px;
}

@media (min-width: 576px) {
    .row-sm-0 {
        --bs-gutter-x: 0;
    }

    .row-sm-10 {
        --bs-gutter-x: 10px;
    }

    .row-sm-20 {
        --bs-gutter-x: 20px;
    }

    .row-sm-30 {
        --bs-gutter-x: 30px;
    }

    .row-sm-40 {
        --bs-gutter-x: 40px;
    }

    .row-sm-50 {
        --bs-gutter-x: 50px;
    }

    .row-sm-60 {
        --bs-gutter-x: 60px;
    }

    .row-sm-70 {
        --bs-gutter-x: 70px;
    }

    .row-sm-80 {
        --bs-gutter-x: 80px;
    }

    .row-sm-90 {
        --bs-gutter-x: 90px;
    }

    .row-sm-100 {
        --bs-gutter-x: 100px;
    }

    .row-sm-110 {
        --bs-gutter-x: 110px;
    }

    .row-sm-120 {
        --bs-gutter-x: 120px;
    }

    .row-sm-130 {
        --bs-gutter-x: 130px;
    }

    .row-sm-140 {
        --bs-gutter-x: 140px;
    }

    .row-sm-150 {
        --bs-gutter-x: 150px;
    }

    .row-sm-160 {
        --bs-gutter-x: 160px;
    }

    .row-sm-170 {
        --bs-gutter-x: 170px;
    }

    .row-sm-180 {
        --bs-gutter-x: 180px;
    }

    .row-sm-190 {
        --bs-gutter-x: 190px;
    }

    .row-sm-200 {
        --bs-gutter-x: 200px;
    }
}

@media (min-width: 768px) {
    .row-md-0 {
        --bs-gutter-x: 0;
    }

    .row-md-10 {
        --bs-gutter-x: 10px;
    }

    .row-md-20 {
        --bs-gutter-x: 20px;
    }

    .row-md-30 {
        --bs-gutter-x: 30px;
    }

    .row-md-40 {
        --bs-gutter-x: 40px;
    }

    .row-md-50 {
        --bs-gutter-x: 50px;
    }

    .row-md-60 {
        --bs-gutter-x: 60px;
    }

    .row-md-70 {
        --bs-gutter-x: 70px;
    }

    .row-md-80 {
        --bs-gutter-x: 80px;
    }

    .row-md-90 {
        --bs-gutter-x: 90px;
    }

    .row-md-100 {
        --bs-gutter-x: 100px;
    }

    .row-md-110 {
        --bs-gutter-x: 110px;
    }

    .row-md-120 {
        --bs-gutter-x: 120px;
    }

    .row-md-130 {
        --bs-gutter-x: 130px;
    }

    .row-md-140 {
        --bs-gutter-x: 140px;
    }

    .row-md-150 {
        --bs-gutter-x: 150px;
    }

    .row-md-160 {
        --bs-gutter-x: 160px;
    }

    .row-md-170 {
        --bs-gutter-x: 170px;
    }

    .row-md-180 {
        --bs-gutter-x: 180px;
    }

    .row-md-190 {
        --bs-gutter-x: 190px;
    }

    .row-md-200 {
        --bs-gutter-x: 200px;
    }
}

@media (min-width: 992px) {
    .row-lg-0 {
        --bs-gutter-x: 0;
    }

    .row-lg-10 {
        --bs-gutter-x: 10px;
    }

    .row-lg-20 {
        --bs-gutter-x: 20px;
    }

    .row-lg-30 {
        --bs-gutter-x: 30px;
    }

    .row-lg-40 {
        --bs-gutter-x: 40px;
    }

    .row-lg-50 {
        --bs-gutter-x: 50px;
    }

    .row-lg-60 {
        --bs-gutter-x: 60px;
    }

    .row-lg-70 {
        --bs-gutter-x: 70px;
    }

    .row-lg-80 {
        --bs-gutter-x: 80px;
    }

    .row-lg-90 {
        --bs-gutter-x: 90px;
    }

    .row-lg-100 {
        --bs-gutter-x: 100px;
    }

    .row-lg-110 {
        --bs-gutter-x: 110px;
    }

    .row-lg-120 {
        --bs-gutter-x: 120px;
    }

    .row-lg-130 {
        --bs-gutter-x: 130px;
    }

    .row-lg-140 {
        --bs-gutter-x: 140px;
    }

    .row-lg-150 {
        --bs-gutter-x: 150px;
    }

    .row-lg-160 {
        --bs-gutter-x: 160px;
    }

    .row-lg-170 {
        --bs-gutter-x: 170px;
    }

    .row-lg-180 {
        --bs-gutter-x: 180px;
    }

    .row-lg-190 {
        --bs-gutter-x: 190px;
    }

    .row-lg-200 {
        --bs-gutter-x: 200px;
    }
}

@media (min-width: 1200px) {
    .row-xl-0 {
        --bs-gutter-x: 0;
    }

    .row-xl-10 {
        --bs-gutter-x: 10px;
    }

    .row-xl-20 {
        --bs-gutter-x: 20px;
    }

    .row-xl-30 {
        --bs-gutter-x: 30px;
    }

    .row-xl-40 {
        --bs-gutter-x: 40px;
    }

    .row-xl-50 {
        --bs-gutter-x: 50px;
    }

    .row-xl-60 {
        --bs-gutter-x: 60px;
    }

    .row-xl-70 {
        --bs-gutter-x: 70px;
    }

    .row-xl-80 {
        --bs-gutter-x: 80px;
    }

    .row-xl-90 {
        --bs-gutter-x: 90px;
    }

    .row-xl-100 {
        --bs-gutter-x: 100px;
    }

    .row-xl-110 {
        --bs-gutter-x: 110px;
    }

    .row-xl-120 {
        --bs-gutter-x: 120px;
    }

    .row-xl-130 {
        --bs-gutter-x: 130px;
    }

    .row-xl-140 {
        --bs-gutter-x: 140px;
    }

    .row-xl-150 {
        --bs-gutter-x: 150px;
    }

    .row-xl-160 {
        --bs-gutter-x: 160px;
    }

    .row-xl-170 {
        --bs-gutter-x: 170px;
    }

    .row-xl-180 {
        --bs-gutter-x: 180px;
    }

    .row-xl-190 {
        --bs-gutter-x: 190px;
    }

    .row-xl-200 {
        --bs-gutter-x: 200px;
    }

    .col-xl-65 {
        width: 54%;
    }

    .col-xl-55 {
        width: 46%;
    }
}

/******
 ****** General
 ******/
.wrapper-none,
.home .wrapper {
    background: none;
}

.content {
    padding-top: 30px;
    padding-bottom: 30px;
}

.home-wrapper .content {
    padding: 0;
}

.main-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.section-title {
    margin-bottom: 30px;
}

.single-title {
    margin-bottom: 30px;
}

@media (min-width: 768px) {
    .content {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    .section-title {
        margin-bottom: 50px;
    }
}

@media (min-width: 1200px) {
    .content {
        padding-top: 80px;
        padding-bottom: 80px;
    }

    .section-title {
        margin-bottom: 70px;
    }
}

/******
 ****** 404
 ******/
.error-404-wrapper {
    padding: 60px 0;
    text-align: center;
}

.img-404 {
    line-height: 0;
}

.img-404 img {
    max-height: 150px;
}

@media (min-width: 768px) {
    .error-404-wrapper {
        padding: 80px 0;
    }

    .img-404 img {
        max-height: 200px;
    }
}

@media (min-width: 992px) {
    .error-404-wrapper {
        padding: 100px 0;
    }

    .img-404 img {
        max-height: 260px;
    }
}

@media (min-width: 1200px) {
    .error-404-wrapper {
        padding: 150px 0;
    }

    .img-404 img {
        max-height: 320px;
    }
}

.welcome-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.welcome-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.welcome-icon {
    transition: transform 0.3s ease;
}

.welcome-card:hover .welcome-icon {
    transform: scale(1.1);
}

.btn-lg {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card,
.card-header,
.card-body{
    border: 0;
}

.card-header{
    padding: 1.25rem;
    background-color: #fff;
}

.welcome-logo {
    max-width: 200px;
    height: auto;
    transition: transform 0.3s ease;
    display: inline;
}

.welcome-logo:hover {
    transform: scale(1.05);
}

.display-4 {
    background: linear-gradient(45deg, #F2AE1E, #d49a1a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 空状态占位符样式 */
.empty-state {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.empty-icon {
    transition: transform 0.3s ease;
}

.empty-state:hover .empty-icon {
    transform: scale(1.1);
}

.empty-tips {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px 20px;
    border-left: 4px solid #F2AE1E;
    max-width: 400px;
}

/* 截图水印样式 */
.screenshot-watermark {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    user-select: none;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.screenshot-watermark * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
