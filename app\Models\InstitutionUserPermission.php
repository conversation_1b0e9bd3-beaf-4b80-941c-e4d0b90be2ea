<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstitutionUserPermission extends Model
{
    use HasFactory;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'institution_user_permissions';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'nursing_home_id',
        'can_manage_staff',
        'can_manage_info',
        'can_manage_videos',
        'can_view_data',
        'can_manage_finance',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'can_manage_staff' => 'boolean',
        'can_manage_info' => 'boolean',
        'can_manage_videos' => 'boolean',
        'can_view_data' => 'boolean',
        'can_manage_finance' => 'boolean',
    ];
    
    /**
     * Get the user that owns the permission.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the nursing home that owns the permission.
     */
    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class);
    }
}