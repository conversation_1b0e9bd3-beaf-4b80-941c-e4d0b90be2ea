@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/invoice/index',
])
@endsection


@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">申请发票</h5>
    <div class="row">
      <div class="col-md-12">
        <div class="card mb-4">
          <div class="card-body">
            @if(session('message'))
            <div class="alert alert-info">
              {{ session('message') }}
            </div>
            @endif

            <section class="border-main rounded mt-1 bg-white p-3 p-lg-5">

            <form class="form-wrap" action="{{ route('invoice.store') }}" method="POST">
              @csrf
              <div class="row mb-4">
                <label for="payment_id" class="col-sm-2 col-form-label">选择付款记录</label>
                <div class="col-sm-5">
                  <select class="form-select" id="payment_id" name="payment_id" required>
                    <option value="">请选择付款记录</option>
                    @foreach($payments as $payment)
                      <option value="{{ $payment->id }}" data-amount="{{ $payment->total_amount }}" {{ request('payment_id') == $payment->id ? 'selected' : '' }}>
                        订单号：{{ $payment->no }} - 金额：{{ $payment->total_amount }} - 付款时间：{{ $payment->paid_at }}
                      </option>
                    @endforeach
                  </select>
                </div>
              </div>

              <div class="row mb-4">
                <label for="amount" class="col-sm-2 col-form-label">发票金额</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="amount" name="amount" readonly>
                </div>
              </div>

              <div class="row mb-4">
                <label for="invoice_title" class="col-sm-2 col-form-label">发票抬头</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="invoice_title" name="invoice_title" required>
                </div>
              </div>

              <div class="row mb-4">
                <label for="tax_number" class="col-sm-2 col-form-label">税号</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="tax_number" name="tax_number">
                </div>
              </div>

              <div class="row mb-4">
                <label for="address" class="col-sm-2 col-form-label">地址</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="address" name="address">
                </div>
              </div>

              <div class="row mb-4">
                <label for="phone" class="col-sm-2 col-form-label">电话</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="phone" name="phone">
                </div>
              </div>

              <div class="row mb-4">
                <label for="bank_name" class="col-sm-2 col-form-label">开户行</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="bank_name" name="bank_name">
                </div>
              </div>

              <div class="row mb-4">
                <label for="bank_account" class="col-sm-2 col-form-label">银行账户</label>
                <div class="col-sm-5">
                  <input type="text" class="form-control" id="bank_account" name="bank_account">
                </div>
              </div>

              <div class="row mb-4">
                <label for="remark" class="col-sm-2 col-form-label">备注</label>
                <div class="col-sm-5">
                  <textarea class="form-control" id="remark" name="remark" rows="3"></textarea>
                </div>
              </div>

              <button type="submit" class="btn btn-primary">提交申请</button>
            </form>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const paymentSelect = document.getElementById('payment_id');
    const amountInput = document.getElementById('amount');

    // 页面加载时，如果已选择付款记录，则填充金额
    const selectedOption = paymentSelect.options[paymentSelect.selectedIndex];
    if (selectedOption.value) {
      const amount = selectedOption.getAttribute('data-amount');
      amountInput.value = amount ? parseFloat(amount).toFixed(2) : '';
    }

    paymentSelect.addEventListener('change', function() {
      const selectedOption = this.options[this.selectedIndex];
      const amount = selectedOption.getAttribute('data-amount');
      amountInput.value = amount ? parseFloat(amount).toFixed(2) : '';
    });
  });
</script>
@endsection
