<?php

namespace App\Http\Controllers\Web;

use App\Models\Milestone;
use App\Models\NursingHome;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\NursingHomeContact;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Web\WebController;

class NursingHomeController extends WebController
{
    /**
     * 机构编辑信息
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // 获取用户管理的机构ID
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        $result = NursingHome::findOrFail($nursing_home_id);

        $image_urls_json = $this->imageUrlsJson($result->image_urls);
        $price_image_urls_json = $this->imageUrlsJson($result->price_image_urls);
        $transportation_image_urls_json = $this->imageUrlsJson($result->transportation_image_urls);
        $honor_image_urls_json = $this->imageUrlsJson($result->honor_image_urls);
        $other_image_urls_json = $this->imageUrlsJson($result->other_image_urls);
        // dd($image_urls_json);

        return view('nursing-home.edit', compact('result', 'image_urls_json', 'price_image_urls_json', 'transportation_image_urls_json', 'honor_image_urls_json', 'other_image_urls_json'));
    }

    /**
     * Json格式化图片地址
     */
    public function imageUrlsJson($image_urls)
    {
        $image_urls_json = [];
        if (!empty($image_urls)) {
            $image_urls_json = array_map(function ($url, $index) {
                // 从URL中提取文件名部分
                $pathParts = explode('/', $url);
                $originalFilename = end($pathParts);

                // 创建新的文件名格式
                $name = 'image' . ($index + 1) . '.jpg';
                return [
                    'name' => $name,
                    'url' => $url
                ];
            }, $image_urls, array_keys($image_urls));
        }
        $image_urls_json = json_encode($image_urls_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        return $image_urls_json;
    }

    /**
     * 机构编辑信息
     */
    public function update(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('/')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        $request->validate(
            [
                'name' => 'required',
                'address' => 'required',
                'mobile' => 'required',
            ],
            [
                'name.required' => '名称必填',
                'address.required' => '地址必填',
                'mobile.required' => '电话必填',
            ]
        );

        $result = NursingHome::findOrFail($nursing_home_id);

        // 上传封面图片
        if ($request->hasFile('avatar_url') && $request->file('avatar_url')->isValid()) {
            $avatarUrl = $this->uploadImage($request->file('avatar_url'));
            $result->avatar_url = $avatarUrl;
        }

        // 上传视频
        if ($request->hasFile('upload_video_url') && $request->file('upload_video_url')->isValid()) {
            $videoUrl = $this->uploadImage($request->file('upload_video_url'));
            $result->upload_video_url = $videoUrl;
            // $result->video_url = null;
        }

        // 上传视频封面
        if ($request->hasFile('video_thumb_url') && $request->file('video_thumb_url')->isValid()) {
            $avatarUrl = $this->uploadImage($request->file('video_thumb_url'));
            $result->video_thumb_url = $avatarUrl;
        }

        $result->image_urls = safeExplode($request->input('image_urls'));
        $result->price_image_urls = safeExplode($request->input('price_image_urls'));
        $result->transportation_image_urls = safeExplode($request->input('transportation_image_urls'));
        $result->honor_image_urls = safeExplode($request->input('honor_image_urls'));
        $result->other_image_urls = safeExplode($request->input('other_image_urls'));

        $result->name        = $request->input('name');
        $result->province    = $request->input('province');
        $result->city        = $request->input('city');
        $result->district    = $request->input('district');
        $result->address     = $request->input('address');
        $result->mobile      = $request->input('mobile');
        $result->description = $request->input('description');
        $result->save();
        return redirect()->route('nursing-home.index')->with(['message' => '保存成功。']);
    }

    public function uploadImage($file)
    {
        $folder_name = 'nursing-home/' . date("Y/m");
        $full_image_path = '';
        if ($file) {
            // 获取文件的后缀名，因图片从剪贴板里黏贴时后缀名为空，所以此处确保后缀一直存在
            $extension = strtolower($file->getClientOriginalExtension()) ?: 'png';
            $filename = time() . '_' . Str::random(10) . '.' . $extension;
            $image_path = $file->storeAs($folder_name, $filename, 'oss');
            $full_image_path = Storage::disk('oss')->url($image_path);
        }
        return $full_image_path;
    }

    public function milestone(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        $result = NursingHome::with(['milestones'])->findOrFail($nursing_home_id);

        return view('nursing-home.milestone', [
            'result' => $result,
            'milestones' => $result->milestones, // 提取 milestones 数据
        ]);
    }

    public function milestoneUpdate(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 获取养老院对象
        $result = NursingHome::findOrFail($nursing_home_id);

        // 更新里程碑
        if ($request->has('milestones')) {
            $milestones = [];
            foreach ($request->input('milestones') as $milestone) {
                if (!empty($milestone['title']) || !empty($milestone['description'])) {
                    $milestones[] = new Milestone($milestone);
                }
            }
            $result->milestones()->delete(); // 清空旧的里程碑
            $result->milestones()->saveMany($milestones); // 保存新的里程碑
        }

        // 保存数据
        $result->save();

        return redirect()->route('nursing-home.milestone')->with(['message' => '保存成功。']);
    }

    /**
     * 机构联系信息
     */
    public function contacts(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        $results = NursingHomeContact::where('nursing_home_id', $nursing_home_id)->paginate(10);

        return view('nursing-home.contacts', compact('results'));
    }
}
