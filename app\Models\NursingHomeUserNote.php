<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class NursingHomeUserNote extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'nursing_home_user_notes';

    protected $fillable = [
        'nursing_home_id',
        'user_id',
        'nickname',
        'notes',
        'tags',
        'user_type',
        'channel',
        'user_level',
    ];

    protected $casts = [
        'tags' => 'array',
    ];

    // 用户类型常量
    const USER_TYPE_NONE = 0;
    const USER_TYPE_RESIDENT = 1;
    const USER_TYPE_FAMILY = 2;
    const USER_TYPE_STAFF = 3;
    const USER_TYPE_PARTNER = 4;

    public static $userTypes = [
        self::USER_TYPE_NONE => '无',
        self::USER_TYPE_RESIDENT => '入住老人',
        self::USER_TYPE_FAMILY => '入住老人家属',
        self::USER_TYPE_STAFF => '养老院员工',
        self::USER_TYPE_PARTNER => '养老院上下游',
    ];

    // 获客渠道常量
    const CHANNEL_NONE = 0;
    const CHANNEL_INFLUENCER = 1;
    const CHANNEL_COMMUNITY = 2;
    const CHANNEL_OTHER = 3;

    public static $channels = [
        self::CHANNEL_NONE => '无',
        self::CHANNEL_INFLUENCER => '博主大V',
        self::CHANNEL_COMMUNITY => '街道居委',
        self::CHANNEL_OTHER => '其他',
    ];

    // 用户等级常量
    const LEVEL_NONE = 0;
    const LEVEL_KEY = 1;
    const LEVEL_WATCH = 2;
    const LEVEL_NORMAL = 3;

    public static $userLevels = [
        self::LEVEL_NONE => '无',
        self::LEVEL_KEY => '重点',
        self::LEVEL_WATCH => '观察',
        self::LEVEL_NORMAL => '普通',
    ];



    /**
     * 关联到用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联到养老机构
     */
    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class, 'nursing_home_id');
    }
}
