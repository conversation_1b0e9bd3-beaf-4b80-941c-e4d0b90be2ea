<view class="page"><u-no-network vue-id="46087cad-1" bind:__l="__l"></u-no-network><u-toast class="vue-ref" vue-id="46087cad-2" data-ref="uToast" bind:__l="__l"></u-toast><u-notify class="vue-ref" vue-id="46087cad-3" data-ref="uNotify" bind:__l="__l"></u-notify><block wx:if="{{loading}}"><view class="loading"><u-loading-page vue-id="46087cad-4" loading="{{loading}}" bind:__l="__l"></u-loading-page></view></block><view class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center"><view class="u-flex-col u-col-center mb-40"><view class="form-title u-m-b-30">机构邀请</view><view class="u-font-16 u-m-b-10">邀请管理员加入机构：</view><view class="u-font-16 u-m-b-30">{{institutionName}}</view></view><view class="px-20 mb-40"><u-row vue-id="46087cad-5" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('46087cad-6')+','+('46087cad-5')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['toContractPayment',['$event']]]]]}}" class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center" bindtap="__e"><u--text vue-id="{{('46087cad-7')+','+('46087cad-6')}}" text="管理数据中心" color="#F2AE1E" bold="{{true}}" align="center" size="20" bind:__l="__l"></u--text><text class="d-block u-font-18 mt-30">邀请为数据中心管理员</text><text class="d-block mt-30 fc-666">{{package_description}}</text></view></u-col><u-col vue-id="{{('46087cad-8')+','+('46087cad-5')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['toContractPayment',['$event']]]]]}}" class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center" bindtap="__e"><u--text vue-id="{{('46087cad-9')+','+('46087cad-8')}}" text="管理创作中心" color="#F2AE1E" bold="{{true}}" align="center" size="20" bind:__l="__l"></u--text><text class="d-block u-font-18 mt-30">邀请为创作中心管理员</text><text class="d-block mt-30 fc-666">{{package_description}}</text></view></u-col></u-row></view><view class="px-80 my-20"><u-row vue-id="46087cad-10" justify="center" gutter="20" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('46087cad-11')+','+('46087cad-10')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><u-button vue-id="{{('46087cad-12')+','+('46087cad-11')}}" color="#f5f5f5" customStyle="color:#000" shape="circle" text="取消" data-event-opts="{{[['^click',[['goBack']]]]}}" bind:click="__e" bind:__l="__l"></u-button></u-col><u-col vue-id="{{('46087cad-13')+','+('46087cad-10')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><u-button vue-id="{{('46087cad-14')+','+('46087cad-13')}}" color="#F2AE1E" shape="circle" text="发送邀请" loading="{{btnLoading}}" data-event-opts="{{[['^click',[['acceptInvite']]]]}}" bind:click="__e" bind:__l="__l"></u-button></u-col></u-row></view></view></view>