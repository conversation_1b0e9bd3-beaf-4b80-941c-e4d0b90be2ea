<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\NursingHome;
use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\Payment;
use App\Admin\Actions\Restore\Restore;
use App\Models\Payment as PaymentModels;
use App\Admin\Actions\Restore\BatchRestore;
use App\Admin\Actions\Grid\SetExpireAt;
use Dcat\Admin\Http\Controllers\AdminController;
use Carbon\Carbon;

class PaymentController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Payment::with(['nursingHome']), function (Grid $grid) {

            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            // $grid->column('no');
            $grid->column('nursingHome.name', '机构名称');
            $grid->column('total_amount');
            $grid->column('paid_at');
            $grid->column('period', '订单周期')->display(function () {
                return $this->start_at ? Carbon::parse($this->start_at)->format('Y-m-d') .
                    ' ~ ' . Carbon::parse($this->end_at)->format('Y-m-d') : '-';
            });

            $grid->column('expired_at', '机构到期时间')->display(function () {
                if (isset($this->nursingHome->expired_at)) {
                    return date('Y-m-d', strtotime($this->nursingHome->expired_at));
                }
                return '-';
            });

            $grid->column('status')->using(Payment::$paymentStatusMap)->badge(Payment::$statusColor);

            // $grid->column('remark');
            // $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no')->width(3);
                $filter->equal('nursing_home_id', '机构名称')
                    ->select(NursingHome::all()->pluck('name', 'id')->toArray())->width(3);
                $filter->between('paid_at')->datetime()->width(3);
                $filter->between('created_at')->datetime()->width(3);
                $filter->equal('pay_method', '支付方式')->radio(Payment::$payMethodMap)->width(3);
                $filter->equal('status', '订单状态')->radio(Payment::$paymentStatusMap)->width(3);
                $filter->scope('trashed', '回收站')->onlyTrashed()->width(3);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(PaymentModels::class));
                } else {
                    $actions->append(new SetExpireAt());
                }
            });

            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(PaymentModels::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Payment(), function (Show $show) {
            $show->field('id');
            $show->field('no');
            $show->field('user_id');
            $show->field('total_amount');
            $show->field('paid_at');
            $show->field('transaction_id');
            $show->field('refund_no');
            $show->field('refund_id');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(Payment::with(['nursingHome']), function (Form $form) {
            $form->select('nursing_home_id')
                ->options(NursingHome::all()->pluck('name', 'id'))->required();
            $form->decimal('total_amount');
            $form->radio('pay_method')->options(Payment::$payMethodMap)->default(1);
            $form->datetime('paid_at')->default(now());
            $form->date('start_at');
            $form->date('end_at');
            $form->text('remark');
            $form->radio('status')->options(Payment::$paymentStatusMap)->default(1);

            $form->display('created_at');
            // if ($form->model()->status == Order::STATUS_PAID) {
            //     $id = $form->model()->id;
            //     $form->tools(function (Form\Tools $tools) use ($id) {
            //         $tools->append(RefundForm::make()->setKey($id));
            //     });
            // }
        });
    }
}
