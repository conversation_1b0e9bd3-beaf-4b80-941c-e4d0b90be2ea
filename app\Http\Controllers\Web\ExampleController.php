<?php

namespace App\Http\Controllers\Web;

use Mail;
use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;

class ExampleController extends WebController
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
    }

    /**
     * 发邮件示例
     *
     * @return void
     */
    public function email()
    {
        $name = 'John';
        Mail::send('emails.test', ['name' => $name], function ($message) {
            $to = '<EMAIL>';
            $message->to($to)->subject('邮件测试');
        });

        return "发送成功";
    }

}
