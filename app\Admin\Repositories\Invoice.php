<?php

namespace App\Admin\Repositories;

use App\Models\Invoice as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Invoice extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;
    
    const STATUS_PENDING = 1;  // 待处理
    const STATUS_APPROVED = 2; // 已批准
    const STATUS_REJECTED = 3; // 已拒绝
    const STATUS_ISSUED = 4;   // 已开具

    public static $statusMap = [
        self::STATUS_PENDING  => '待处理',
        self::STATUS_APPROVED => '已批准',
        self::STATUS_REJECTED => '已拒绝',
        self::STATUS_ISSUED   => '已开具',
    ];
}
