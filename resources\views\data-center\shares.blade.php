@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/data-center/user-shares',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">

    <ul class="nav nav-underline mb-4" id="userDataTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="user-data-tab" data-bs-toggle="tab" data-bs-target="#userData" type="button" role="tab" aria-controls="userData" aria-selected="true">分享数据</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab" aria-controls="statistics" aria-selected="false">统计信息</button>
      </li>
    </ul>

    @if (session('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif

    <!-- Tab Content -->
    <div class="tab-content" id="userDataTabsContent">
      <!-- 用户数据 Tab -->
      <div class="tab-pane fade show active" id="userData" role="tabpanel" aria-labelledby="user-data-tab">

        <!-- 筛选和搜索 -->
        <div class="mb-3 py-3 bg-white">
          <div class="card">
            <div class="card-body">
              @if($filteredUser || $filteredDiary || $searchUser || $searchVideo)
              <div class="alert alert-info mb-3">
                <i class="fas fa-filter me-2"></i>
                当前筛选条件：
                @if($filteredUser)
                <span class="badge bg-primary me-2">用户：{{ $filteredUser->nickname ?: '匿名用户' }}</span>
                @endif
                @if($filteredDiary)
                <span class="badge bg-success me-2">视频：{{ $filteredDiary->title }}</span>
                @endif
                @if($searchUser)
                <span class="badge bg-info me-2">搜索用户：{{ $searchUser }}</span>
                @endif
                @if($searchVideo)
                <span class="badge bg-warning me-2">搜索视频：{{ $searchVideo }}</span>
                @endif
                <a href="{{ route('data-center.user-shares') }}" class="btn btn-sm btn-outline-secondary ms-2">清除筛选</a>
              </div>
              @endif
              <form method="GET">
                <div class="row g-3">
                  <div class="col-md-3">
                    <label class="form-label">时间范围</label>
                    <select name="date_range" class="form-control">
                      <option value="1" {{ $dateRange == '1' ? 'selected' : '' }}>今天</option>
                      <option value="7" {{ $dateRange == '7' ? 'selected' : '' }}>最近7天</option>
                      <option value="30" {{ $dateRange == '30' ? 'selected' : '' }}>最近30天</option>
                      <option value="90" {{ $dateRange == '90' ? 'selected' : '' }}>最近90天</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">搜索用户</label>
                    <input type="text" name="search_user" class="form-control"
                      placeholder="搜索用户昵称"
                      value="{{ $searchUser }}">
                  </div>
                  <div class="col-md-3">
                    <label class="form-label">搜索视频</label>
                    <input type="text" name="search_video" class="form-control"
                      placeholder="搜索视频标题"
                      value="{{ $searchVideo }}">
                  </div>
                  <div class="col-md-3">
                    <label class="form-label text-white">筛选</label>
                    <button type="submit" class="form-control btn btn-primary">筛选</button>
                    @if($searchUser || $searchVideo)
                    <a href="{{ route('data-center.user-shares', request()->except(['search_user', 'search_video'])) }}"
                      class="btn btn-outline-secondary ms-2">清除搜索</a>
                    @endif
                  </div>
                </div>

              </form>
            </div>
          </div>
        </div>

        <!-- 详细分享记录 -->
        <div class="border-main rounded mt-1 bg-white p-3 p-lg-4">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="mb-0">详细分享记录</h6>
              <small class="text-muted">每个用户分享每个视频的详细记录</small>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>分享用户</th>
                      <th>分享内容</th>
                      <th>点击用户</th>
                      <th>分享状态</th>
                      <th>分享时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    @forelse ($shareRecords as $record)
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          @if($record->fromUser)
                          <img src="{{ $record->fromUser->full_avatar_url }}" class="rounded-circle me-2 w-8 h-8">
                          <div>
                            <div class="fw-bold">
                              <!-- {{ route('data-center.user-shares', ['user_id' => $record->fromUser->id] + request()->except(['user_id', 'diary_id'])) }} -->
                              <a href="{{ route('user-management.user.detail', $record->fromUser->id) }}" target="_blank"
                                class="text-decoration-underline"
                                title="筛选此用户的分享记录">
                                {{ $record->fromUser->nickname ?: '匿名用户' }}
                              </a>
                            </div>
                          </div>
                          @else
                          <div class="text-muted">
                            <i class="fas fa-user-slash me-2 "></i>
                            用户已删除
                          </div>
                          @endif
                        </div>
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div>
                            <div class="text-truncate" style="max-width: 250px;">
                              @if($record->share_type === 'nursing_home')
                              <strong class="text-info">机构主页</strong>
                              @if($record->nursingHome)
                              <span class="text-muted ms-2">{{ $record->nursingHome->name }}</span>
                              @else
                              <span class="text-muted ms-2">机构已删除</span>
                              @endif
                              @else
                              @if($record->diary)
                              <strong class="text-primary">视频：</strong>
                              <a href="{{ route('data-center.user-shares', ['diary_id' => $record->diary->id] + request()->except(['user_id', 'diary_id'])) }}"
                                class="text-decoration-none"
                                title="筛选此视频的分享记录">
                                {{ $record->diary->title }}
                              </a>
                              @else
                              <span class="text-muted">视频已删除</span>
                              @endif
                              @endif
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        @if($record->toUser)
                        <div class="d-flex align-items-center">
                          <img src="{{ $record->toUser->full_avatar_url }}" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                          <a href="{{ route('user-management.user.detail', $record->toUser->id) }}" target="_blank"
                            class="text-decoration-underline"
                            title="筛选此用户的分享记录">
                            {{ $record->toUser->nickname ?: '匿名用户' }}
                          </a>
                        </div>
                        @else
                        <span class="text-muted">
                          <i class="fas fa-clock me-1"></i>
                          等待点击
                        </span>
                        @endif
                      </td>
                      <td>
                        @if($record->to_user_id)
                        <span class="badge bg-success">
                          <i class="fas fa-check me-1"></i>
                          已观看
                        </span>
                        @else
                        <span class="badge bg-warning">
                          <i class="fas fa-share me-1"></i>
                          已分享
                        </span>
                        @endif
                      </td>
                      <td>
                        <div>{{ $record->created_at->format('m-d H:i') }}</div>
                        <small class="text-muted">{{ $record->created_at->diffForHumans() }}</small>
                      </td>
                    </tr>
                    @empty
                    <tr>
                      <td colspan="5" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <div>暂无分享记录</div>
                      </td>
                    </tr>
                    @endforelse
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              @if($shareRecords->hasPages())
              <div class="d-flex justify-content-center mt-3">
                {{ $shareRecords->appends(request()->query())->links() }}
              </div>
              @endif
            </div>
          </div>
        </div>
      </div>
      <!-- 统计信息 Tab -->
      <div class="tab-pane fade" id="statistics" role="tabpanel" aria-labelledby="statistics-tab">

        <!-- 统计卡片 -->
        <div class="mb-3 py-2 bg-white">
          <div class="row">
            <div class="col-lg-3 col-md-6">
              <div class="card border-primary">
                <div class="card-body text-center">
                  <h3 class="text-primary">{{ number_format($statistics['total_shares']) }}</h3>
                  <p class="card-text">总分享次数</p>
                  <small class="text-muted">
                    今日: {{ number_format($statistics['today_shares']) }} |
                    昨日: {{ number_format($statistics['yesterday_shares']) }}
                  </small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-success">
                <div class="card-body text-center">
                  <h3 class="text-success">{{ number_format($statistics['week_shares']) }}</h3>
                  <p class="card-text">最近一周分享次数</p>
                  <small class="text-muted">过去7天的分享量</small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-info">
                <div class="card-body text-center">
                  <h3 class="text-info">{{ number_format($statistics['total_clicks']) }}</h3>
                  <p class="card-text">总点击次数</p>
                  <small class="text-muted">今日: {{ number_format($statistics['today_clicks']) }}</small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-warning">
                <div class="card-body text-center">
                  <h3 class="text-warning">{{ $statistics['click_rate'] }}%</h3>
                  <p class="card-text">点击率</p>
                  <small class="text-muted">最近一周: {{ $statistics['week_click_rate'] }}%</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 额外统计信息 -->
        <div class="mb-3 py-2 bg-white">
          <div class="row">
            <div class="col-lg-3 col-md-6">
              <div class="card border-secondary">
                <div class="card-body text-center">
                  <h3 class="text-secondary">{{ number_format($statistics['active_sharers']) }}</h3>
                  <p class="card-text">活跃分享用户数</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-dark">
                <div class="card-body text-center">
                  <h3 class="text-dark">{{ number_format($statistics['shared_videos']) }}</h3>
                  <p class="card-text">被分享的视频数</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-info">
                <div class="card-body text-center">
                  <h3 class="text-info">{{ number_format($statistics['nursing_home_shares'] ?? 0) }}</h3>
                  <p class="card-text">机构主页分享次数</p>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-success">
                <div class="card-body text-center">
                  <h3 class="text-success">{{ number_format($statistics['nursing_home_clicks'] ?? 0) }}</h3>
                  <p class="card-text">机构主页点击次数</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 统计信息 Tab End -->
    </div>


  </div>
</div>
@endsection
