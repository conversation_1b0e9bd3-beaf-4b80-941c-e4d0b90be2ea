<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Config;
use Antto\Sms\Facades\Sms;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use App\Http\Controllers\Api\ApiController;
use Jiannei\Response\Laravel\Support\Facades\Response;

class AuthMbController extends ApiController
{
    /**
     * 发送验证码
     */
    public function sendVerifyCode(Request $request)
    {
        $request->validate(
            [
                'mobile'           => 'required|is_mobile|can_send',
            ],
            [
                'mobile.required'  => '手机号不能为空',
                'mobile.is_mobile' => '手机号格式不正确',
                'mobile.can_send'  => '验证码发送过于频繁',
            ]
        );

        $mobile = $request->mobile;

        // 测试手机号不发短信 START
        $whitelist = [];
        $result = Config::where('key', 'whitelist')->first();
        if ($result) {
            $whitelist = array_map('trim', explode("\n", $result->value1));
        }
        if (in_array($mobile, $whitelist)) {
            return Response::ok('发送成功（白名单）');
        }
        // 测试手机号不发短信 END

        if (Sms::sendVerifyCode($mobile)) {
            return Response::ok('发送成功');
        } else {
            Response::errorBadRequest('发送失败');
        }
    }

    /**
     * 手机号验证码登录
     */
    public function mobileLogin(Request $request)
    {
        $mobile = $request->mobile;

        // 测试手机号以可直接使用任意验证码登录
        $whitelist = [];
        $result = Config::where('key', 'whitelist')->first();
        if ($result) {
            $whitelist = array_map('trim', explode("\n", $result->value1));
        }

        if (in_array($mobile, $whitelist)) {
            $user = User::where('mobile', $mobile)->first();
            if (!$user) {
                // 没有用户就创建
                $fields = [
                    'mobile'      => $mobile,
                    'gender'      => 0,
                    'nickname'    => env('NAME_PREFIX', '用户') . time(),
                    'status'      => 1
                ];
                $user = User::create($fields);
            }
        } else if (Str::contains($mobile, '188888888') && $request->code == '8888') {
            $user = User::where('mobile', $mobile)->first();
        } else {
            $request->validate(
                [
                    'mobile' => 'required|is_mobile',
                    // 'code'   => 'required'
                    'code'   => 'required|verify_code:' . $request->mobile
                ],
                [
                    'mobile.required'  => '手机号不能为空',
                    'mobile.is_mobile' => '手机号格式不正确',
                    'code.required'    => '请输入验证码',
                    'code.verify_code' => '验证码错误'
                ]
            );

            $user = User::where('mobile', $mobile)->first();
            if (!$user) {
                // 没有用户就创建
                $fields = [
                    'mobile'      => $mobile,
                    'gender'      => 0,
                    'nickname'    => env('NAME_PREFIX', '用户') . time(),
                    'status'      => 1
                ];
                $user = User::create($fields);
            }
        }

        $token = $user->createToken('token-name');
        return Response::success([
            'user'       => new UserResource($user),
            'token'      => $token->plainTextToken
        ], '登录成功');
    }
}
