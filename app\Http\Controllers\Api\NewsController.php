<?php

namespace App\Http\Controllers\Api;

use App\Models\News;
use Illuminate\Http\Request;
use App\Http\Resources\NewsResource;

class NewsController extends ApiController
{
    /**
     * 1501-新闻列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 20;
        $category_id = $request->category_id;
        $category_ids = $request->category_ids; // 多个分类ID，逗号隔开
        $is_recommend = $request->is_recommend;

        $tag_id = $request->tag_id;
        $result = News::where('status', 1)
            ->when($category_id, function ($query) use ($category_id) {
                return $query->where('news_category_id', $category_id);
            })
            ->when($category_ids, function ($query) use ($category_ids) {
                return $query->whereIn('news_category_id', explode(',', $category_ids));
            })
            ->when($is_recommend, function ($query) use ($is_recommend) {
                return $query->where('is_recommend', $is_recommend);
            })
            ->when($tag_id, function ($query) use ($tag_id) {
                return $query->whereHas('tags', function ($query) use ($tag_id) {
                    $query->where('news_tag_id', $tag_id);
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
        return $this->success(NewsResource::collection($result));
    }

    /**
     * 1502-新闻详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $result = News::find($id);
        if ($result) {
            return $this->success(new NewsResource($result));
        }
        $this->errorBadRequest('暂无数据');
    }
}
