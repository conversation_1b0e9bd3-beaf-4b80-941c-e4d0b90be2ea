<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\Diary;
use Illuminate\Support\Str;
use Dcat\Admin\Widgets\Card;
use App\Admin\Repositories\Common;
use App\Admin\Repositories\Comment;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Comment as CommentModels;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Actions\Restore\BatchRestore;


class CommentController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Comment::with(['user', 'diary']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user.mobile', '用户手机号');
            $grid->column('content')->display(function () {
                return Str::limit($this->content, 80);
            })->modal(function ($modal) {
                $modal->title('内容');
                $card = new Card(null, $this->content);
                return "<div style='padding:10px 10px 0'>$card</div>";
            });
            $grid->column('diary_id')->display(function () {
                $text = optional($this->diary)->title;
                return Str::limit($text, 80);
            })->link(function () {
                return admin_url('diaries/?id=' . $this->diary_id);
            });
            $grid->column('comment_id')->display(function () {
                return $this->comment_id ? '#' . $this->comment_id : '';
            })->link(function () {
                return admin_url('comments/?id=' . $this->comment_id);
            });
            $grid->column('status')->using(Common::$activeMap)->badge(Common::$statusColor);

            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id')->width(3);
                $filter->equal('user_id')->select(User::all()->pluck('mobile', 'id'))->width(3);
                $filter->equal('diary_id')->select(Diary::all()->pluck('title', 'id'))->width(3);
                $filter->equal('comment_id')->select(CommentModels::all()->pluck('id', 'id'))->width(3);
                $filter->equal('status')->radio(['' => '全部'] + Common::$activeMap)->width(3);
                $filter->between('created_at')->datetime()->width(3);
                $filter->between('updated_at')->datetime()->width(3);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(CommentModels::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(CommentModels::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Comment(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('diary_id');
            $show->field('comment_id');
            $show->field('first_comment_id');
            $show->field('second_comment_id');
            $show->field('content');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Comment(), function (Form $form) {
            $form->display('id');
            $form->select('user_id')->options(User::all()->pluck('mobile', 'id'));
            $form->select('diary_id')->options(Diary::all()->pluck('title', 'id'));
            $form->text('comment_id');
            $form->hidden('first_comment_id');
            $form->hidden('second_comment_id');
            $form->textarea('content');
            $form->switch('status')->default(1);
            $form->select('nursing_home_id')->options(\App\Models\NursingHome::all()->pluck('name', 'id'))->help('养老院');

            $form->display('created_at');
            $form->display('updated_at');

            $form->saving(function (Form $form) {
                if ($form->isCreating() || $form->isEditing()) {

                    if ($form->user_id == null) {
                        $form->user_id = mt_rand(10001, 10070);
                    }

                    $comment = CommentModels::find($form->comment_id);
                    $first_comment_id = null;
                    $second_comment_id = null;

                    switch (true) {
                        case $comment && $comment->comment_id == null:
                            $first_comment_id = $comment->id;
                            break;
                        case $comment && $comment->comment_id != null:
                            $first_comment_id = $comment->first_comment_id;
                            break;
                        default:
                            break;
                    }

                    switch (true) {
                        case $comment && $comment->comment_id != null && $comment->second_comment_id == null:
                            $second_comment_id = $comment->id;
                            break;
                        case $comment && $comment->comment_id != null && $comment->second_comment_id != null:
                            $second_comment_id = $comment->second_comment_id;
                            break;
                        default:
                            break;
                    }

                    $form->first_comment_id  = $first_comment_id;
                    $form->second_comment_id = $second_comment_id;
                }
            });

            $form->deleted(function (Form $form, $result) {
                // 获取待删除行数据，这里获取的是一个二维数组
                $data = $form->model()->toArray();

                // 重新计算评论数
                $diary_id = $data[0]['diary_id'];
                $comment_number = CommentModels::where('diary_id', $diary_id)->count();
                Diary::where('id', $diary_id)->update(['comment_number' => $comment_number]);

                // 通过 $result 可以判断数据是否删除成功
                if (! $result) {
                    return $form->response()->error('数据删除失败');
                }

                // 返回删除成功提醒，此处跳转参数无效
                return $form->response()->success('删除成功');
            });
        });
    }
}
