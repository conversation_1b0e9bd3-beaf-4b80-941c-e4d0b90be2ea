<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view class="content container p-20 u-flex-col u-row-center" v-else-if="showSuccess">
      <!-- 签约成功信息 -->
      <view class="u-col-center u-col-center u-row-center">
        <view class="mb-20 px-60 u-font-18 u-text-center">付款金额</view>
        <view class="u-flex u-row-center mb-10 px-60 u-font-18">
          <text class="amount">￥<text>12,000</text></text>
        </view>
        <view class="mt-40 mb-20 px-60 u-font-18 u-text-center">{{ payment_description }}</view>
      </view>
      <view class="my-30">
        <view class="tip-wrap bg-fff py-30 px-10">
          <u-cell-group :border="false">
            <!-- 账号信息 -->
            <u-cell :border="false" title="公司名称" value="羊老师文化传播（上海）有限公司"></u-cell>
            <u-cell :border="false" title="开户行" value="招商银行股份有限公司上海徐汇滨江支行"></u-cell>
            <u-cell :border="false" title="银行帐号" value="***************"></u-cell>
          </u-cell-group>

          <view class="mt-30 pt-30 u-border-top">
            <u-row>
              <u-col span="8">
                <u--text text="付款时请备注机构名称" prefixIcon="info-circle" iconStyle="font-size: 18px; margin-right: 5px"
                  color="#000" size="15"></u--text>
              </u-col>
              <u-col span="4">
                <u-button color="#f5f5f5" customStyle="color: #000" shape="circle" text="复制账号信息"
                  @click="copyAccountInfo"></u-button>
              </u-col>
            </u-row>
          </view>
        </view>
      </view>

      <view class="px-80 mt-60 mb-20">
        <u-row justify="center" gutter="20">
          <u-col span="6">
            <u-button color="#F2AE1E" shape="circle" text="返回" @click="goBack"></u-button>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/common/api'
export default {
  data() {
    return {
      loading: false,
      showSuccess: false,
      signTimeText: '',
      payment_description: '',
    };
  },
  onLoad() {
    this.loadContractData();
    this.loadConfig();
  },
  methods: {
    async loadContractData() {
      this.loading = true;
      try {
        const res = await api.getContracts();
        if (res.data.status === 'success' && res.data.data) {
          const contract = res.data.data;

          // 检查是否已签约
          if (contract.status === 1) {
            this.showSuccess = true;
            this.signTimeText = contract.sign_at;
          } else {
            // 未签约，跳回第一步
            uni.showToast({
              title: '请先完成签约',
              icon: 'none',
              duration: 1500
            });
            setTimeout(() => {
              uni.redirectTo({
                url: '/pages/contract/form'
              });
            }, 1500);
          }
        } else {
          // 没有签约信息，跳回第一步
          uni.showToast({
            title: '请先填写签约信息',
            icon: 'none',
            duration: 1500
          });
          setTimeout(() => {
            uni.redirectTo({
              url: '/pages/contract/form'
            });
          }, 1500);
        }
      } catch (error) {
        console.error('加载签约信息失败:', error);
        // 发生错误时跳回第一步
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 1500
        });
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/contract/form'
          });
        }, 1500);
      } finally {
        this.loading = false;
      }
    },

    loadConfig() {
      api.getConfig("payment_description").then(res => {
        this.payment_description = res.data.data.value;
      });
    },

    goBack() {
      uni.navigateBack();
    },

    toUserCenter() {
      uni.navigateTo({
        url: '/pages/user/index'
      });
    },

    // 复制账号信息
    copyAccountInfo() {
      const accountInfo = `公司名称：羊老师文化传播（上海）有限公司
开户行：招商银行股份有限公司上海徐汇滨江支行
银行帐号：***************
付款时请备注机构名称`;

      uni.setClipboardData({
        data: accountInfo,
        success: () => {
          uni.showToast({
            title: '账号信息已复制',
            icon: 'success',
            duration: 2000
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
.content {
  min-height: 100vh;
  background-color: #F8F8F8;

  .checkmark img {
    width: 168upx;
    height: 168upx;
  }
}

.btn-grey {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
  color: #332c2b;
}

.tip-wrap {
  border-radius: 20upx;
}

.u-cell__value {
  font-size: 28upx !important;
  color: #000 !important;
}

.amount text {
  font-size: 68upx;
  font-weight: bold;
  color: #F2AE1E;
}
</style>
