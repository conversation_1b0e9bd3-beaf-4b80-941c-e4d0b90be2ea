<?php

namespace App\Admin\Controllers;

use App\Models\News;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Box;
use App\Models\NewsCategory;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use App\Admin\Metrics\Dashboard;
use App\Models\AdminNotification;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use App\Admin\Widgets\Charts\BarChart;
use Overtrue\LaravelWeChat\EasyWeChat;
use App\Services\QywxNotificationService;
use App\Admin\Widgets\Charts\NewsRecommend;
use App\Admin\Widgets\Charts\NewsStatistics;

class ToolController extends Controller
{

    public function index(Request $request, Content $content)
    {
        return $content
            ->header('工具合集')
            ->description('常用工具集合')
            ->body(admin_view('admin.tools.index'));
    }

    public function process($method, Request $request)
    {
        // 根据路由参数，请求对应的方法
        if (method_exists($this, $method)) {
            return $this->$method($request);
        }
        return $this->errorNotFound('请求方法不存在');
    }

    /**
     * 发邮件测试
     */
    public function email(Request $request)
    {

        $data = $request->only(['email']);

        try {
            $data['subject'] = '测试邮件';
            $data['content'] = '这是一封测试邮件';

            Mail::send('emails.test', ['content' => $data['content']], function ($message) use ($data) {
                $message->to($data['email'])
                    ->subject($data['subject']);
            });

            return response()->json(['status' => true, 'message' => '发送成功']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Redis 测试
     */
    public function redis(Request $request)
    {
        $data = $request->only(['key', 'value']);

        try {
            $redis = app('redis');
            $redis->set($data['key'], $data['value']);
            $storedValue = $redis->get($data['key']);

            return response()->json(['status' => true, 'message' => 'Redis 赋值成功：' . $storedValue]);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 测试队列服务
     */
    public function queue(Request $request)
    {
        try {
            // 这里添加测试队列服务的逻辑，例如分发一个测试任务到队列
            dispatch(new \App\Jobs\TestQueueJob());

            return response()->json(['status' => true, 'message' => '测试队列任务已成功分发，请查看日志记录。']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 生成小程序二维码
     */
    public function xcxqrcode(Request $request)
    {
        // 默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；
        // 为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用
        // $check_path = config('app.env') == 'production';
        $check_path = false;

        // $page = 'pages/webview/index';
        // $page = 'pages/login/fake'; // 模拟登录页面
        // $page = 'pages/onlineExhibition/index'; // 线上展览列表
        // $page = 'pages/onlineExhibition/detail'; // 线上展览

        $page = $request->input('page');
        if (!$page) {
            return response()->json(['status' => false, 'message' => "请输入页面路径"]);
        }

        // 判断环境 production 生成正式版，否则生成体验版
        // if (config('app.env') == 'production') {
        //     $env_version = 'release';
        // } else {
        //     $env_version = 'trial';
        // }
        $env_version = $request->input('env', 'trial');

        $app = EasyWeChat::miniApp();
        $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
            'scene'       => '1',
            'page'        => $page,
            'width'       => 600,
            'check_path'  => $check_path,
            'env_version' => $env_version,
        ]);

        if ($response) {

            // 直接输出图片到浏览器
            // header('Content-Type: image/png'); // 设置响应头为图片类型
            // header('Cache-Control: public');
            // echo $response->getContent();      // 输出图片内容
            // exit;

            // 返回Base64编码的图像数据
            $base64Image = base64_encode($response->getContent());
            $image_html = '<img src="data:image/png;base64,' . $base64Image . '" alt="二维码" />';
            return response()->json(['status' => true, 'message' => $image_html]);

            // 保存图片
            // if (!file_exists(storage_path('app/public/qrcode/'))) {
            //     mkdir(storage_path('app/public/qrcode/'), 0777, true);
            // }
            // $name = 'xcx_' . now()->format('Y_m_d_H_i_s') . '_' . Str::random(5) . '.png';
            // $filename = $response->saveAs(storage_path('app/public/qrcode/' . $name));
            // echo $filename;
        }
    }

    /**
     * 测试后台通知
     */
    public function notification(Request $request)
    {
        try {
            // 这里添加测试后台通知的逻辑，例如发送一条测试通知
            AdminNotification::create([
                'admin_user_id' => 1,
                'title'         => '测试通知',
                'content'       => '这是一条测试通知',
                'link'          => null,
                'is_read'       => 0,
                'type'          => 2,
            ]);
            return response()->json(['status' => true, 'message' => '测试通知已成功发送。']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 企业微信通知
     */
    public function qywxNotification(Request $request)
    {
        try {
            $content = $request->input('content');
            $msgtype = $request->input('msgtype', 'text');
            if (!$content) {
                return response()->json(['status' => false, 'message' => "请输入内容"]);
            }
            $qywxService = new QywxNotificationService();
            if ($msgtype == 'markdown') {
                $response = $qywxService->sendNotification($content);
            } else {
                $response = $qywxService->sendTextNotification($content);
            }

            if ($response->getStatusCode() == 200) {
                return response()->json(['status' => true, 'message' => '企业微信通知已成功发送。']);
            } else {
                return response()->json(['status' => false, 'message' => '企业微信通知发送失败。']);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

}
