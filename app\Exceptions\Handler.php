<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Services\QywxNotificationService;
use Jiannei\Response\Laravel\Support\Traits\ExceptionTrait;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Handler extends ExceptionHandler
{
    use ExceptionTrait;
    /**
     * A list of the exception types that are not reported.
     *
     * @var string[]
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var string[]
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $e
     * @return void
     *
     * @throws \Throwable
     */
    public function report(Throwable $e)
    {
        // 首先调用父类的 report 方法，确保异常被正常记录
        parent::report($e);

        // 检查异常是否在 $dontReport 列表中
        if ($this->shouldReport($e)) {

            // 如果是本地环境，不发送通知
            if (app()->environment('local')) {
                return;
            }

            // 如果是晚上23点到第二天8点之间，不发送通知
            if (now()->hour >= 23 || now()->hour < 8) {
                return;
            }

            // 获取上次发送通知的时间戳
            $lastNotificationTime = Cache::get('last_notification_time', 0);

            // 检查是否距离上次发送通知的时间超过20秒
            if (now()->timestamp - $lastNotificationTime >= 20) {
                // 发送企业微信通知
                try {
                    $qywxNotificationService = new QywxNotificationService();
                    $message = config('app.name') . config('app.env') . ": " .
                        $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
                    $qywxNotificationService->sendTextNotification($message);

                    // 更新上次发送通知的时间戳
                    Cache::put('last_notification_time', now()->timestamp, 20);
                } catch (Throwable $notificationException) {
                    // 捕获 sendTextNotification 的异常，避免嵌套
                    Log::error('Failed to send WeChat notification: ' . $notificationException->getMessage());
                }
            }
        }
    }
}
