<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\NursingHome;
use App\Services\OssService;
use App\Admin\Repositories\Diary;
use App\Admin\Repositories\Common;
use App\Admin\Renderable\UserTable;
use App\Models\Diary as DiaryModels;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Actions\Restore\BatchRestore;
use Dcat\Admin\Http\Controllers\AdminController;

class DiaryController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Diary::with(['nursingHome']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('nursingHome.name', '养老院');
            $grid->column('title');
            $grid->column('cover_url')->image('', 40, 40);
            $grid->column('likes_number');
            $grid->column('comment_number');
            $grid->column('browse_number');
            $grid->column('favorite_number');
            $grid->column('share_number');
            $grid->column('is_recommend')->using(Common::$yesNoMap)->badge(Common::$statusColor);
            $grid->column('status')->using(Diary::$statusMap)->badge(Diary::$statusColor);
            $grid->column('publish_at');
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('nursing_home_id')->select(NursingHome::all()->pluck('name', 'id'))->width(3);
                $filter->like('title')->width(3);
                $filter->equal('is_recommend')->radio(['' => '全部'] + Common::$yesNoMap)->width(3);
                $filter->equal('status')->radio(['' => '全部'] + Common::$activeMap)->width(3);
                $filter->between('publish_at')->datetime()->width(3);
                $filter->between('created_at')->datetime()->width(3);
                $filter->between('updated_at')->datetime()->width(3);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(DiaryModels::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(DiaryModels::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Diary(), function (Show $show) {
            $show->field('id');
            $show->field('nursing_home_id');
            $show->field('title');
            $show->field('cover_url');
            $show->field('cover_vertical_url');
            $show->field('upload_video_url');
            $show->field('video_url');
            $show->field('likes_number');
            $show->field('comment_number');
            $show->field('browse_number');
            $show->field('favorite_number');
            $show->field('share_number');
            $show->field('is_recommend');
            $show->field('publish_at');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(Diary::with(['nursingHome']), function (Form $form) {
            $form->display('id');

            $form->selectTable('user_id')
                ->title('发布人')
                ->dialogWidth('50%') // 弹窗宽度，默认 800px
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(\App\Models\User::class, 'id', 'nickname'); // 设置编辑数据显示

            $form->select('nursing_home_id')->options(NursingHome::all()->pluck('name', 'id'))->required();
            $form->text('title')->required();
            $form->image('cover_url')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：600x800")->autoUpload()->uniqueName();
            // $form->image('cover_vertical_url')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：600x800")->autoUpload()->uniqueName();
            // $form->file('upload_video_url')->saveFullUrl()->move('videos/' . date("Y/m"))->autoUpload()->uniqueName();

            // 改为阿里云oss直传
            $ossService = new OssService();
            $configs = $ossService->adminGetUploadPolicy('mp4');
            $full = $configs['full'];
            $filename = $configs['filename'];
            $policy = $configs['policy'];
            $OSSAccessKeyId = $configs['OSSAccessKeyId'];
            $signature = $configs['signature'];
            $callBackBase64 = $configs['callBackBase64'];

            $form->file('upload_video_url', '上传视频')
                ->disk('oss') // 更改储存驱动
                ->saveFullUrl()
                ->autoUpload() // 自动上传
                // ->accept('mp4') // 限制文件上传格式
                // ->help('仅支持MP4格式')
                ->maxSize(512000) // 更改文件最大限制
                ->removable(false) // 关闭页面删除
                ->url('https://yang-diary.oss-cn-shanghai.aliyuncs.com') // 设置上传地址
                // 自定义 webuploader 配置
                ->options(['fileVal' => 'file']) // Dcat 默认 name 为 _file_
                // 上传事件
                ->on(
                    'startUpload',
                    <<<JS
                                function () {
                                    console.log('文件开始上传...', this);
                                    // 上传文件前附加自定义参数到文件上传接口
                                    this.uploader.options.formData['key'] = '$full';
                                    this.uploader.options.formData['fileName'] = "$filename";
                                    this.uploader.options.formData['policy'] = '$policy';
                                    this.uploader.options.formData['OSSAccessKeyId'] = '$OSSAccessKeyId';
                                    this.uploader.options.formData['signature'] = '$signature';
                                    this.uploader.options.formData['success_action_status'] = "200";
                                    this.uploader.options.formData['callback'] = '$callBackBase64';
                                }
                            JS
                );
            $form->text('video_url')->help('视频地址，优先使用上传视频');
            $form->number('likes_number')->default(0)->min(0);
            $form->number('comment_number')->default(0)->min(0);
            $form->number('browse_number')->default(0)->min(0);
            $form->number('favorite_number')->default(0)->min(0);
            $form->number('share_number')->default(0)->min(0);
            $form->switch('is_recommend');
            $form->datetime('publish_at');
            $form->radio('status')->options(Diary::$statusMap)->default(0)->required();
            $form->display('created_at');
            $form->display('updated_at');

            $form->saving(function (Form $form) {

                // 如果 publish_at 为空，设置为当前时间
                if (!$form->publish_at) {
                    $form->publish_at = now();
                }

                // 如果video_url是空的，设置状态为1
                if (!$form->video_url) {
                    $form->status = 1;
                }
            });
        });
    }
}
