<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->string('video_thumb_url')->nullable()->comment('视频封面')->after('video_url');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->dropColumn('video_thumb_url');
        });
    }
};
