<?php

namespace Database\Seeders;

use Dcat\Admin\Models;
use Illuminate\Database\Seeder;
use DB;

class AdminTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // base tables
        Models\Menu::truncate();
        Models\Menu::insert(
            [
                [
                    "id" => 1,
                    "parent_id" => 0,
                    "order" => 1,
                    "title" => "仪表盘",
                    "icon" => "fa-dashboard",
                    "uri" => "/",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2023-11-09 17:36:31"
                ],
                [
                    "id" => 2,
                    "parent_id" => 0,
                    "order" => 22,
                    "title" => "Admin",
                    "icon" => "feather icon-settings",
                    "uri" => NULL,
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 3,
                    "parent_id" => 2,
                    "order" => 24,
                    "title" => "Users",
                    "icon" => "",
                    "uri" => "auth/users",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 4,
                    "parent_id" => 2,
                    "order" => 25,
                    "title" => "Roles",
                    "icon" => "",
                    "uri" => "auth/roles",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 5,
                    "parent_id" => 2,
                    "order" => 26,
                    "title" => "Permission",
                    "icon" => "",
                    "uri" => "auth/permissions",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 6,
                    "parent_id" => 2,
                    "order" => 27,
                    "title" => "Menu",
                    "icon" => "",
                    "uri" => "auth/menu",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 8,
                    "parent_id" => 9,
                    "order" => 13,
                    "title" => "操作日志",
                    "icon" => "fa-file-text-o",
                    "uri" => "auth/operation-logs",
                    "extension" => "dcat-admin.operation-log",
                    "show" => 1,
                    "created_at" => "2022-11-02 15:35:52",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 9,
                    "parent_id" => 0,
                    "order" => 12,
                    "title" => "日志记录",
                    "icon" => "fa-server",
                    "uri" => "log-viewer",
                    "extension" => "antto.dcat-log-viewer",
                    "show" => 1,
                    "created_at" => "2022-11-02 15:36:01",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 10,
                    "parent_id" => 0,
                    "order" => 2,
                    "title" => "用户管理",
                    "icon" => "fa-user-o",
                    "uri" => "users",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-11-02 16:00:07",
                    "updated_at" => "2022-11-02 16:02:56"
                ],
                [
                    "id" => 11,
                    "parent_id" => 0,
                    "order" => 6,
                    "title" => "页面管理",
                    "icon" => "fa-file-text-o",
                    "uri" => "pages",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-11-02 16:04:16",
                    "updated_at" => "2024-09-11 16:43:13"
                ],
                [
                    "id" => 15,
                    "parent_id" => 0,
                    "order" => 7,
                    "title" => "滑块管理",
                    "icon" => "fa-sliders",
                    "uri" => "sliders",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-11-02 16:10:36",
                    "updated_at" => "2024-09-11 16:43:13"
                ],
                [
                    "id" => 17,
                    "parent_id" => 2,
                    "order" => 23,
                    "title" => "配置管理",
                    "icon" => "fa-gears",
                    "uri" => "configs",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2022-12-14 17:02:57",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 18,
                    "parent_id" => 0,
                    "order" => 9,
                    "title" => "意见反馈",
                    "icon" => "fa-phone",
                    "uri" => "feedbacks",
                    "extension" => "",
                    "show" => 0,
                    "created_at" => "2023-10-20 14:42:17",
                    "updated_at" => "2025-03-26 11:49:02"
                ],
                [
                    "id" => 19,
                    "parent_id" => 9,
                    "order" => 14,
                    "title" => "系统日志",
                    "icon" => "fa-info-circle",
                    "uri" => "log-viewer",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2023-11-09 16:54:55",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 20,
                    "parent_id" => 2,
                    "order" => 28,
                    "title" => "环境",
                    "icon" => "fa-certificate",
                    "uri" => "environment",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2023-11-16 14:08:06",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 21,
                    "parent_id" => 9,
                    "order" => 15,
                    "title" => "短信日志",
                    "icon" => "fa-envelope-o",
                    "uri" => "sms-logs",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2023-11-16 16:06:25",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 22,
                    "parent_id" => 9,
                    "order" => 16,
                    "title" => "后台登录日志",
                    "icon" => "fa-list",
                    "uri" => "admin-login-logs",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-03-05 21:42:05",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 23,
                    "parent_id" => 0,
                    "order" => 17,
                    "title" => "数据统计",
                    "icon" => "fa-area-chart",
                    "uri" => NULL,
                    "extension" => "",
                    "show" => 0,
                    "created_at" => "2024-06-29 22:09:01",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 24,
                    "parent_id" => 23,
                    "order" => 19,
                    "title" => "饼图示例",
                    "icon" => "fa-pie-chart",
                    "uri" => "/statistics/pie",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-06-29 22:13:37",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 25,
                    "parent_id" => 23,
                    "order" => 21,
                    "title" => "表格示例",
                    "icon" => "fa-table",
                    "uri" => "statistics/table",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-06-29 23:11:12",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 26,
                    "parent_id" => 23,
                    "order" => 20,
                    "title" => "柱图示例",
                    "icon" => "fa-bar-chart",
                    "uri" => "statistics/bar",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-06-29 23:12:06",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 27,
                    "parent_id" => 23,
                    "order" => 18,
                    "title" => "数据统计",
                    "icon" => "fa-area-chart",
                    "uri" => "statistics",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-07-27 16:32:54",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 28,
                    "parent_id" => 0,
                    "order" => 3,
                    "title" => "养老院管理",
                    "icon" => "fa-home",
                    "uri" => "nursing-homes",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-09-11 16:11:19",
                    "updated_at" => "2024-09-11 16:11:25"
                ],
                [
                    "id" => 29,
                    "parent_id" => 0,
                    "order" => 4,
                    "title" => "日记管理",
                    "icon" => "fa-edit",
                    "uri" => "diaries",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-09-11 16:29:13",
                    "updated_at" => "2024-09-11 16:29:18"
                ],
                [
                    "id" => 30,
                    "parent_id" => 0,
                    "order" => 5,
                    "title" => "日记评论",
                    "icon" => "fa-commenting",
                    "uri" => "comments",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2024-09-11 16:43:06",
                    "updated_at" => "2024-09-11 16:43:13"
                ],
                [
                    "id" => 32,
                    "parent_id" => 0,
                    "order" => 8,
                    "title" => "消息管理",
                    "icon" => NULL,
                    "uri" => "nursing-home-contacts",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2025-03-26 11:25:03",
                    "updated_at" => "2025-03-26 11:48:48"
                ],
                [
                    "id" => 33,
                    "parent_id" => 0,
                    "order" => 10,
                    "title" => "签约管理",
                    "icon" => "fa-align-justify",
                    "uri" => "contracts",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2025-07-21 09:33:42",
                    "updated_at" => "2025-07-21 14:16:13"
                ],
                [
                    "id" => 34,
                    "parent_id" => 0,
                    "order" => 11,
                    "title" => "付款管理",
                    "icon" => "fa-dollar",
                    "uri" => "payments",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2025-07-25 09:10:44",
                    "updated_at" => "2025-07-25 09:10:52"
                ],
                [
                    "id" => 35,
                    "parent_id" => 0,
                    "order" => 29,
                    "title" => "发票管理",
                    "icon" => "fa-newspaper-o",
                    "uri" => "invoices",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2025-07-28 18:15:17",
                    "updated_at" => "2025-07-28 18:15:17"
                ],
                [
                    "id" => 36,
                    "parent_id" => 0,
                    "order" => 30,
                    "title" => "机构用户权限管理",
                    "icon" => "fa-user-plus",
                    "uri" => "institution-user-permissions",
                    "extension" => "",
                    "show" => 1,
                    "created_at" => "2025-07-28 18:15:50",
                    "updated_at" => "2025-07-28 18:15:50"
                ]
            ]
        );

        Models\Permission::truncate();
        Models\Permission::insert(
            [
                [
                    "id" => 1,
                    "name" => "Auth management",
                    "slug" => "auth-management",
                    "http_method" => "",
                    "http_path" => "",
                    "order" => 1,
                    "parent_id" => 0,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 2,
                    "name" => "Users",
                    "slug" => "users",
                    "http_method" => "",
                    "http_path" => "/auth/users*",
                    "order" => 2,
                    "parent_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 3,
                    "name" => "Roles",
                    "slug" => "roles",
                    "http_method" => "",
                    "http_path" => "/auth/roles*",
                    "order" => 3,
                    "parent_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 4,
                    "name" => "Permissions",
                    "slug" => "permissions",
                    "http_method" => "",
                    "http_path" => "/auth/permissions*",
                    "order" => 4,
                    "parent_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 5,
                    "name" => "Menu",
                    "slug" => "menu",
                    "http_method" => "",
                    "http_path" => "/auth/menu*",
                    "order" => 5,
                    "parent_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 6,
                    "name" => "Extension",
                    "slug" => "extension",
                    "http_method" => "",
                    "http_path" => "/auth/extensions*",
                    "order" => 6,
                    "parent_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => NULL
                ],
                [
                    "id" => 7,
                    "name" => "用户管理",
                    "slug" => "user",
                    "http_method" => "",
                    "http_path" => "",
                    "order" => 7,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 16:51:59",
                    "updated_at" => "2025-04-30 16:51:59"
                ],
                [
                    "id" => 8,
                    "name" => "查看用户",
                    "slug" => "users-view",
                    "http_method" => "GET",
                    "http_path" => "/users*",
                    "order" => 8,
                    "parent_id" => 7,
                    "created_at" => "2025-04-30 16:55:34",
                    "updated_at" => "2025-04-30 16:55:34"
                ],
                [
                    "id" => 9,
                    "name" => "养老院管理",
                    "slug" => "nursing-homes",
                    "http_method" => "GET",
                    "http_path" => "/nursing-homes*",
                    "order" => 9,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 17:00:13",
                    "updated_at" => "2025-04-30 17:00:22"
                ],
                [
                    "id" => 10,
                    "name" => "日记管理",
                    "slug" => "diaries",
                    "http_method" => "GET",
                    "http_path" => "/diaries*",
                    "order" => 10,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 17:01:57",
                    "updated_at" => "2025-04-30 17:01:57"
                ],
                [
                    "id" => 11,
                    "name" => "评论管理",
                    "slug" => "comments",
                    "http_method" => "GET",
                    "http_path" => "/comments*",
                    "order" => 11,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 17:02:16",
                    "updated_at" => "2025-04-30 17:02:16"
                ],
                [
                    "id" => 12,
                    "name" => "联系管理",
                    "slug" => "nursing-home-contacts",
                    "http_method" => "GET",
                    "http_path" => "/nursing-home-contacts*",
                    "order" => 12,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 17:02:53",
                    "updated_at" => "2025-04-30 17:02:53"
                ],
                [
                    "id" => 13,
                    "name" => "短信日志",
                    "slug" => "sms-logs",
                    "http_method" => "GET",
                    "http_path" => "/sms-logs*",
                    "order" => 13,
                    "parent_id" => 0,
                    "created_at" => "2025-04-30 17:09:18",
                    "updated_at" => "2025-04-30 17:09:46"
                ]
            ]
        );

        Models\Role::truncate();
        Models\Role::insert(
            [
                [
                    "id" => 1,
                    "name" => "Administrator",
                    "slug" => "administrator",
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2022-09-01 16:58:57"
                ],
                [
                    "id" => 2,
                    "name" => "readonly",
                    "slug" => "只读管理员",
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ]
            ]
        );

        Models\Setting::truncate();
		Models\Setting::insert(
			[

            ]
		);

		Models\Extension::truncate();
		Models\Extension::insert(
			[
                [
                    "id" => 1,
                    "name" => "dcat-admin.operation-log",
                    "version" => "1.0.0",
                    "is_enabled" => 1,
                    "options" => NULL,
                    "created_at" => "2022-11-02 15:35:52",
                    "updated_at" => "2022-11-02 15:36:45"
                ],
                [
                    "id" => 2,
                    "name" => "antto.dcat-log-viewer",
                    "version" => "1.0.0",
                    "is_enabled" => 1,
                    "options" => NULL,
                    "created_at" => "2022-11-02 15:36:01",
                    "updated_at" => "2022-11-02 15:36:45"
                ],
                [
                    "id" => 3,
                    "name" => "sparkinzy.dcat-distpicker",
                    "version" => "1.1.0",
                    "is_enabled" => 1,
                    "options" => NULL,
                    "created_at" => "2023-04-19 11:28:12",
                    "updated_at" => "2023-11-09 17:38:31"
                ]
            ]
		);

		Models\ExtensionHistory::truncate();
		Models\ExtensionHistory::insert(
			[
                [
                    "id" => 1,
                    "name" => "dcat-admin.operation-log",
                    "type" => 2,
                    "version" => "1.0.0",
                    "detail" => "create_opration_log_table.php",
                    "created_at" => "2022-11-02 15:35:52",
                    "updated_at" => "2022-11-02 15:35:52"
                ],
                [
                    "id" => 2,
                    "name" => "dcat-admin.operation-log",
                    "type" => 1,
                    "version" => "1.0.0",
                    "detail" => "Initialize extension.",
                    "created_at" => "2022-11-02 15:35:52",
                    "updated_at" => "2022-11-02 15:35:52"
                ],
                [
                    "id" => 3,
                    "name" => "antto.dcat-log-viewer",
                    "type" => 1,
                    "version" => "1.0.0",
                    "detail" => "Initialize extension.",
                    "created_at" => "2022-11-02 15:36:01",
                    "updated_at" => "2022-11-02 15:36:01"
                ],
                [
                    "id" => 4,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.0",
                    "detail" => "Initialize dcat-dispicker.",
                    "created_at" => "2023-04-19 11:28:11",
                    "updated_at" => "2023-04-19 11:28:11"
                ],
                [
                    "id" => 5,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.1",
                    "detail" => "修改文档",
                    "created_at" => "2023-04-19 11:28:12",
                    "updated_at" => "2023-04-19 11:28:12"
                ],
                [
                    "id" => 6,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.2",
                    "detail" => "修复js引用",
                    "created_at" => "2023-04-19 11:28:13",
                    "updated_at" => "2023-04-19 11:28:13"
                ],
                [
                    "id" => 7,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.3",
                    "detail" => "修复Grid filter",
                    "created_at" => "2023-04-19 11:28:13",
                    "updated_at" => "2023-04-19 11:28:13"
                ],
                [
                    "id" => 8,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.4",
                    "detail" => "修复Grid filter的一些问题",
                    "created_at" => "2023-04-19 11:28:14",
                    "updated_at" => "2023-04-19 11:28:14"
                ],
                [
                    "id" => 9,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.4",
                    "detail" => "通过level()控制展示的级别",
                    "created_at" => "2023-04-19 11:28:14",
                    "updated_at" => "2023-04-19 11:28:14"
                ],
                [
                    "id" => 10,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.5",
                    "detail" => "filter默认展开省市区",
                    "created_at" => "2023-04-19 11:28:14",
                    "updated_at" => "2023-04-19 11:28:14"
                ],
                [
                    "id" => 11,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.6",
                    "detail" => "修复表单编辑回显",
                    "created_at" => "2023-04-19 11:28:14",
                    "updated_at" => "2023-04-19 11:28:14"
                ],
                [
                    "id" => 12,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.7",
                    "detail" => "更新地址库至2021.10.10版本",
                    "created_at" => "2023-04-19 11:28:15",
                    "updated_at" => "2023-04-19 11:28:15"
                ],
                [
                    "id" => 13,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.8",
                    "detail" => "优化地址库",
                    "created_at" => "2023-04-19 11:28:15",
                    "updated_at" => "2023-04-19 11:28:15"
                ],
                [
                    "id" => 14,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.9",
                    "detail" => "优化地址库ID",
                    "created_at" => "2023-04-19 11:28:16",
                    "updated_at" => "2023-04-19 11:28:16"
                ],
                [
                    "id" => 15,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.10",
                    "detail" => "新增code筛选",
                    "created_at" => "2023-04-19 11:28:16",
                    "updated_at" => "2023-04-19 11:28:16"
                ],
                [
                    "id" => 16,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.0.10",
                    "detail" => "新增地域的数据库文件",
                    "created_at" => "2023-04-19 11:28:16",
                    "updated_at" => "2023-04-19 11:28:16"
                ],
                [
                    "id" => 17,
                    "name" => "sparkinzy.dcat-distpicker",
                    "type" => 1,
                    "version" => "1.1.0",
                    "detail" => "修复三级选项同时开启且无法关闭的问题",
                    "created_at" => "2023-11-09 17:38:31",
                    "updated_at" => "2023-11-09 17:38:31"
                ]
            ]
		);

        // pivot tables
        DB::table('admin_permission_menu')->truncate();
		DB::table('admin_permission_menu')->insert(
			[

            ]
		);

        DB::table('admin_role_menu')->truncate();
        DB::table('admin_role_menu')->insert(
            [
                [
                    "role_id" => 1,
                    "menu_id" => 2,
                    "created_at" => "2025-04-30 17:10:24",
                    "updated_at" => "2025-04-30 17:10:24"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 8,
                    "created_at" => "2025-04-30 17:08:25",
                    "updated_at" => "2025-04-30 17:08:25"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 11,
                    "created_at" => "2025-04-30 17:07:58",
                    "updated_at" => "2025-04-30 17:07:58"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 15,
                    "created_at" => "2025-04-30 17:08:09",
                    "updated_at" => "2025-04-30 17:08:09"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 19,
                    "created_at" => "2025-04-30 17:08:29",
                    "updated_at" => "2025-04-30 17:08:29"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 22,
                    "created_at" => "2025-04-30 17:08:34",
                    "updated_at" => "2025-04-30 17:08:34"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 34,
                    "created_at" => "2025-07-25 09:10:44",
                    "updated_at" => "2025-07-25 09:10:44"
                ],
                [
                    "role_id" => 1,
                    "menu_id" => 35,
                    "created_at" => "2025-07-28 18:15:17",
                    "updated_at" => "2025-07-28 18:15:17"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 1,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 9,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 10,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 18,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 21,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 28,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 29,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 30,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ],
                [
                    "role_id" => 2,
                    "menu_id" => 32,
                    "created_at" => "2025-04-30 16:50:35",
                    "updated_at" => "2025-04-30 16:50:35"
                ]
            ]
        );

        DB::table('admin_role_permissions')->truncate();
        DB::table('admin_role_permissions')->insert(
            [
                [
                    "role_id" => 2,
                    "permission_id" => 8,
                    "created_at" => "2025-04-30 16:57:52",
                    "updated_at" => "2025-04-30 16:57:52"
                ],
                [
                    "role_id" => 2,
                    "permission_id" => 9,
                    "created_at" => "2025-04-30 17:00:49",
                    "updated_at" => "2025-04-30 17:00:49"
                ],
                [
                    "role_id" => 2,
                    "permission_id" => 10,
                    "created_at" => "2025-04-30 17:03:32",
                    "updated_at" => "2025-04-30 17:03:32"
                ],
                [
                    "role_id" => 2,
                    "permission_id" => 11,
                    "created_at" => "2025-04-30 17:03:32",
                    "updated_at" => "2025-04-30 17:03:32"
                ],
                [
                    "role_id" => 2,
                    "permission_id" => 12,
                    "created_at" => "2025-04-30 17:03:32",
                    "updated_at" => "2025-04-30 17:03:32"
                ],
                [
                    "role_id" => 2,
                    "permission_id" => 13,
                    "created_at" => "2025-04-30 17:10:09",
                    "updated_at" => "2025-04-30 17:10:09"
                ]
            ]
        );

        // users tables
        Models\Administrator::truncate();
        Models\Administrator::insert(
            [
                [
                    "id" => 1,
                    "username" => "admin",
                    "password" => "\$2y\$10\$/umnAMhMDJaD4eB7JH8ejOE9aySnPzQPFWS.meplW6S5/qopA/sW.",
                    "name" => "Administrator",
                    "avatar" => NULL,
                    "remember_token" => "XaYp76orJg0XxP6UyRznJJ6xFBbGoqPYdD1HqlKXZamA4tYFyBymc6b2j6Qe",
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2024-09-14 11:31:13"
                ],
                [
                    "id" => 2,
                    "username" => "gongan",
                    "password" => "\$2y\$10\$6XnhUWfF50XTRCxjgPtQpevllEIET4J2QSzgNjUWY6DK5yf4NCrDm",
                    "name" => "公安安全审核",
                    "avatar" => NULL,
                    "remember_token" => NULL,
                    "created_at" => "2024-12-20 13:40:20",
                    "updated_at" => "2024-12-20 13:40:20"
                ],
                [
                    "id" => 3,
                    "username" => "laolin",
                    "password" => "\$2y\$10\$LEGnOXfuDimeHZs2FZzkne47US5exazcrqxPG6Hosnil6LhbVxRFa",
                    "name" => "管理员",
                    "avatar" => NULL,
                    "remember_token" => NULL,
                    "created_at" => "2025-04-30 16:57:11",
                    "updated_at" => "2025-04-30 16:57:11"
                ],
                [
                    "id" => 4,
                    "username" => "jiang",
                    "password" => "\$2y\$10\$qmKsOgt0AvDqFkVQgdwAWO/VMBodp/witiFX6q4sQYXXfjMlv2iOi",
                    "name" => "姜",
                    "avatar" => NULL,
                    "remember_token" => "RfWGD6Iihb12S2p30uS9frQRpiQYb0VXSqD5ClMGM6z85rmAsmUhXzWKjG8Z",
                    "created_at" => "2025-07-29 10:56:43",
                    "updated_at" => "2025-07-29 10:56:43"
                ]
            ]
        );

        DB::table('admin_role_users')->truncate();
        DB::table('admin_role_users')->insert(
            [
                [
                    "role_id" => 1,
                    "user_id" => 1,
                    "created_at" => "2022-09-01 16:58:57",
                    "updated_at" => "2022-09-01 16:58:57"
                ],
                [
                    "role_id" => 1,
                    "user_id" => 2,
                    "created_at" => "2024-12-20 13:40:20",
                    "updated_at" => "2024-12-20 13:40:20"
                ],
                [
                    "role_id" => 1,
                    "user_id" => 4,
                    "created_at" => "2025-07-29 10:56:43",
                    "updated_at" => "2025-07-29 10:56:43"
                ],
                [
                    "role_id" => 2,
                    "user_id" => 3,
                    "created_at" => "2025-04-30 16:57:11",
                    "updated_at" => "2025-04-30 16:57:11"
                ]
            ]
        );

        // finish
    }
}
