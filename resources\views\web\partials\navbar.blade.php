<!-- Navigation -->
<nav class="main-navbar navbar navbar-expand-md bg-white" id="mainNav">
  <div class="container-fluid py-3 py-sm-0 pe-sm-0">
    <a class="navbar-brand p-sm-0" href="{{ route('home') }}">
      <img class="img-fluid" src="{{ asset('images/logo.svg') }}" alt="">
    </a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
      aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>

    <div class="navbar-collapse" id="navbarSupportedContent">

      <ul class="navbar-nav ms-auto right-entry">
        <li class="nav-item px-2">
          <a href="{{ url('privacy-policy') }}" class="nav-link">
            隐私条款
          </a>
        </li>
        <li class="nav-item px-2">
          <a href="{{ route('contact') }}" class="nav-link">
            联系我们
          </a>
        </li>

        @if (!request()->user())
        <li class="nav-item nav-tiem-login">
          <a class="nav-link px-3" href="{{ route('login') }}">
            <i class="bi bi-person-fill me-1"></i> 登录
          </a>
        </li>
        @else
        <!-- 管理首页 -->
        <li class="nav-item px-2">
          <a href="{{ url('dashboard') }}" class="nav-link">
            <i class="bi bi-columns-gap me-1"></i>
            {{ request()->user()->managedNursingHome->name }}
            管理中心
          </a>
        </li>
        <!-- 个人（机构）中心 -->

        <li class="nav-item dropdown">

          <a class="nav-link px-4" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <span class="d-flex align-items-center dropdown-toggle">
              <img class="img-fluid rounded-circle auth-avator-img me-2" src="{{ asset('images/app-logo.svg') }}" alt="">
              <span class="align-middle">{{ request()->user()->nickname ? request()->user()->nickname : request()->user()->name }}</span>
            </span>
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="{{ route('nursing-home.index') }}">机构信息</a></li>
            <li class="dropdown-item">
              <a class="nav-link" href="{{ route('logout') }}"
                onclick="event.preventDefault(); document.getElementById('logout-form').submit();"><i
                  class="bi bi-box-arrow-right me-1"></i>退出</a>
              <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                {{ csrf_field() }}
              </form>
            </li>
          </ul>
        </li>

        <!-- <li class="nav-item">
          <a href="{{ route('nursing-home.index') }}" class="nav-link px-3">
            <i class="bi bi-person-square me-1"></i>机构信息
          </a>
        </li>
        <li class="nav-item">
          <a href="{{ route('nursing-home.contacts') }}" class="nav-link px-3">
            <i class="bi bi-megaphone me-1"></i>消息
          </a>
        </li>
        <li class="nav-item">
          <a href="{{ route('diary.list') }}" class="nav-link px-3">
            <i class="bi bi-lightbulb me-1"></i>创作中心
          </a>
        </li> -->

        @endif
      </ul>

    </div>
  </div>
</nav>
