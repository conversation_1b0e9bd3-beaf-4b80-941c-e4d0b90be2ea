<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class UserLike extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'user_likes';

    protected $fillable = [
        'user_id',
        'diary_id',
        'nursing_home_id',
    ];

    public function diary()
    {
        return $this->belongsTo(Diary::class, 'diary_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
