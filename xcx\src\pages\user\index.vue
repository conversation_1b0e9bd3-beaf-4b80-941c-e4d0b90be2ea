<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />
    <bind-phone-modal />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view class="container u-p-b-20" v-else>
      <view class="card">
        <view class="user-top u-flex u-row-between mb-30">
          <view class="u-flex flex-1">
            <view class="user-avatar" @click="goEdit">
              <u-avatar
                :src="user.avatar_url"
                size="82"
                shape="square"
                default-url="/static/icon-video/default-avatar.png"
              ></u-avatar>
            </view>
            <view class="user-info flex-1" @click="goEdit" v-if="isLogin">
              <view class="user-nickname mb-10">
                <u--text size="22" :lines="1" :text="user.nickname"></u--text>
              </view>
              <view class="user-mobile">
                <u--text size="16" :text="user.mobile"></u--text>
                <!-- 销售tag -->
                <view class="sale-tag mt-6" v-if="user.role == 2">
                  <u--text size="12" :text="'机构管理员'"></u--text>
                </view>
                <view class="sale-tag mt-6" v-if="user.role == 3">
                  <u--text size="12" :text="'销售'"></u--text>
                </view>
              </view>
            </view>
            <view class="login-btn-wrap" v-if="!isLogin">
              <u-button
                shape="circle"
                size="large"
                type="info"
                @click="goAuthorize"
              >
                用户登录
              </u-button>
            </view>
          </view>
          <u-icon
            name="arrow-right"
            size="20"
            @click="goEdit"
            v-if="isLogin"
          ></u-icon>
        </view>
        <view class="user-tips" v-if="nicknameStatus">
          <u--text
            prefixIcon="info-circle"
            iconStyle="font-size: 20px; margin-right: 6px"
            text="补全昵称和头像，获得更好的使用体验。"
          ></u--text>
        </view>
      </view>

      <!-- 切换养老院 -->
      <view class="card change-nursing-home" v-if="nursingHomeList.length > 0">
        <view class="change-nursing-home-img">
          <image src="@/static/images/switch-agency.png" mode="widthFix" />
        </view>
        <view class="change-nursing-home-text pt-30">
          <picker
            :range="nursingHomeList"
            range-key="label"
            @change="changeNursingHome"
          >
            <view class="w-100 u-flex u-row-right">
              {{ nursing_home_name || "请选择" }}
              <u-icon name="arrow-right" size="22"></u-icon>
            </view>
          </picker>
        </view>
      </view>

      <view class="card py-10 px-0">
        <u-cell-group :border="false" :customStyle="{ fontSize: '30rpx' }">
          <u-cell
            title="开通机构账户"
            v-if="user.role == 1"
            isLink
            size="large"
            :border="false"
            url="/pages/contract/form"
          >
            <u-icon
              slot="icon"
              name="integral"
              color="#000"
              size="22"
            ></u-icon>
          </u-cell>
          <u-cell
            title="邀请管理员"
            v-if="user.role == 2"
            isLink
            size="large"
            :border="false"
            url="/pages/institution/invite"
          >
            <u-icon
              slot="icon"
              name="man-add"
              color="#000"
              size="22"
            ></u-icon>
          </u-cell>
          <u-cell
            title="我的收藏"
            isLink
            size="large"
            :border="false"
            url="/pages/user/collections"
          >
            <u-icon slot="icon" name="star" color="#000" size="22"></u-icon>
          </u-cell>
          <u-cell
            title="关于"
            isLink
            size="large"
            :border="false"
            url="/pages/page/index?permalink=about"
          >
            <u-icon slot="icon" name="setting" color="#000" size="22"></u-icon>
          </u-cell>

          <u-cell
            title="使用条款"
            isLink
            size="large"
            :border="false"
            url="/pages/page/index?permalink=terms"
          >
            <u-icon
              slot="icon"
              name="file-text"
              color="#000"
              size="22"
            ></u-icon>
          </u-cell>

          <u-cell
            title="隐私政策"
            isLink
            size="large"
            :border="false"
            url="/pages/page/index?permalink=privacy-policy"
          >
            <u-icon
              slot="icon"
              name="info-circle"
              color="#000"
              size="22"
            ></u-icon>
          </u-cell>

          <u-cell
            v-if="isLogin"
            title="退出登录"
            isLink
            size="large"
            :border="false"
            @click="logOut"
          >
            <u-icon
              slot="icon"
              name="backspace"
              color="#000"
              size="22"
            ></u-icon>
          </u-cell>
        </u-cell-group>
      </view>

      <view
        v-if="versionCode"
        class="u-text-center u-font-sm fc-ccc"
        @click="updateApp"
        >版本号: v{{ versionCode }}</view
      >

      <!-- 退出成功弹窗 -->
      <u-modal
        :show="logoutModeDialogShow"
        :showCancelButton="false"
        @confirm="gotoHome"
        title="温馨提示"
        content="您已退出当前账号。"
      ></u-modal>
    </view>
  </view>
</template>

<script>
import api from "@/common/api";
export default {
  data() {
    return {
      loading: false,
      user: {},
      isLogin: false,
      version: "",
      nicknameStatus: false,
      logoutModeDialogShow: false,
      versionCode: "",
      nursingHomeList: [],
      nursing_home_name: "",
      nursing_home_id: 0,
      currentNursingHomeIndex: 0,
    };
  },
  onShow() {
    // 更新状态示例
    // console.log(this.$store.getters.getStatus.user)
    // this.$store.dispatch('setStatus', {user: false})
    if (uni.getStorageSync("token")) {
      this.loadData();
      this.isLogin = true;
    } else {
      this.isLogin = false;
      this.loading = false;
    }
  },
  onLoad() {
    this.getVersionCode();
  },
  onReady() {},
  methods: {
    getNursingHomeList() {
      api.userNursingHomeList().then((res) => {
        this.nursingHomeList = res.data.data;
        // 查找当前用户机构在列表中的索引
        this.currentNursingHomeIndex = 0; // 默认为0
        if (this.nursing_home_id && res.data.data.length > 0) {
          const currentIndex = res.data.data.findIndex(
            (item) => item.id === this.nursing_home_id
          );
          if (currentIndex !== -1) {
            this.currentNursingHomeIndex = currentIndex;
          }
        }
      });
    },

    changeNursingHome(e) {
      let id = this.nursingHomeList[e.detail.value].id;
      let label = this.nursingHomeList[e.detail.value].label;
      api
        .changeNursingHome({
          nursing_home_id: id,
        })
        .then((res) => {
          this.nursing_home_id = id;
          this.nursing_home_name = label;
          this.loadData();
          this.$refs.uToast.show({
            message: res.data.message,
          });
        });
    },

    getVersionCode() {
      uni.getSystemInfo({
        success: (res) => {
          this.versionCode = res.appVersion; // 赋值版本号
        },
        fail: (err) => {
          console.error("获取系统信息失败:", err);
        },
      });
    },

    goLogin() {
      this.login();
      uni.navigateTo({ url: "/pages/login/index" });
      return;
    },

    loadData() {
      uni.showLoading({
        mask: true,
      });
      api.getCurrentDetail().then((res) => {
        this.loading = false;
        let userInfo = res.data.data;
        if (!this.$u.test.isEmpty(userInfo)) {
          this.user = userInfo;
          this.nursing_home_name = userInfo.nursing_home_name;
          this.nursing_home_id = userInfo.nursing_home_id;

          // 更新store中的用户信息
          this.$store.commit("SET_USER", userInfo);

          this.nicknameStatus =
            this.user.nickname.indexOf("用户") == 0 ? true : false;

          // 在用户信息加载完成后获取机构列表
          this.getNursingHomeList();
        }
        this.$nextTick(() => {
          uni.hideLoading();
        });
      });
    },
    toTeamList(index) {
      uni.navigateTo({ url: "/pages/team/index?index=" + index });
    },
    logOut() {
      uni.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        asyncConfirm: true,
        asyncCancel: true,
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            if (this.user?.xcx_openid) {
              uni.showToast({
                mask: false,
                icon: "success",
                title: "退出成功",
                duration: 1500,
              });
            }
            this.isLogin = false;
            uni.removeStorageSync("userInfo");
            uni.removeStorageSync("token");
            uni.removeStorageSync("user");
            this.user = {};

            // 退出成功弹出成功消息弹窗
            this.logoutModeDialogShow = true;
          }
        },
      });
    },
    // 跳转首页
    gotoHome() {
      uni.navigateTo({ url: "/pages/index/index" });
    },

    updateApp() {
      const updateManager = uni.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            uni.showModal({
              title: "更新提示",
              content: "新版本已经准备好，是否重启应用？",
              success: function (res) {
                if (res.confirm) {
                  updateManager.applyUpdate();
                }
              },
            });
          });
          updateManager.onUpdateFailed(function () {
            uni.showModal({
              title: "已经有新版本了哟~",
              content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~",
            });
          });
        } else {
          uni.showToast({
            title: "已经是最新版本了！",
            icon: "none",
            mask: true,
            duration: 800,
          });
        }
      });
    },
    async goAuthorize() {
      // #ifdef MP-WEIXIN
      this.login();
      // #endif
      // #ifndef MP-WEIXIN
      uni.navigateTo({ url: "/pages/login/index" });
      // #endif
      return;
    },
    async login() {
      let login = await uni.login();
      if (login[1].code) {
        let res = await api.getTokenXcx({
          data: {
            code: login[1].code,
          },
          method: "POST",
        });
        if (res.data.code == 200) {
          let resData = res.data.data;
          uni.setStorageSync("token", resData.token);
          this.$store.commit("SET_USER", resData.user);
          switch (process.env.VUE_APP_USER_UNIQUE_KEY) {
            case "openid":
              if (
                !resData.user.mobile &&
                !this.$store.getters.getShowBindModal
              ) {
                this.$store.commit("TOGGLE_BIND_MODAL", true);
              }
              break;
            case "mobile":
              if (!resData.token && !this.$store.getters.getShowBindModal) {
                this.$store.commit("TOGGLE_BIND_MODAL", true);
              }
              break;
            default:
              break;
          }
          if (uni.getStorageSync("token")) {
            uni.redirectTo({
              url: "/pages/user/index",
              fail(res) {
                uni.reLaunch({
                  url: "/pages/user/index",
                });
              },
            });
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      }
    },
    goEdit() {
      if (this.isLogin) {
        uni.navigateTo({
          url: "/pages/user/edit",
        });
      }
    },
    goMy() {
      if (this.isLogin) {
        uni.navigateTo({
          url: "/pages/user/NursingHome",
        });
      }
    },
    goFav() {
      if (this.isLogin) {
        uni.navigateTo({
          url: "/pages/user/collections",
        });
      }
    },
    goFol() {
      if (this.isLogin) {
        uni.navigateTo({
          url: "/pages/user/follows",
        });
      }
    },
  },
  onPullDownRefresh() {
    if (uni.getStorageSync("token")) {
      this.loadData();
    }
    uni.stopPullDownRefresh();
  },
};
</script>

<style lang="scss">
.page {
  padding-bottom: 100rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.user-top {
  .user-avatar {
    line-height: 0;
    margin-right: 28upx;
    border: #ddd solid 1upx;
    border-radius: 20upx;
    overflow: hidden;
  }
  .user-info {
    flex: 1;
    .user-nickName {
      font-size: 38upx;
    }
  }
}
.user-tips {
  font-size: 26upx;
}
.sale-tag {
  display: inline-block;
  padding: 4upx 10upx;
  border-radius: 10upx;
  border: #999 solid 1upx;
  background-color: #fff;
}
.link-list-wrap {
  padding-bottom: 50upx;
}
.contact-btn::after {
  content: "";
  border: none !important;
  outline: none !important;
}
.u-col {
  text-align: center !important;
}
.favoriteCount,
.subscriberCount {
  font-size: 50upx;
  font-weight: bold;
}
.py-2 {
  padding-top: 20upx;
  padding-bottom: 20upx;
}
.border-bottom {
  border-bottom: 2upx solid #ddd;
}
.organization-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}
.institution-bg {
  width: 140rpx;
  height: 140rpx;
}
.institution {
  background-color: #f2ae1e;
}
.bg-transparent {
  background-color: transparent;
}
.flex-1 {
  flex: 1;
}
.institution-btn {
  color: #fff;
  background-color: #f2ae1e;
  border-radius: 32px;
  border-color: #fff;
  border: 4rpx solid;
  font-size: 30rpx;
}
.sale-code-wrap {
  width: 600rpx;
  padding: 40rpx;
}
.sale-code-img-wrap {
  width: 100%;
  line-height: 0;
}
.sale-code-tip-text {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}
.change-nursing-home {
  background-color: transparent;
  position: relative;
  padding: 0;
  overflow: hidden;
  .change-nursing-home-img {
    line-height: 0;
    width: 100%;
    image {
      width: 100%;
    }
  }
  .change-nursing-home-text {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 30rpx;
    .change-nursing-home-text-title {
      font-size: 28rpx;
      color: #333;
      margin-right: 10rpx;
      max-width: 300rpx;
    }
  }
}
</style>
