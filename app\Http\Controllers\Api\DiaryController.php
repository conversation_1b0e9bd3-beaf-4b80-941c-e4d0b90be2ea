<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Diary;
use App\Models\Comment;
use App\Models\UserLike;
use App\Models\UserFollow;
use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\UserCollection;
use App\Models\UserBrowseRecord;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\DiaryResource;
use Illuminate\Support\Facades\Redis;
use App\Http\Resources\CommentResource;
use App\Http\Resources\DiaryListResource;
use App\Http\Resources\DiaryShortResource;
use Illuminate\Pagination\LengthAwarePaginator;

class DiaryController extends ApiController
{

    /**
     * 公用的查询方法
     */
    private function getDiaries()
    {
        return Diary::published()
            ->with('nursingHome');
    }

    /**
     * 列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = [];
        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
        }

        $limit = $request->limit ?? 20;
        $keyword = $request->keyword;
        $longitude = $request->longitude;
        $latitude = $request->latitude;
        $not_short = $request->not_short ?? 0;
        $is_user_collect = $request->is_user_collect ?? 0;
        $nursing_home_id = $request->nursing_home_id ?? 0;
        $nursingHomes = [];
        $is_random = $request->is_random ?? 0;

        // type 1-关注 2-推荐 3-附近
        $type = $request->type ?? 0;

        if ($type == 3 && $longitude && $latitude) {
            $nursingHomes = NursingHome::whereNotNull('longitude')->whereNotNull('latitude')
                ->selectRaw('*, round((
                    6370.996 * acos (
                    cos ( radians( ? ) )
                    * cos( radians( latitude ) )
                    * cos( radians( longitude ) - radians( ? ) )
                    + sin ( radians( ? ) )
                    * sin( radians( latitude ) )
                    )
                    ), 1) AS distance', [$latitude, $longitude, $latitude])
                ->orderBy('distance')->limit(100)->pluck('id')->toArray();
        }

        // 通过关键字查询养老院
        $keyword_nursingHome_ids = [];
        if ($keyword) {
            $keyword_nursingHome_ids = NursingHome::where('name', 'like', "%$keyword%")->pluck('id')->toArray();
        }

        $result = Diary::where('status', 3)
            ->where(function ($query) {
                $query->whereNull('publish_at')
                    ->orWhere('publish_at', '<=', now());
            })
            ->with('nursingHome')
            ->when($keyword, function ($query) use ($keyword, $keyword_nursingHome_ids) {
                return $query->where(function ($query) use ($keyword, $keyword_nursingHome_ids) {
                    $query->where('title', 'like', "%$keyword%")
                        ->orWhereIn('nursing_home_id', $keyword_nursingHome_ids);
                });
            })
            ->when($type == 1, function ($query) use ($user) {
                return $query->whereIn('nursing_home_id', $user->follows()->pluck('nursing_home_id')->toArray());
            })
            ->when($type == 2, function ($query) {
                return $query->orderBy('is_recommend', 'desc');
            })
            ->when($type == 3, function ($query) use ($nursingHomes) {
                return $query->whereIn('nursing_home_id', $nursingHomes);
            })
            ->when($is_user_collect, function ($query) use ($user) {
                return $query->whereIn('id', $user->collections()->pluck('diary_id')->toArray());
            })
            ->when($nursing_home_id, function ($query) use ($nursing_home_id) {
                return $query->where('nursing_home_id', $nursing_home_id);
            })
            ->when($is_random, function ($query) {
                return $query->inRandomOrder();
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit);

        if ($not_short) {
            foreach ($result as $key => $value) {
                if (auth('api')->check()) {
                    // 是否关注
                    $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                    // 是否收藏
                    $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                    // 是否点赞
                    $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                }
            }
            return $this->success(DiaryResource::collection($result));
        }

        return $this->success(DiaryShortResource::collection($result));
    }

    /**
     * 播放列表
     *
     * @param  int  $id
     * @param  \Illuminate\Http\Request  $request
     */
    public function playlist($id, Request $request)
    {

        $limit = $request->limit ?? 20;
        $nursing_home_id = $request->nursing_home_id ?? '';

        $result = Diary::with('nursingHome')->where('id', $id)->first();

        if (!$result) {
            $this->errorBadRequest('暂无数据');
        }

        $playlist = Diary::with('nursingHome')
            ->where('status', 3)
            ->when($nursing_home_id, function ($query) use ($nursing_home_id) {
                return $query->where('nursing_home_id', $nursing_home_id);
            })
            ->where(function ($query) {
                $query->whereNull('publish_at')
                    ->orWhere('publish_at', '<=', now());
            })
            ->where('id', '!=', $id)
            ->inRandomOrder()
            ->paginate($limit);

        $playlist->prepend($result);

        if (auth('api')->check()) {
            $user = User::find(auth('api')->id());
            foreach ($playlist as $key => $value) {
                // 是否关注
                $value->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $value->nursing_home_id)->exists();
                // 是否收藏
                $value->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
                // 是否点赞
                $value->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $value->id)->exists();
            }
        }

        return $this->success(DiaryListResource::collection($playlist));
    }

    /**
     * 详情
     */
    public function show($id, Request $request)
    {
        $user = $request->user();
        $result = Diary::with('nursingHome')
            ->where('status', 3)
            ->where(function ($query) {
                $query->whereNull('publish_at')
                    ->orWhere('publish_at', '<=', now());
            })
            ->where('id', $id)
            ->first();
        if ($result) {
            $result->increment('browse_number'); // 浏览量 +1

            if (auth('api')->check()) {
                // 是否关注
                $result->is_follow = UserFollow::where('user_id', $user->id)->where('nursing_home_id', $result->nursing_home_id)->exists();
                // 是否收藏
                $result->is_collected = UserCollection::where('user_id', $user->id)->where('diary_id', $result->id)->exists();
                // 是否点赞
                $result->is_liked = UserLike::where('user_id', $user->id)->where('diary_id', $result->id)->exists();
            }
            return $this->success(new DiaryResource($result));
        }
        $this->errorBadRequest('暂无数据');
    }

    /**
     * 点赞
     *
     * @param  $request
     */
    public function like(Request $request)
    {
        $request->validate(
            [
                'diary_id' => 'required|integer',
            ],
            [
                'diary_id.required' => '日记ID不能为空',
                'diary_id.integer' => '日记ID格式错误',
            ]
        );
        $user = $request->user();
        $diaryId = $request->diary_id;
        $diary = Diary::find($diaryId);
        $is_double = $request->is_double ?? 0;
        if ($diary) {
            $like = UserLike::where('user_id', $user->id)->where('diary_id', $diaryId)->first();
            if ($like) {
                if (!$is_double) {
                    $like->delete();
                    if ($diary->likes_number > 0) {
                        $diary->decrement('likes_number');
                    }
                    return $this->ok('取消点赞成功');
                }
                return $this->ok('双击点赞');
            } else {
                UserLike::create([
                    'user_id' => $user->id,
                    'diary_id' => $diary->id,
                ]);
                $diary->increment('likes_number');
                return $this->ok('点赞成功');
            }
        }
        $this->errorBadRequest('日记不存在');
    }

    /**
     * 收藏
     *
     * @param  $request
     */
    public function collection(Request $request)
    {
        $request->validate(
            [
                'diary_id' => 'required|integer',
            ],
            [
                'diary_id.required' => '日记ID不能为空',
                'diary_id.integer' => '日记ID格式错误',
            ]
        );
        $user = $request->user();
        $diaryId = $request->diary_id;
        $diary = Diary::find($diaryId);
        if ($diary) {
            $collection = UserCollection::where('user_id', $user->id)->where('diary_id', $diaryId)->first();
            if ($collection) {
                $collection->delete();
                if ($diary->favorite_number > 0) {
                    $diary->decrement('favorite_number');
                }
                return $this->ok('取消收藏成功');
            } else {
                UserCollection::create([
                    'user_id' => $user->id,
                    'diary_id' => $diary->id,
                ]);
                $diary->increment('favorite_number');
                return $this->ok('收藏成功');
            }
        }
        $this->errorBadRequest('日记不存在');
    }

    /**
     * 【废弃】分享
     *
     * @param  $request
     * @return \Illuminate\Http\Response
     */
    public function share(Request $request)
    {
        $diaryId = $request->diary_id;
        if ($diaryId) {
            $diary = Diary::find($diaryId);
            if ($diary) {
                $diary->increment('share_number');
            }
        }
    }

    /**
     * 记录用户浏览行为
     * 前端在视频播放2秒后调用此接口
     *
     * @param Request $request
     */
    public function recordBrowse(Request $request)
    {
        $request->validate([
            'diary_id' => 'required|integer|exists:diaries,id',
            'browse_duration' => 'integer|min:0',
            'device_type' => 'string|in:mobile,desktop,tablet',
            'platform' => 'string|max:50',
        ], [
            'diary_id.required' => '日记ID不能为空',
            'diary_id.integer' => '日记ID格式错误',
            'diary_id.exists' => '日记不存在',
            'browse_duration.integer' => '浏览时长必须为整数',
            'browse_duration.min' => '浏览时长不能为负数',
            'device_type.in' => '设备类型无效',
            'platform.max' => '平台名称过长',
        ]);

        $user = $request->user();
        $diaryId = $request->diary_id;
        $browseDuration = $request->browse_duration ?? 2; // 默认2秒

        // 获取日记信息
        $diary = Diary::find($diaryId);
        if (!$diary) {
            return $this->errorBadRequest('日记不存在');
        }

        // 获取设备和平台信息
        $userAgent = $request->header('User-Agent');
        $deviceType = $request->device_type ?? $this->detectDeviceType($userAgent);
        $platform = $request->platform ?? $this->detectPlatform($userAgent);

        // 检查是否已有浏览记录（同一用户同一日记在5分钟内的记录视为同一次浏览）
        $existingRecord = null;
        if ($user) {
            $existingRecord = UserBrowseRecord::where('user_id', $user->id)
                ->where('diary_id', $diaryId)
                ->where('created_at', '>=', now()->subMinutes(5))
                ->orderBy('created_at', 'desc')
                ->first();
        }

        if ($existingRecord) {
            // 更新现有记录的浏览时长
            $existingRecord->update([
                'browse_duration' => max($existingRecord->browse_duration, $browseDuration),
                'browse_ended_at' => now(),
            ]);

            return $this->success([
                'record_id' => $existingRecord->id,
                'total_duration' => $existingRecord->browse_duration,
            ], '浏览记录已更新');
        } else {
            // 创建新的浏览记录
            $browseRecord = UserBrowseRecord::create([
                'user_id' => $user ? $user->id : null,
                'diary_id' => $diaryId,
                'nursing_home_id' => $diary->nursing_home_id,
                'browse_duration' => $browseDuration,
                'ip_address' => $request->ip(),
                'user_agent' => $userAgent,
                'device_type' => $deviceType,
                'platform' => $platform,
                'browse_started_at' => now(),
                'browse_ended_at' => now(),
            ]);

            return $this->success([
                'record_id' => $browseRecord->id,
                'duration' => $browseDuration,
            ], '浏览记录已保存');
        }
    }

    /**
     * 检测设备类型
     */
    private function detectDeviceType($userAgent)
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        return 'desktop';
    }

    /**
     * 检测平台类型
     */
    private function detectPlatform($userAgent)
    {
        if (preg_match('/iPhone|iOS/', $userAgent)) {
            return 'iOS';
        } elseif (preg_match('/Android/', $userAgent)) {
            return 'Android';
        } elseif (preg_match('/MicroMessenger/', $userAgent)) {
            if (preg_match('/miniProgram/', $userAgent)) {
                return 'MiniProgram';
            }
            return 'WeChat';
        }
        return 'Web';
    }
}
