<?php
namespace App\Http\Controllers\Web;

use App\Models\News;
use App\Http\Controllers\Web\WebController;

class NewsController extends WebController
{
    /**
     * 列表
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $results = News::where('status', 1)->paginate(9);
        return view('news.index', compact('results'));
    }

    /**
     * 详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $item = News::find($id);
        return view('news.detail', compact('item'));
    }
}
