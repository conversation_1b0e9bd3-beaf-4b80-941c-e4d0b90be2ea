<?php
namespace App\Http\Controllers\Api;

use Image;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Api\ApiController;
use Rolandstarke\Thumbnail\Facades\Thumbnail;

class ImagesController extends ApiController
{
    public function index(Request $request)
    {
        // Log::info('上传图片', $request->all());

        // 默认生成缩略图
        // $use_crop = true;

        $index = $request->index ? $request->index : '0';
        $allowed_ext = ["png", "jpg", "gif", 'jpeg'];

        $folder = $request->type ? $request->type : 'images';
        $folder_name = $folder . '/' . date("Y/m");

        $image_path = '';
        if ($request->file('image')) {
            $file = $request->file('image');

            // 获取文件的后缀名，因图片从剪贴板里黏贴时后缀名为空，所以此处确保后缀一直存在
            $extension = strtolower($file->getClientOriginalExtension()) ?: 'png';

            // 如果上传的不是图片将终止操作
            if (!in_array($extension, $allowed_ext)) {
                return false;
            }

            $filename = time() . '_' . Str::random(10) . '.' . $extension;

            $image_path = $file->storeAs($folder_name, $filename, 'oss');
            $full_image = Storage::disk('oss')->url($image_path);

            $return_data = [
                // 'image'      => $image_path,
                'image'      => $full_image,
                'full_image' => $full_image,
                'index'      => $index
            ];
            // if ($use_crop) {
            //     $thumb_url = Thumbnail::src($image_path, 'public')->url(true);
            //     $return_data['thumb_url'] = $thumb_url;
            // }
            return $this->success($return_data);
        }
    }
}
