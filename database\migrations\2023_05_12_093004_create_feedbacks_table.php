<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFeedbacksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feedbacks', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable()->comment('用户ID');
            $table->string('email')->nullable()->comment('邮箱');
            $table->text('message')->nullable()->comment('意见');
            $table->text('image_urls')->nullable()->comment('图片');
            $table->tinyInteger('status')->default('0')->comment('状态: 0 未读, 1 已读');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feedbacks');
    }
}
