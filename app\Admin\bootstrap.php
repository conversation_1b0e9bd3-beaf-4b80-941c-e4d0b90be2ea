<?php

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Filter;
use Dcat\Admin\Form\Field\Editor;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 *
 * extend custom field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Column::extend('php', PHPEditor::class);
 * Dcat\Admin\Grid\Filter::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */
Admin::css('css/admin.css');
Grid::resolving(function (Grid $grid) {
    $grid->disableViewButton();

    // 将操作按钮修改为直接显示
    $grid->setActionClass(Grid\Displayers\Actions::class);

    $grid->filter(function (Grid\Filter $filter) {
        $filter->panel();
    });
});

Form::resolving(function (Form $form) {
    $form->disableEditingCheck();
    $form->disableCreatingCheck();
    $form->disableViewCheck();
    $form->tools(function (Form\Tools $tools) {
        // $tools->disableDelete();
        $tools->disableView();
        // $tools->disableList();
    });
});

Editor::resolving(function (Editor $editor) {
    $editor->imageDirectory('editor/images');
});

// 扩展：隐藏的“等于”筛选
Filter::extend('hiddenEqual', HiddenEqual::class);
