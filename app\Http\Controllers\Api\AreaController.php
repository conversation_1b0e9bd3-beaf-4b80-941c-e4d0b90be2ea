<?php

namespace App\Http\Controllers\Api;

use App\Models\ChinaArea;
use Illuminate\Http\Request;
use App\Http\Resources\AreaResource;
use App\Http\Resources\AreaTreeResource;
use App\Http\Controllers\Api\ApiController;

class AreaController extends ApiController
{
    /**
     * 地区列表
     */
    public function index(Request $request)
    {
        $pid = $request->pid;
        $list = ChinaArea::query()
            ->when($pid, function ($query) use ($pid) {
                $query->where('pid', $pid);
            })
            ->get();
        return $this->success(AreaResource::collection($list));
    }

    /**
     * 树列表
     */
    public function tree()
    {
        $list = ChinaArea::with('cities.counties')->where('pid', 1)->get();
        return $this->success(AreaTreeResource::collection($list));
        // return response()->json(AreaTreeResource::collection($list))->setEncodingOptions(JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取单独城市
     */
    public function city()
    {
        $list = ChinaArea::query()
            ->where('lat', '!=', '')
            ->where('level', 2)
            ->orWhere(function ($query) {
                $query->where('level', 3)->where('name', 'like', '%市%');
            })
            ->orderBy('pinyin', 'asc')
            ->get();
        return $this->success(AreaResource::collection($list));
    }
}
