<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use App\Models\AdminLoginLog;
use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;

class AuthController extends BaseAuthController
{
    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function sendLoginResponse(Request $request)
    {
        $request->session()->regenerate();

        $path = $this->getRedirectPath();

        try {
            AdminLoginLog::create([
                'admin_id' => Admin::user()->id,
                'ip' => getClientIp(),
            ]);
        } catch (\Throwable $th) {
            report($th);
        }

        return $this->response()
            ->success(trans('admin.login_successful'))
            ->location($path)
            ->locationIf(Admin::app()->getEnabledApps(), $path)
            ->send();
    }
}
