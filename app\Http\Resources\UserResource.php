<?php

namespace App\Http\Resources;

use App\Models\NursingHome;
use App\Models\UserNursingHome;
use Illuminate\Http\Resources\Json\JsonResource;


class UserResource extends JsonResource
{
    /**
     * Undocumented function
     *
     * @param [type] $request
     */
    public function toArray($request)
    {
        // $userNursingHome = UserNursingHome::where('user_id', $this->id)->first();
        // if ($userNursingHome) {
        //     $user_nursing_home_id = $userNursingHome->nursing_home_id;
        // } else {
        //     $user_nursing_home_id = 0;
        // }

        $nursing_home_name = '';
        $is_nursing_home_valid = 1; // 默认合法
        if ($this->nursing_home_id) {
            $nursing_home = NursingHome::where('id', $this->nursing_home_id)->first();
            $nursing_home_name = optional($nursing_home)->name ?? '';

            // 判断机构会员资格是否过期
            if ($nursing_home && $nursing_home->expired_at) {
                // 如果过期时间小于当前时间，则为非法（过期）
                $is_nursing_home_valid = $nursing_home->expired_at < now() ? 0 : 1;
            }
        }

        $manage_nursing_home_name = '';
        $is_nursing_home_valid = 1; // 默认合法
        if ($this->nursing_home_id) {
            $nursing_home = NursingHome::where('id', $this->nursing_home_id)->first();
            $manage_nursing_home_name = optional($nursing_home)->name ?? '';

        }

        return [
            'id'                   => $this->id,
            'xcx_code'             => $this->role == 3 ? $this->xcx_code_url : '',
            'xcx_openid'           => $this->xcx_openid,
            'name'                 => $this->name ?? '',
            'real_name'            => $this->real_name ?? '',
            'mobile'               => $this->mobile ? maskMobile($this->mobile) : '',
            'nickname'             => $this->nickname ?? '',
            'avatar_url'           => $this->full_avatar_url,
            'gender'               => $this->gender,
            'birthdate'            => $this->birthdate ?? '',
            'province'             => $this->province ?? '',
            'city'                 => $this->city ?? '',
            'district'             => $this->district ?? '',
            'role'                 => $this->role,
            'nursing_home_id'      => $this->nursing_home_id ?? 0,
            'nursing_home_name'    => $nursing_home_name,
            'is_nursing_home_valid'=> $is_nursing_home_valid,
            'user_nursing_home_id' => $this->nursing_home_id ?? 0,

            'manage_nursing_home_id' => $this->manage_nursing_home_id ?? 0,
            'manage_nursing_home_name' => $this->manage_nursing_home_name,
            // manage_nursing_home_id对应的机构名称

            'collect_count'        => $this->collections_count ?? 0,
            'nursingHomes_count'   => $this->nursing_homes_count ?? 0,
            'follow_count'         => $this->follows_count ?? 0,
            'status'               => $this->status,
            'created_at'           => optional($this->created_at)->toDateTimeString(),
        ];
    }
}
