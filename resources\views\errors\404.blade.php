@extends('layouts.web')

@section('title', '页面未找到')

@section('content')
<div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
  <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">404</h1>
      <h2 class="text-xl font-semibold text-gray-700 mb-4">页面未找到</h2>
      <div class="text-gray-600 mb-6">
        <p>抱歉，您访问的页面不存在。</p>
        <p>请检查网址是否正确，或返回首页。</p>
      </div>

      <div class="py-5 text-center">
        <svg style="width: 4rem; height: 4rem;" class="d-inline-block text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.816-6.207-2.175.193-.39.398-.774.618-1.15A8.962 8.962 0 0112 13.5c2.34 0 4.5-.816 6.207-2.175-.193-.39-.398-.774-.618-1.15A7.962 7.962 0 0112 12z"></path>
        </svg>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4 mt-6">
        <a href="{{ route('home') }}" class="btn btn-primary px-4 py-2 transition">
          返回首页
        </a>
        <button onclick="history.back()" class="btn btn-outline-primary px-4 py-2 transition">
          返回上页
        </button>
      </div>
    </div>
  </div>
</div>
@endsection
