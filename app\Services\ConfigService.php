<?php
namespace App\Services;

class ConfigService
{
    public function getValue($config)
    {
        $value = '';
        $value_text = '';
        switch ($config->type) {
            case 1:
                $value = $config->value1;
                break;
            case 2:
                $value = $config->value2;
                break;
            case 3:
                $value = $config->full_image_url;
                break;
            case 4:
                $value = $config->full_image_urls;
                break;
            case 5:
                $value = $config->value5;
                break;
            case 6:
                $value = $config->value6;
                break;
            case 7:
                $value = $config->value7;
                $value_text = optional($config->value7)->format('Y年m月d日');
                break;
            case 8:
                $value = $config->value8;
                $value_text = optional($config->value7)->format('Y年m月d日 h:i:s');
                break;
            case 9:
                $value = $config->value9;
                break;
            default:
                break;
        }
        return [
            'title'      => $config->title,
            'key'        => $config->key,
            'type'       => $config->type,
            'value'      => $value,
            'value_text' => $value_text
        ];
    }
}

