<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('institution_user_permissions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('nursing_home_id');
            $table->boolean('can_manage_staff')->default(false);
            $table->boolean('can_manage_info')->default(false);
            $table->boolean('can_manage_videos')->default(false);
            $table->boolean('can_view_data')->default(false);
            $table->boolean('can_manage_finance')->default(false);
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('nursing_home_id')->references('id')->on('nursing_homes')->onDelete('cascade');
            
            $table->unique(['user_id', 'nursing_home_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('institution_user_permissions');
    }
};