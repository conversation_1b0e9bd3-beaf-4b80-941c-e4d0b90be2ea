<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CommentResource extends JsonResource
{
    public function toArray($request)
    {
        $reply = null;
        if ($request->comment_id && (optional($this->comment)->id != $request->comment_id || $request->complete)) {
            $reply = new CommentReplyResource($this->comment);
        }
        return [
            'id'                 => $this->id,
            'user_id'            => $this->user_id,
            'user'               => $this->whenLoaded('user') ? new UserResource($this->user): '',
            'content'            => $this->content,
            'sub_comments'       => CommentReplyResource::collection($this->whenLoaded('subComments')),
            'sub_comments_count' => $this->when($this->second_comments_count, $this->second_comments_count),
            'reply'              => $reply,
            'created_at_'         => optional($this->created_at)->format('m-d'),
            'created_at'         => optional($this->created_at)->toDateTimeString(),
            'updated_at'         => optional($this->updated_at)->toDateTimeString(),
        ];
    }
}
