<?php

namespace App\Http\Controllers\Web;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;

class VideoController extends WebController
{
    public function input(Request $request)
    {
        // \Log::info($request->all());

        $preview = $config = $errors = [];

        if (!$request->file('upload_video_url')) {
            return [];
        }

        $folder_name = "videos/" . date("Y/m");
        $files = $request->file('upload_video_url');
        foreach ($files as $key => $file) {
            // 获取文件的后缀名
            $extension = strtolower($file->getClientOriginalExtension()) ?: '';

            $filename = time() . '_' . Str::random(10) . '.' . $extension;
            $file_path = $file->storeAs($folder_name, $filename, 'oss');
            $full_path = \Storage::disk('oss')->url($file_path);

            $preview[] = $full_path;

            $config[] = [
                'type'    => 'video',
                'filetype'  => "video/mp4",
                'key'     => $file->getClientOriginalName() . $key,
                'caption' => $file->getClientOriginalName(),
                'size'    => $file->getSize(),
            ];
        }

        $out = ['initialPreview' => $preview, 'initialPreviewConfig' => $config, 'initialPreviewAsData' => true];
        if (!empty($errors)) {
            $out['error'] = '上传失败，请稍后重试。';
        }
        return $out;
    }

    public function delete(Request $request)
    {
        // \Log::info($request->all());

        $file_path = $request->input('key');
        \Storage::disk('oss')->delete($file_path);

        return ['success' => true];
    }
}
