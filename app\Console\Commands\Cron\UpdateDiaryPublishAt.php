<?php

namespace App\Console\Commands\Cron;

use \Exception;
use App\Models\Diary;
use Illuminate\Console\Command;

class UpdateDiaryPublishAt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:update-diary-publish-at';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新日记发布时间';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始更新发布时间');
        $diaries = Diary::where('publish_at', null)->cursor();
        foreach ($diaries as $diary) {
            if ($diary->created_at) {
                $diary->publish_at = $diary->created_at;
                $diary->save();
            }
        }
        $this->info('结束更新发布时间');
        return 0;
    }

}
