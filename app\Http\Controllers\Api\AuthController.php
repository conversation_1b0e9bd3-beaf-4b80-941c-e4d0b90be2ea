<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\Api\ApiController;
use Antto\Sms\Facades\Sms;

class AuthController extends ApiController
{
    /**
     * @var bool
     * 延时创建开关，获取到微信信息后是否创建用户，在系统有其他注册用户途径时关闭，避免重复创建用户
     * true: 先不创建用户
     * false: 先创建用户
     */
    protected $DELAYCREATE = false;

    /**
     * 功能有：
     * 假冒登录(test)
     * 登出(logout)
     * 手机号密码登录(mobilePasswordLogin)
     * 邮箱密码登录(emialPasswordLogin)
     * 小程序授权(xcxAuth)
     * 小程序绑定手机(xcxBindMobile)
     * 微信H5授权(wxAuth)
     * 微信绑定手机(wxBindMobile)
     * 用户注册(register)
     * 邮箱注册(emailRegister)
     * 手机号注册(mobileRegister)
     */

    /**
     * 假冒登录
     */
    public function test(Request $request)
    {
        // 生产环境，直接退出
        if (app()->environment('production')) {
            return $this->errorBadRequest('错误的请求');
        }

        if ($request->user_id) {
            $user = User::find($request->user_id);
        } else {
            $user = User::first();
        }
        if ($user) {
            $token = $user->createToken('token-name');
            return $this->success([
                'user'  => new UserResource($user),
                'token' => $token->plainTextToken
            ]);
        }
        $this->errorBadRequest('用户不存在，假冒登录失败');
    }


    /**
     * 模拟登录
     * 用于生产环境模拟用户登录
     */
    public function fake(Request $request)
    {

        // 生产环境，直接退出
        if (app()->environment('production')) {
            $this->errorBadRequest('错误的请求');
        }

        // $user = $request->user();
        // if (!$user) {
        //     $this->errorBadRequest('请登录后使用');
        // }

        // // 用户手机号在白名单
        // $authorized_list = [
        //     '18888888888',
        //     '13788921860',
        //     '15900694860',
        // ];
        // if (!in_array($user->mobile, $authorized_list)) {
        //     // Log::error($user->mobile . '用户手机号不在白名单');
        //     $this->errorBadRequest('无权限');
        // }


        $fake_user_id = $request->user_id;
        if ($fake_user_id) {
            $user = User::find($fake_user_id);
        }

        if ($user) {
            $token = $user->createToken('token-name');
            return $this->success([
                'user'  => new UserResource($user),
                'token' => $token->plainTextToken
            ]);
        }
        $this->errorBadRequest('用户不存在，假冒登录失败');
    }

    /**
     * 登出
     * Token失效
     * @return void
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        return $this->ok('退出成功');
    }


    /**
     * 用户名密码登录
     *
     * @return void
     */
    public function usernamePasswordLogin(Request $request)
    {
        $request->validate(
            [
                'username'   => 'required',
                'password' => 'required'
            ],
            [
                'username.required'   => '请输入用户名',
                'password.required' => '请输入密码',
            ]
        );
        $user = User::where('username', $request->username)->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            $this->errorBadRequest('用户名或密码不正确');
        }
        if ($user->status === 0) {
            $this->errorBadRequest('账号暂未激活，请联系管理员');
        }
        $token = $user->createToken('token-name');
        return $this->success([
            'user'  => new UserResource($user),
            'token' => $token->plainTextToken
        ]);
    }

    /**
     * 手机号密码登录
     *
     * @return void
     */
    public function mobilePasswordLogin(Request $request)
    {
        $request->validate(
            [
                'mobile'   => 'required|zh_mobile',
                'password' => 'required'
            ],
            [
                'mobile.required'   => '请输入手机号',
                'mobile.zh_mobile'  => '请输入正确的手机号',
                'password.required' => '请输入密码',
            ]
        );
        $user = User::where('mobile', $request->mobile)->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            $this->errorBadRequest('手机号或密码不正确');
        }
        if ($user->status === 0) {
            $this->errorBadRequest('账号暂未激活，请联系管理员');
        }
        $token = $user->createToken('token-name');
        return $this->success([
            'user'  => new UserResource($user),
            'token' => $token->plainTextToken
        ]);
    }

    /**
     * 邮箱密码登录
     *
     * @return void
     */
    public function emialPasswordLogin(Request $request)
    {
        $request->validate(
            [
                'email'    => 'required|email',
                'password' => 'required'
            ],
            [
                'email.required'    => '请输入邮箱',
                'email.email'       => '邮箱格式不正确',
                'password.required' => '请输入密码',
            ]
        );
        $user = User::where('email', $request->email)->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            $this->errorBadRequest('邮箱或密码不正确');
        }
        if ($user->status === 0) {
            $this->errorBadRequest('账号暂未激活，请联系管理员');
        }
        $token = $user->createToken('token-name');
        return $this->success([
            'user'  => new UserResource($user),
            'token' => $token->plainTextToken
        ]);
    }

    /**
     * 小程序授权
     *
     * https://github.com/overtrue/laravel-wechat
     *
     * @return void
     */
    public function xcxAuth(Request $request)
    {

        // 测试环境跳过授权，直接返回用户信息
        // if (app()->environment('local', 'staging')) {
        //     $user = User::find(env('FAKE_USER_ID', 1));
        //     $token = $user->createToken('token-name');
        //     return $this->success([
        //         'user'  => new UserResource($user),
        //         'token' => $token->plainTextToken
        //     ]);
        // }

        $request->validate(
            [
                'code'    => 'required',
            ],
            [
                'code.required'    => '获取凭证失败',
            ]
        );

        // 获取openid
        $miniProgram = \EasyWeChat::miniProgram();
        try {
            $result = $miniProgram->auth->session($request->code);
        } catch (\Exception $e) {
            \Log::error('小程序授权发生错误');
            report($e);
            $this->errorBadRequest('发生错误，请稍后重试');
        }
        if (!isset($result['openid'])) {
            \Log::error("小程序授权解析" . json_encode($result));
            $this->errorBadRequest('授权失败，请重新进入小程序');
        }
        $openid = $result['openid'];

        $user = User::where('xcx_openid', $openid)->first();
        if (!$user) {
            // 不存在用户
            if ($this->DELAYCREATE) {
                // 不创建用户
                return $this->success([
                    'session_info' => $result,
                    'user'       => ['status' => 0],
                    'token'      => '',
                ]);
            } else {
                // 创建用户
                $fields = [
                    'nickname'    => env('NAME_PREFIX', '用户') . time(),
                    'avatar_url'  => 'avatars/default-avatar.png',
                    'xcx_openid'  => $openid,
                    'status'      => 1
                ];
                $user = User::create($fields);
            }
        }

        if ($user) {
            $token = $user->createToken('token-name');
            return $this->success([
                'session_info' => $result,
                'user'         => new UserResource($user),
                'token'        => $token->plainTextToken
            ]);
        }
    }


    /**
     * 微信H5授权
     * 未关注用户，不可用静默授权（获取不到openid）
     * https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html#3
     * https://github.com/overtrue/laravel-wechat
     * https://easywechat.vercel.app/6.x/common/oauth.html#%E5%BE%AE%E4%BF%A1-oauth
     *
     * @return void
     */
    public function wxAuth(Request $request)
    {
        $request->validate(
            [
                'code' => 'required'
            ],
            [
                'code.required'    => '获取code失败'
            ]
        );
        // 解密信息
        $app = \EasyWeChat::officialAccount();
        $result = $app->oauth->userFromCode($request->code);
        $openid = $result->getId();
        // TIP: 未关注用户，不可用静默授权（获取不到openid）

        $fields = [];
        $user = User::whereNotNull('wx_openid')->where('wx_openid', $openid)->first();
        if ($user) {
            // 存在用户则更新用户信息
            if (isset($result['token_response']) && $result['token_response']['scope'] == 'snsapi_base') {
                // 静默授权
                if (!$user->oauth_scope) {
                    // 不存在授权类型就更新
                    $fields['oauth_scope'] = 1;
                }
            } else {
                // 用户信息授权
                $fields['oauth_scope'] = 2;
            }
            $user->update($fields);
        } else {
            // 不存在用户

            // 判断授权方式设定对应字段
            if (isset($result['token_response']) && $result['token_response']['scope'] == 'snsapi_base') {
                // 静默授权
                $fields['nickname'] = env('NAME_PREFIX', '用户') . time();
                $fields['oauth_scope'] = 1;
            } else {
                // 用户信息授权
                $fields['oauth_scope'] = 2;
                $fields['gender']      = $result['raw']['sex'];
                $fields['wx_nickname'] = $result->getName();
                $fields['wx_avatar']   = $result->getAvatar();
                $fields['nickname']    = env('NAME_PREFIX', '用户') . time();
                $fields['avatar_url']  = 'avatars/default-avatar.png';
                $fields['status']      = 1;
            }

            if ($this->DELAYCREATE) {
                // 延时创建
                return $this->success([
                    'user'      => ['status' => 0],
                    'wx_user'   => $fields,
                    'wx_openid' => $openid,
                    'token'     => ''
                ]);
            } else {
                // 非延时创建
                $fields['wx_openid'] = $openid;
                $user = User::create($fields);
            }
        }
        if ($user) {
            $token = $user->createToken('token-name');
            return $this->success([
                'user'  => new UserResource($user),
                'wx_openid' => $openid,
                'token' => $token->plainTextToken
            ]);
        }
    }

    /**
     * 微信绑定手机
     *
     * @return void
     */
    public function wxBindMobile(Request $request)
    {
        $request->validate(
            [
                'mobile'             => 'required|is_mobile',
                'code'               => 'required|verify_code:' . $request->mobile,
                'openid.required'    => '获取用户openid失败',
            ],
            [
                'mobile.required'    => '手机号不能为空',
                'mobile.is_mobile'   => '手机号格式不正确',
                'mobile.verify_code' => '验证码错误',
                'openid.required'                  => '获取用户openid失败',
            ]
        );

        $wx_openid = $request->input('openid');
        $mobile    = $request->input('mobile');
        // 验证
        if ($this->DELAYCREATE) {
            // 延时创建
            $user = User::where('mobile', $mobile)->first();
            if ($user) {
                // 有用户则不创建新用户并更新此用户xcx_openid
                $user->update([
                    'wx_openid' => $wx_openid
                ]);
            } else {
                // 没有用户就通过xcx_openid进行查找
                $user = User::whereNotNull('wx_openid')->where('wx_openid', $wx_openid)->first();
                if ($user) {
                    // 有用户就更新
                    $user->update([
                        'mobile' => $mobile
                    ]);
                } else {
                    // 没有用户就创建
                    $fields = [
                        'mobile'      => $mobile,
                        'oauth_scope' => $request->userInfo ? $request->userInfo['oauth_scope'] : 1,
                        'gender'      => $request->userInfo ? $request->userInfo['gender'] : 0,
                        'wx_nickname' => $request->userInfo ? $request->userInfo['wx_nickname'] : '',
                        'wx_avatar'   => $request->userInfo ? $request->userInfo['wx_avatar'] : '',
                        'nickname'    => env('NAME_PREFIX', '用户') . time(),
                        'avatar_url'  => 'avatars/default-avatar.png',
                        'wx_openid'   => $wx_openid,
                        'status'      => 1
                    ];
                    $user = User::create($fields);
                }
            }
        } else {
            // 非延时创建
            $user = User::whereNotNull('wx_openid')->where('wx_openid', $wx_openid)->first();
            if ($user) {
                // 有用户就更新
                $user->update([
                    'mobile' => $mobile
                ]);
            } else {
                return $this->errorBadRequest('绑定失败，未找到用户');
            }
        }
        $token = $user->createToken('token-name');
        return $this->success([
            'user'       => new UserResource($user),
            'token'      => $token->plainTextToken
        ]);
    }

    public function jssdk(Request $request)
    {
        if (config('app.env') == 'local') {
            return null;
        }
        $arr = explode(',', $request->get('apis'));
        $debug = $request->get('debug') === 'true' ? true : false;
        $json = $request->get('json') === 'true' ? true : false;
        $url = $request->get('url');

        // check
        if (!$url) {
            return $this->success(['status' => false, 'msg' => 'params error', 'data' => ''], '参数错误');
        }
        $app = \EasyWeChat::officialAccount();
        $app->jssdk->setUrl($url);
        $config = $app->jssdk->buildConfig($arr, $debug, $json, $url);
        return $this->success(json_decode($config, true));
    }

    /**
     * 一键登录
     * https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
     * https://easywechat.com/5.x/mini-program/phone_number.html
     *
     * @return void
     */
    public function xcxMobileLogin(Request $request)
    {
        $request->validate(
            [
                'openid'           => 'required',
                'code'          => 'required',
            ],
            [
                'login_code.required'  => '获取登录凭证失败',
                'mobile_code.required' => '获取手机凭证失败',
            ]
        );
        // 登录凭证
        $login_code = $request->login_code;
        // 手机凭证
        $mobile_code = $request->mobile_code;

        // EasyWeChat
        $miniProgram = \EasyWeChat::miniProgram();

        // 获取openid
        try {
            $openid_result = $miniProgram->auth->session($request->login_code);
        } catch (\Exception $e) {
            report($e);
            $this->errorBadRequest('发生错误，请稍后重试');
        }
        //获取手机号
        try {
            $mobile_result = $miniProgram->phone_number->getUserPhoneNumber($mobile_code);
        } catch (\Exception $e) {
            report($e);
            $this->errorBadRequest('发生错误，请稍后重试');
        }

        if (!isset($openid_result['openid'])) {
            \Log::error("小程序授权解析" . json_encode($openid_result));
            $this->errorBadRequest('授权失败，请重新进入小程序');
        }
        $openid = $openid_result['openid'];

        if (!isset($res['phone_info']['purePhoneNumber'])) {
            \Log::error("手机号授权解析错误" . json_encode($mobile_result));
            $this->errorBadRequest('授权失败，请重新进入小程序');
        }
        $mobile = $mobile_result['phone_info']['purePhoneNumber'];

        $user = User::Where('mobile', $mobile)->first();
        if ($user) {
            // 有用户则不创建新用户并更新此用户xcx_openid
            $user->update([
                'xcx_openid' => $openid
            ]);
        } else {
            // 没有用户就通过xcx_openid进行查找
            $user = User::Where('xcx_openid', $openid)->first();
            if ($user) {
                // 有用户就更新
                $user->update([
                    'mobile' => $mobile
                ]);
            } else {
                // 没有用户就创建
                $fields = [
                    'mobile'      => $mobile,
                    'nickname'    => env('NAME_PREFIX', '用户') . time(),
                    'avatar_url'  => 'avatars/default-avatar.png',
                    'xcx_openid'  => $openid,
                    'status'      => 1
                ];
                $user = User::create($fields);
            }
        }

        $token = $user->createToken('token-name');
        $this->success([
            'user'  => new UserResource($user),
            'token' => $token->plainTextToken
        ]);
    }

    /**
     * 用户注册
     * 注意，该接口仅用于代码样例，实际使用时请根据业务需求自行修改
     *
     * @return void
     */
    public function register(Request $request)
    {
        $inputs = $request->validate([
            'nickname'  => 'required|max:20',
            'real_name' => 'nullable|string|max:50',
            'mobile'    => 'required|max:20',
            'email'     => 'nullable|email',
            'message'   => 'nullable|max:200',
            'gender'    => 'nullable|in:0,1,2',
            'birthdate' => 'nullable|date',
        ], [
            'nickname.required' => '昵称不能为空',
            'nickname.max'      => '昵称不能超过20个字符',
            'real_name.string'  => '真实姓名必须是字符串',
            'real_name.max'     => '真实姓名不能超过50个字符',
            'mobile.required'   => '电话不能为空',
            'mobile.max'        => '电话不能超过20个字符',
            'email.email'       => '邮箱格式不正确',
            'message.max'       => '留言不能超过200个字符',
            'gender.in'         => '请选择性别',
            'birthdate.date'    => '请输入正确的日期格式',
        ]);

        $user = User::where('email', $inputs['email'])->first();
        if ($user) {
            $this->errorBadRequest('该邮箱已注册');
        }

        $fields = [
            'nickname'  => $inputs['nickname'],
            'real_name' => $inputs['real_name'] ?? null,
            'email'     => $inputs['email'],
            'mobile'    => $inputs['mobile'],
            'birthdate' => $inputs['birthdate'],
            'gender'    => $inputs['gender'],
            'role'      => $request->role,
            'hobbies'   => $request->hobbies,
            'intro'     => $request->intro,
            'status'    => $request->status,
        ];

        if (isset($inputs['password'])) {
            $fields['password'] = bcrypt($inputs['password']);
        }

        $user = User::create($fields);
        return $this->ok('注册成功');
    }
}
