<?php

namespace App\Admin\Repositories;

use App\Models\Diary as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Diary extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 状态集合
     */
    public static $statusMap = [
        0 => '草稿',
        1 => '转码中',
        2 => '审核中',
        3 => '已发布',
        4 => '已下架',
        5 => '未通过',
    ];


    public static $statusFrontMap = [
        3 => '发布',
        4 => '下架',
    ];

    public static $statusColor = [
        0 => 'default',
        1 => 'default',
        2 => 'default',
        3 => 'success',
        4 => 'danger',
    ];
}
