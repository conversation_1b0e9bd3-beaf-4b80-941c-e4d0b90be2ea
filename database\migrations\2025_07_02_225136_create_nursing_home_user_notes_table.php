<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nursing_home_user_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('nursing_home_id')->comment('养老机构ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('nickname')->nullable()->comment('机构给用户设置的备注名');
            $table->text('notes')->nullable()->comment('备注信息');
            $table->json('tags')->nullable()->comment('用户标签（JSON格式）');
            $table->tinyInteger('user_type')->default(0)->comment('用户类型：0-无，1-入住老人，2-入住老人家属，3-养老院员工，4-养老院上下游');
            $table->tinyInteger('channel')->default(0)->comment('获客渠道：0-无，1-博主大V，2-街道居委，3-其他');
            $table->tinyInteger('user_level')->default(0)->comment('潜在客户等级：0-无，1-重点，2-观察，3-普通');
            $table->timestamps();

            // 添加索引
            $table->index(['nursing_home_id', 'user_id']);
            $table->index(['nursing_home_id', 'user_type']);
            $table->index(['nursing_home_id', 'user_level']);
            $table->unique(['nursing_home_id', 'user_id'], 'unique_nursing_home_user');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nursing_home_user_notes');
    }
};
