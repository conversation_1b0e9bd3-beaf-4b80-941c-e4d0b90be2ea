<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ContractResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id'                => $this->id,
            'nursing_home_name' => $this->nursing_home_name,
            'sign_at'           => optional($this->sign_at)->format('Y年m月d日 H:i'),
            'status'            => $this->status,
            'created_at'        => optional($this->created_at)->toDateTimeString(),
            'updated_at'        => optional($this->updated_at)->toDateTimeString(),
        ];
    }
}
