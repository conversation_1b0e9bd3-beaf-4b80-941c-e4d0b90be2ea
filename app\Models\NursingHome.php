<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NursingHome extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    use HasFactory;

    protected $table = 'nursing_homes';

    protected $fillable = [
        'created_by',
        'name',
        'avatar_url',
        'province',
        'city',
        'district',
        'address',
        'longitude',
        'latitude',
        'mobile',
        'description',
        'image_urls',
        'price_image_urls',
        'transportation_image_urls',
        'honor_image_urls',
        'other_image_urls',
        'upload_video_url',
        'video_url',
        'video_thumb_url',
        'status',
        'expired_at',
        'deleted_at',
    ];

    protected $casts = [
        'image_urls'                => 'array',
        'price_image_urls'          => 'array',
        'transportation_image_urls' => 'array',
        'honor_image_urls'          => 'array',
        'other_image_urls'          => 'array',
        'expired_at'                => 'date',
    ];

    public function diaries()
    {
        return $this->hasMany(Diary::class);
    }

    public function userFollows()
    {
        return $this->hasMany(UserFollow::class);
    }

    public function UserNursingHomes()
    {
        return $this->hasMany(UserNursingHome::class);
    }

    /**
     * 通过user_nursing_homes表关联的用户
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_nursing_homes', 'nursing_home_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * 机构对用户的备注
     */
    public function userNotes()
    {
        return $this->hasMany(NursingHomeUserNote::class);
    }

    public function milestones()
    {
        return $this->hasMany(Milestone::class);
    }

    /**
     * 创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getVideoThumbUrlThumbAttribute()
    {
        if ($this->video_thumb_url) {
            $video_thumb_url = ossThumb($this->video_thumb_url, 635, 400);
            return $video_thumb_url;
        }
        return '';
    }

    /**
     * Get the institution user permissions for the nursing home.
     */
    public function institutionUserPermissions()
    {
        return $this->hasMany(InstitutionUserPermission::class);
    }
}
