@php
$canManageStaff = can_manage_staff();
$canManageInfo = can_manage_info();
$canManageVideos = can_manage_videos();
$canViewData = can_view_data();
$canManageFinance = can_manage_finance();
@endphp

<div class="sidebar p-lg-4 my-4" id="sidebar">
  <ul class="sidebar-menu">

    <!-- dashboard -->
    <li class="mb-3 {{ $activeMenu === '/dashboard' ? 'active' : '' }}">
      <a href="{{ route('dashboard') }}">
        <i class="bi bi-house icon me-1"></i>
        <span>首页</span>
      </a>
    </li>

    <!-- 创作中心 -->
    @if($canManageVideos)
    <li class="mb-3">
      <div class="mb-2 {{ $activeMenu === '/diary/list' ? 'active' : '' }}">
        <a href="{{ route('diary.list') }}">
          <i class="bi bi-person-square icon me-1"></i>
          <span>创作中心</span>
        </a>
      </div>
      <ul class="sidebar-sub-menu">
        <li class="mb-2 {{ $activeMenu === '/diary/list?status=0' ? 'active' : '' }}">
          <a href="{{ route('diary.list', ['status' => 0]) }}">
            <span>草稿箱</span>
          </a>
        </li>
        <li class="{{ $activeMenu === '/diary/list?status=3' ? 'active' : '' }}">
          <a href="{{ route('diary.list', ['status' => 3]) }}">
            <span>发表记录</span>
          </a>
        </li>
      </ul>
    </li>
    @endif

    <!-- 互动管理 -->
    @if($canManageInfo)
    <li class="mb-3 {{ $activeMenu === '/nursing-home/contacts' ? 'active' : '' }}">
      <a href="{{ route('nursing-home.contacts') }}">
        <i class="bi bi-megaphone icon me-1"></i>
        <span>互动管理</span>
      </a>
    </li>
    @endif

    <!-- 数据中心 -->
    @if($canViewData)
    <li class="mb-3">
      <div class="mb-2 {{ in_array($activeMenu,
        ['/data-center/user-views', '/data-center/user-shares', '/user-management', '/data-center/ranks']) ? 'active' : '' }}">
        <a href="{{ route('user-management.index') }}">
          <i class="bi bi-bar-chart icon me-1"></i>
          <span>数据中心</span>
        </a>
      </div>
      <ul class="sidebar-sub-menu">
        <li class="mb-2 {{ $activeMenu === '/user-management' ? 'active' : '' }}">
          <a href="{{ route('user-management.index') }}">
            <span>用户管理</span>
          </a>
        </li>
        <!-- <li class="mb-2 {{ $activeMenu === '/user-management/statistics' ? 'active' : '' }}">
          <a href="{{ route('user-management.statistics') }}">
            <span>用户数据统计</span>
          </a>
        </li> -->
        <li class="mb-2 {{ $activeMenu === '/data-center/user-views' ? 'active' : '' }}">
          <a href="{{ route('data-center.views') }}">
            <span>浏览数据</span>
          </a>
        </li>
        <li class="mb-2 {{ $activeMenu === '/data-center/user-shares' ? 'active' : '' }}">
          <a href="{{ route('data-center.user-shares') }}">
            <span>分享数据</span>
          </a>
        </li>
        <li class="{{ $activeMenu === '/data-center/ranks' ? 'active' : '' }}">
          <a href="{{ route('data-center.ranks') }}">
            <span>数据排行</span>
          </a>
        </li>
      </ul>
    </li>
    @endif

    <!-- 机构信息 -->
    @if($canManageInfo || $canManageStaff)
    <li class="mb-3">
      <div class="mb-2">
        <a href="{{ route('nursing-home.index') }}">
          <i class="bi bi-lightbulb icon me-1"></i>
          <span>机构信息</span>
        </a>
      </div>
      <ul class="sidebar-sub-menu">
        @if($canManageInfo)
        <li class="mb-2 {{ $activeMenu === '/nursing-home' ? 'active' : '' }}">
          <a href="{{ route('nursing-home.index') }}">
            <span>基础信息</span>
          </a>
        </li>
        <li class="mb-2 {{ $activeMenu === '/nursing-home/milestone' ? 'active' : '' }}">
          <a href="{{ route('nursing-home.milestone') }}">
            <span>里程碑管理</span>
          </a>
        </li>
        @endif
        @if($canManageStaff)
        <li class="mb-2 {{ $activeMenu === '/institution/staff' ? 'active' : '' }}">
          <a href="{{ route('institution.staff.index') }}">
            <span>员工管理</span>
          </a>
        </li>
        @endif
      </ul>
    </li>
    @endif

    <!-- 财务管理 -->
    @if($canManageFinance)
    <li class="mb-3">
      <div class="mb-2">
        <a href="{{ route('payment.index') }}">
          <i class="bi bi-cash icon me-1"></i>
          <span>财务管理</span>
        </a>
      </div>
      <ul class="sidebar-sub-menu">
        <li class="mb-2 {{ $activeMenu === '/payment/index' ? 'active' : '' }}">
          <a href="{{ route('payment.index') }}">
            <span>我的订单</span>
          </a>
        </li>
        <li class="mb-2 {{ $activeMenu === '/invoice/index' ? 'active' : '' }}">
          <a href="{{ route('invoice.index') }}">
            <span>发票管理</span>
          </a>
        </li>
      </ul>
    </li>
    @endif

    <!-- 个人中心 -->
    <li class="mb-3">
      <div class="mb-2">
        <a href="#">
          <i class="bi bi-gear icon me-1"></i>
          <span>个人中心</span>
        </a>
      </div>
      <ul class="sidebar-sub-menu">
        <li class="mb-2 {{ $activeMenu === '/change-password' ? 'active' : '' }}">
          <a href="{{ route('auth.change.password') }}">
            <span>更改密码</span>
          </a>
        </li>
        <li class="mb-2">
          <a href="{{ route('logout') }}"
            onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
            <span>退出</span>
          </a>
        </li>
      </ul>
    </li>

  </ul>
</div>
