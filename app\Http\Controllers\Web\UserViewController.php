<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;
use App\Models\User;
use App\Models\Diary;
use App\Models\UserBrowseRecord;
use Illuminate\Support\Facades\DB;

class UserViewController extends WebController
{

    /**
     * 浏览数据页面 - 基于用户浏览记录重构
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取查询参数
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $dateRange = $request->get('date_range', '7'); // 默认7天
        $sortBy = $request->get('sort_by', 'latest'); // latest, duration, views
        $searchUser = $request->get('search_user', ''); // 搜索用户
        $searchVideo = $request->get('search_video', ''); // 搜索视频
        $userId = $request->get('user_id'); // URL参数筛选用户
        $diaryId = $request->get('diary_id'); // URL参数筛选视频

        // 计算日期范围
        $startDate = match ($dateRange) {
            '1' => now()->startOfDay(),
            '7' => now()->subDays(6)->startOfDay(),
            '30' => now()->subDays(29)->startOfDay(),
            '90' => now()->subDays(89)->startOfDay(),
            default => now()->subDays(6)->startOfDay()
        };

        // 基础查询 - 获取该养老院的浏览记录
        $browseRecordsQuery = UserBrowseRecord::with(['user', 'diary'])
            ->where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate);

        // URL参数筛选
        $filteredUser = null;
        $filteredDiary = null;

        if ($userId) {
            $filteredUser = User::find($userId);
            if ($filteredUser) {
                $browseRecordsQuery->where('user_id', $userId);
            }
        }

        if ($diaryId) {
            $filteredDiary = Diary::find($diaryId);
            if ($filteredDiary) {
                $browseRecordsQuery->where('diary_id', $diaryId);
            }
        }

        // 搜索功能 - 分别处理用户搜索和视频搜索
        if ($searchUser) {
            $browseRecordsQuery->whereHas('user', function ($query) use ($searchUser) {
                $query->where('nickname', 'like', "%{$searchUser}%");
                // ->orWhere('real_name', 'like', "%{$searchUser}%")
                // ->orWhere('mobile', 'like', "%{$searchUser}%");
            });
        }

        if ($searchVideo) {
            $browseRecordsQuery->whereHas('diary', function ($query) use ($searchVideo) {
                $query->where('title', 'like', "%{$searchVideo}%");
            });
        }

        // 排序
        switch ($sortBy) {
            case 'duration':
                $browseRecordsQuery->orderBy('browse_duration', 'desc');
                break;
            case 'views':
                $browseRecordsQuery->orderBy('created_at', 'desc');
                break;
            default:
                $browseRecordsQuery->orderBy('created_at', 'desc');
        }

        // 分页获取浏览记录
        $browseRecords = $browseRecordsQuery->paginate($perPage);

        // 统计数据
        $statistics = $this->getBrowseStatistics($nursing_home_id, $startDate);



        return view('data-center.views', compact(
            'browseRecords',
            'statistics',
            'dateRange',
            'sortBy',
            'searchUser',
            'searchVideo',
            'filteredUser',
            'filteredDiary'
        ));
    }

    /**
     * 获取浏览统计数据
     */
    private function getBrowseStatistics($nursing_home_id, $startDate)
    {
        $baseQuery = UserBrowseRecord::where('nursing_home_id', $nursing_home_id);

        return [
            // 总浏览次数
            'total_views' => $baseQuery->clone()->where('created_at', '>=', $startDate)->count(),

            // 今日浏览次数
            'today_views' => $baseQuery->clone()->whereDate('created_at', today())->count(),

            // 昨日浏览次数
            'yesterday_views' => $baseQuery->clone()->whereDate('created_at', now()->subDay())->count(),

            // 最近一周浏览次数
            'week_views' => $baseQuery->clone()
                ->where('created_at', '>=', now()->subDays(6)->startOfDay())
                ->count(),

            // 总浏览时长（分钟）
            'total_duration' => round($baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->sum('browse_duration') / 60, 2),

            // 平均浏览时长（秒）
            'avg_duration' => round($baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->avg('browse_duration'), 2),

            // 最近一周浏览时长（分钟）
            'week_duration' => $this->calculateWeeklyDuration($nursing_home_id),

            // 最近一周平均浏览时长（秒）
            'week_avg_duration' => $this->calculateWeeklyAvgDuration($nursing_home_id),
        ];
    }

    /**
     * 计算最近一周浏览时长（分钟）
     */
    private function calculateWeeklyDuration($nursing_home_id)
    {
        $weekStartDate = now()->subDays(6)->startOfDay();

        $totalDuration = UserBrowseRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $weekStartDate)
            ->sum('browse_duration');

        return round($totalDuration / 60, 2); // 转换为分钟
    }

    /**
     * 计算最近一周平均浏览时长（秒）
     */
    private function calculateWeeklyAvgDuration($nursing_home_id)
    {
        $weekStartDate = now()->subDays(6)->startOfDay();

        $avgDuration = UserBrowseRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $weekStartDate)
            ->avg('browse_duration');

        return round($avgDuration ?: 0, 2);
    }

    /**
     * 获取热门视频排行
     */
    private function getPopularVideos($nursing_home_id, $startDate)
    {
        return DB::table('user_browse_records')
            ->join('diaries', 'user_browse_records.diary_id', '=', 'diaries.id')
            ->where('user_browse_records.nursing_home_id', $nursing_home_id)
            ->where('user_browse_records.created_at', '>=', $startDate)
            ->select(
                'diaries.id',
                'diaries.title',
                'diaries.cover_url',
                DB::raw('COUNT(*) as view_count'),
                DB::raw('SUM(user_browse_records.browse_duration) as total_duration'),
                DB::raw('AVG(user_browse_records.browse_duration) as avg_duration'),
                DB::raw('COUNT(DISTINCT user_browse_records.user_id) as unique_viewers')
            )
            ->groupBy('diaries.id', 'diaries.title', 'diaries.cover_url')
            ->orderBy('view_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 获取活跃用户排行
     */
    private function getActiveUsers($nursing_home_id, $startDate)
    {
        return DB::table('user_browse_records')
            ->join('users', 'user_browse_records.user_id', '=', 'users.id')
            ->where('user_browse_records.nursing_home_id', $nursing_home_id)
            ->where('user_browse_records.created_at', '>=', $startDate)
            ->whereNotNull('user_browse_records.user_id')
            ->select(
                'users.id',
                'users.nickname',
                'users.real_name',
                'users.avatar_url',
                DB::raw('COUNT(*) as view_count'),
                DB::raw('SUM(user_browse_records.browse_duration) as total_duration'),
                DB::raw('COUNT(DISTINCT user_browse_records.diary_id) as videos_watched')
            )
            ->groupBy('users.id', 'users.nickname', 'users.real_name', 'users.avatar_url')
            ->orderBy('view_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 获取浏览时长分布
     */
    private function getDurationDistribution($nursing_home_id, $startDate)
    {
        $records = UserBrowseRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate)
            ->pluck('browse_duration');

        $distribution = [
            '0-10秒' => 0,
            '11-30秒' => 0,
            '31-60秒' => 0,
            '1-3分钟' => 0,
            '3-5分钟' => 0,
            '5分钟以上' => 0,
        ];

        foreach ($records as $duration) {
            if ($duration <= 10) {
                $distribution['0-10秒']++;
            } elseif ($duration <= 30) {
                $distribution['11-30秒']++;
            } elseif ($duration <= 60) {
                $distribution['31-60秒']++;
            } elseif ($duration <= 180) {
                $distribution['1-3分钟']++;
            } elseif ($duration <= 300) {
                $distribution['3-5分钟']++;
            } else {
                $distribution['5分钟以上']++;
            }
        }

        return $distribution;
    }
}
