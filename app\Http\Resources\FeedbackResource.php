<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;


class FeedbackResource extends JsonResource
{
    /**
     * Undocumented function
     *
     * @param [type] $request
     * @return void
     */
    public function toArray($request)
    {
        return [
            'id'          => $this->id,
            'user'        => new UserResource($this->user),
            'email'       => $this->email,
            'message'     => $this->message,
            'image_urls'  => $this->full_image_urls,
            'status'      => $this->status,
            'status_text' => $this->status_text,
            'created_at'  => optional($this->created_at)->toDateTimeString(),
            'updated_at'  => optional($this->updated_at)->toDateTimeString(),
        ];
    }
}
