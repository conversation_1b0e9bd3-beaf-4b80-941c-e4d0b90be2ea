<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UserBrowseRecord;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserBrowseRecordController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserBrowseRecord(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('diary_id');
            $grid->column('nursing_home_id');
            $grid->column('browse_duration');
            $grid->column('ip_address');
            $grid->column('user_agent');
            $grid->column('device_type');
            $grid->column('platform');
            $grid->column('browse_started_at');
            $grid->column('browse_ended_at');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserBrowseRecord(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('diary_id');
            $show->field('nursing_home_id');
            $show->field('browse_duration');
            $show->field('ip_address');
            $show->field('user_agent');
            $show->field('device_type');
            $show->field('platform');
            $show->field('browse_started_at');
            $show->field('browse_ended_at');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserBrowseRecord(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('diary_id');
            $form->text('nursing_home_id');
            $form->text('browse_duration');
            $form->text('ip_address');
            $form->text('user_agent');
            $form->text('device_type');
            $form->text('platform');
            $form->text('browse_started_at');
            $form->text('browse_ended_at');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
