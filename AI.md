# AI.md

## 项目介绍

**老邻日记 (Laolin Diary)** 是一个综合的社交媒体平台，基于 Laravel 9 和 PHP 8.1 构建，前端包括 WeChat 小程序和PC端网站，后端使用 Dcat Admin 2.2.2-beta。该平台允许用户分享日常生活经历（日记），关注护理家园，并通过管理界面管理内容。


## Architecture

### Backend (Laravel 9)
- **Framework**: Laravel 9 with PHP 8.1+
- **Admin Panel**: Dcat Admin 2.2.2-beta
- **Authentication**: Laravel Sanctum + Jetstream
- **Database**: MySQL with comprehensive migrations
- **API**: RESTful API with Sanctum authentication
- **Storage**: Aliyun OSS + Tencent COS

### Frontend
- **Web**: Blade templates with Bootstrap + Tailwind CSS
- **WeChat Mini-Program**: Uni-app based (Vue 2.6.x) in `/xcx/`

### 功能说明
- `app\Admin\Repositories\User.php` 定义了3种用户角色，机构用户是PC端前端使用者。
- 机构超级管理员可为机构的其他管理员分配权限：人员管理、机构信息管理、视频管理、数据中心、财务管理
- User authentication (multiple methods: WeChat, mobile, email)
- Diary sharing with media support
- Nursing home management and following
- Content management (news, pages, sliders)
- User analytics and data center
- Social features (likes, comments, shares, collections)

## Development Setup

### Prerequisites
- PHP 8.1+
- MySQL 5.7+
- Node.js 14+
- Composer
- WeChat Developer Tools (for mini-program)

## Directory Structure

### `/app`
- **Admin/**: Dcat Admin controllers, repositories, metrics
- **Http/Controllers/**: Web and API controllers
- **Models/**: Eloquent models
- **Service/**: Business logic services

### `/xcx`
- WeChat mini-program source code
- Uni-app configuration and components
- Pages and business logic

### `/database`
- **migrations/**: Database schema definitions
- **factories/**: Model factories for testing
- **seeders/**: Database seeders

### `/routes`
- **web.php**: Web routes (Blade views)
- **api.php**: API routes (JSON responses)
- **app/Admin/routes.php**: Admin panel routes

## Key Models & Relationships

### Core Models
- **User**: Platform users with authentication
- **Diary**: User-generated content/diary entries
- **NursingHome**: Nursing home profiles
- **News**: Content management articles
- **Comment**: User comments on diaries
- **UserShareRecord**: Share tracking and analytics

### Relationships
- User → Diaries (hasMany)
- User → NursingHomes (belongsToMany)
- Diary → NursingHome (belongsTo)
- Diary → Comments (hasMany)
- User → UserShareRecord (hasMany)


## 常用任务

### 新增字段

- 创建迁移文件
- 更新数据模型 `app/Models/`
- 数组的定义，如状态类型，放在数据仓库类中 `app/Admin/Repositories/`
- 更新后台 `app/Admin/Controllers`
- 更新 API 接口 `app/Http/Controllers/Api/`
- 更新资源文件 `app/Admin/Resources`
- 更新机构PC前端页面 `resources/views/`

### 新增功能

- 新增后台路由 `app/Admin/routes.php`
- 新增 API 接口 `app/Http/Controllers/Api/`
- 新增前端页面 `resources/views/`
- 新增数据库迁移 `database/migrations/`
- 新增数据模型 `app/Models/`
- 新增数据仓库类 `app/Admin/Repositories/`
- 新增服务类 `app/Service/`
- 新增控制器类 `app/Admin/Controllers/`
- 新增语言文件 `resources/lang/`，只需要添加中文
