<?php

namespace App\Admin\Repositories;

use App\Models\User as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class User extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 性别集合
     */
    const GENDER_UNKNOWN = 0;
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;
    public static $gender = [
        self::GENDER_UNKNOWN => '保密',
        self::GENDER_MALE    => '男',
        self::GENDER_FEMALE  => '女'
    ];

    /**
     * 角色 1:普通用户, 2: 机构超级管理员, 3:销售, 4:机构员工
     */
    const  ROLE_NORMAL               = 1;
    const  ROLE_NURSINGHOME          = 2;
    const  ROLE_SALES                = 3;
    const  ROLE_INSTITUTION_STAFF    = 4;
    public static $role = [
        self::ROLE_NORMAL               => '普通用户',
        self::ROLE_NURSINGHOME          => '机构超级管理员',
        self::ROLE_SALES                => '销售',
        self::ROLE_INSTITUTION_STAFF    => '机构管理员'
    ];
}
