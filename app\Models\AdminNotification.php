<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class AdminNotification extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'admin_notifications';

    // 定义可批量赋值的字段
    protected $fillable = [
        'admin_user_id',
        'title',
        'content',
        'link',
        'is_read',
        'type'
    ];

    // 定义作用域，用于查询未读通知
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function adminUser()
    {
        return $this->belongsTo(config('admin.auth.providers.admin.model'), 'admin_user_id');
    }
}
