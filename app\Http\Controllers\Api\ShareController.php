<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Diary;
use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\UserNursingHome;
use App\Models\UserShareRecord;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\ApiController;

class ShareController extends ApiController
{
    /**
     * 创建转发记录
     * 用户在小程序端点击转发时调用
     */
    public function createShare(Request $request)
    {
        $request->validate([
            'diary_id' => 'required|integer|exists:diaries,id',
        ], [
            'diary_id.required' => '日记ID不能为空',
            'diary_id.exists' => '日记不存在',
        ]);

        $user = $request->user();
        $diaryId = $request->diary_id;

        // 获取日记信息
        $diary = Diary::with('nursingHome')->find($diaryId);
        if (!$diary) {
            return $this->errorBadRequest('日记不存在');
        }

        $diary->increment('share_number');

        // 创建转发记录（to_user_id为空，表示还没有人点击）
        $shareRecord = UserShareRecord::create([
            'from_user_id'    => $user->id,
            'diary_id'        => $diaryId,
            'nursing_home_id' => $diary->nursing_home_id,
            'to_user_id'      => null,
            'share_type'      => UserShareRecord::SHARE_TYPE_VIDEO,
        ]);

        return $this->success([
            'share_record_id' => $shareRecord->id,
            'message' => '转发记录创建成功'
        ]);
    }

    /**
     * 处理转发链接点击
     * 有人根据转发的链接进入小程序时调用
     * 参数：from_user_id、diary_id
     */
    public function handleShareClick(Request $request)
    {

        // Log::info('handleShareClick', [
        //     'user' => $user,
        //     'request' => $request->all(),
        // ]);

        $user = $request->user();
        if (!$user) {
            return $this->ok('用户未登录');
        }

        $request->validate([
            'diary_id'     => 'required|integer',
        ], [
            'diary_id.required'     => '日记ID不能为空',
        ]);

        $diaryId = $request->diary_id;
        $fromUserId = $request->from_user_id;

        $diary = Diary::find($diaryId);
        // 检查日记是否还存在且已发布
        if (!$diary || $diary->status != 3) {
            return $this->errorBadRequest('日记不存在或已下线');
        }

        // 不存在就创建关联的养老院
        UserNursingHome::firstOrCreate(
            [
                'user_id' => $user->id,
                'nursing_home_id' => $diary->nursing_home_id,
            ]
        );

        // 如果用户没有设定养老院，自动设定
        if (!$user->nursing_home_id) {
            $user->update([
                'nursing_home_id' => $diary->nursing_home_id,
            ]);
        }

        // 如果没有from_user_id，直接返回成功
        if (!$fromUserId) {
            return $this->ok('访问成功');
        }

        // 检查from_user_id是否是销售
        $fromUser = User::find($fromUserId);
        if ($fromUser && $fromUser->role == 3) {
            // 如果当前用户的销售是空或0
            if ($user->sales_id == 0 || $user->sales_id == null) {
                $user->update([
                    'sales_id' => $fromUserId,
                ]);
            }
        }

        // 通过from_user_id和diary_id查找最新的视频分享记录
        $shareRecord = UserShareRecord::with(['diary', 'nursingHome'])
            ->where('from_user_id', $fromUserId)
            ->where('diary_id', $diaryId)
            ->where('share_type', UserShareRecord::SHARE_TYPE_VIDEO)
            ->orderBy('created_at', 'desc')
            ->first();

        // 如果不存在转发记录，创建新的
        if (!$shareRecord) {
            $shareRecord = UserShareRecord::create([
                'from_user_id'    => $fromUserId,
                'diary_id'        => $diaryId,
                'nursing_home_id' => $diary->nursing_home_id,
                'to_user_id'      => $user ? $user->id : null,
                'share_type'      => UserShareRecord::SHARE_TYPE_VIDEO,
            ]);
        }

        // 如果点击的是转发人自己，直接返回成功
        if ($user && $user->id == $shareRecord->from_user_id) {
            return $this->success([
                'message' => '访问成功'
            ]);
        }

        // 检查当前用户是否已经点击过这个分享链接
        $hasClicked = false;
        $isNewClick = false;

        // 检查是否已经有这个用户的点击记录
        $existingRecord = UserShareRecord::where('from_user_id', $shareRecord->from_user_id)
            ->where('diary_id', $shareRecord->diary_id)
            ->where('share_type', UserShareRecord::SHARE_TYPE_VIDEO)
            ->where('to_user_id', $user->id)
            ->first();

        if ($existingRecord) {
            $hasClicked = true;
        } else {
            // 如果原始转发记录的to_user_id为空（还没人点击），直接更新
            if (is_null($shareRecord->to_user_id)) {
                $shareRecord->update(['to_user_id' => $user->id]);
            } else {
                // 否则创建新的点击记录
                UserShareRecord::create([
                    'from_user_id'    => $shareRecord->from_user_id,
                    'diary_id'        => $shareRecord->diary_id,
                    'nursing_home_id' => $shareRecord->nursing_home_id,
                    'to_user_id'      => $user->id,
                    'share_type'      => $shareRecord->share_type,
                ]);
            }

            $hasClicked = true;
            $isNewClick = true;
        }

        return $this->success([
            'diary' => [
                'id' => $shareRecord->diary->id,
                'title' => $shareRecord->diary->title,
                'cover_url' => $shareRecord->diary->cover_url,
                'video_url' => $shareRecord->diary->video_url,
                'nursing_home_id' => $shareRecord->diary->nursing_home_id,
            ],
            'nursing_home' => $shareRecord->nursingHome ? [
                'id' => $shareRecord->nursingHome->id,
                'name' => $shareRecord->nursingHome->name,
                'avatar_url' => $shareRecord->nursingHome->avatar_url,
            ] : null,
            'has_clicked' => $hasClicked,
            'is_new_click' => $isNewClick,
            'message' => '访问成功'
        ]);
    }

    /**
     * 创建机构主页分享记录
     * 用户在小程序端点击分享机构主页时调用
     */
    public function createNursingHomeShare(Request $request)
    {
        $request->validate([
            'nursing_home_id' => 'required|integer|exists:nursing_homes,id',
        ], [
            'nursing_home_id.required' => '机构ID不能为空',
            'nursing_home_id.exists' => '机构不存在',
        ]);

        $user = $request->user();
        $nursingHomeId = $request->nursing_home_id;

        // 创建机构主页分享记录（to_user_id为空，表示还没有人点击）
        $shareRecord = UserShareRecord::create([
            'from_user_id'    => $user->id,
            'diary_id'        => null,
            'nursing_home_id' => $nursingHomeId,
            'to_user_id'      => null,
            'share_type'      => UserShareRecord::SHARE_TYPE_NURSING_HOME,
        ]);

        return $this->success([
            'share_record_id' => $shareRecord->id,
            'message' => '机构主页分享记录创建成功'
        ]);
    }

    /**
     * 处理机构主页分享链接点击
     * 有人根据分享的机构主页链接进入小程序时调用
     * 参数：from_user_id、nursing_home_id
     */
    public function handleNursingHomeShareClick(Request $request)
    {
        $request->validate([
            'from_user_id' => 'required|integer|exists:users,id',
            'nursing_home_id' => 'required|integer|exists:nursing_homes,id',
        ], [
            'from_user_id.required' => '转发用户ID不能为空',
            'from_user_id.exists' => '转发用户不存在',
            'nursing_home_id.required' => '机构ID不能为空',
            'nursing_home_id.exists' => '机构不存在',
        ]);

        $user = $request->user(); // 可能为null（未登录用户）

        // 通过from_user_id和nursing_home_id查找最新的机构主页分享记录
        $shareRecord = UserShareRecord::with(['nursingHome'])
            ->where('from_user_id', $request->from_user_id)
            ->where('nursing_home_id', $request->nursing_home_id)
            ->where('share_type', UserShareRecord::SHARE_TYPE_NURSING_HOME)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$shareRecord) {
            return $this->errorBadRequest('机构主页分享记录不存在');
        }

        // 如果点击的是转发人自己，直接返回成功
        if ($user && $user->id == $shareRecord->from_user_id) {
            return $this->success([
                'message' => '访问成功'
            ]);
        }

        // 检查当前用户是否已经点击过这个分享链接
        $hasClicked = false;
        $isNewClick = false;

        // 如果用户已登录，处理点击记录和自动绑定养老院逻辑
        if ($user) {
            // 检查是否已经有这个用户的点击记录
            $existingRecord = UserShareRecord::where('from_user_id', $shareRecord->from_user_id)
                ->where('nursing_home_id', $shareRecord->nursing_home_id)
                ->where('share_type', UserShareRecord::SHARE_TYPE_NURSING_HOME)
                ->where('to_user_id', $user->id)
                ->first();

            if ($existingRecord) {
                $hasClicked = true;
            } else {
                // 如果原始转发记录的to_user_id为空（还没人点击），直接更新
                if (is_null($shareRecord->to_user_id)) {
                    $shareRecord->update(['to_user_id' => $user->id]);
                } else {
                    // 否则创建新的点击记录
                    UserShareRecord::create([
                        'from_user_id' => $shareRecord->from_user_id,
                        'diary_id' => null,
                        'nursing_home_id' => $shareRecord->nursing_home_id,
                        'to_user_id' => $user->id,
                        'share_type' => UserShareRecord::SHARE_TYPE_NURSING_HOME,
                    ]);
                }

                $hasClicked = true;
                $isNewClick = true;

                // 检查用户是否已经绑定了养老院
                $hasNursingHome = UserNursingHome::where('user_id', $user->id)->exists();

                if (!$hasNursingHome && $shareRecord->nursing_home_id) {
                    // 自动绑定到分享的养老院
                    UserNursingHome::updateOrCreate(
                        ['user_id' => $user->id],
                        ['nursing_home_id' => $shareRecord->nursing_home_id]
                    );
                }
            }
        }

        return $this->success([
            'nursing_home' => $shareRecord->nursingHome ? [
                'id' => $shareRecord->nursingHome->id,
                'name' => $shareRecord->nursingHome->name,
                'avatar_url' => $shareRecord->nursingHome->avatar_url,
                'description' => $shareRecord->nursingHome->description,
            ] : null,
            'has_clicked' => $hasClicked,
            'is_new_click' => $isNewClick,
            'message' => '访问成功'
        ]);
    }
}
