<?php

namespace App\Http\Controllers\Web;

use App\Models\Invoice;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;

class InvoiceController extends Controller
{
    /**
     * 发票列表
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        Log::info('Invoice index request', ['user_id' => $user->id, 'nursing_home_id' => $nursing_home_id]);

        if (!$nursing_home_id) {
            Log::warning('User not bound to nursing home', ['user_id' => $user->id]);
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取发票列表
        $invoices = Invoice::whereHas('payment', function ($query) use ($nursing_home_id) {
            $query->where('nursing_home_id', $nursing_home_id);
        })->with('payment')->orderBy('created_at', 'desc')->paginate(10);

        Log::info('Invoices retrieved successfully', ['user_id' => $user->id, 'count' => $invoices->count()]);

        return view('invoice.index', compact('invoices'));
    }

    /**
     * 申请发票页面
     */
    public function create(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;
        $payment_id = $request->get('payment_id');

        Log::info('Invoice create request', ['user_id' => $user->id, 'nursing_home_id' => $nursing_home_id, 'payment_id' => $payment_id]);

        if (!$nursing_home_id) {
            Log::warning('User not bound to nursing home', ['user_id' => $user->id]);
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取已支付且未申请发票的付款记录
        $payments = Payment::where('nursing_home_id', $nursing_home_id)
            ->where('status', \App\Admin\Repositories\Payment::STATUS_PAID)
            ->whereDoesntHave('invoice')
            ->orderBy('created_at', 'desc')
            ->get();

        Log::info('Payments for invoice creation retrieved successfully', ['user_id' => $user->id, 'count' => $payments->count()]);

        return view('invoice.create', compact('payments', 'payment_id'));
    }

    /**
     * 提交发票申请
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 验证输入
        $validatedData = $request->validate([
            'payment_id' => 'required|exists:payments,id,nursing_home_id,' . $nursing_home_id . ',status,' . \App\Admin\Repositories\Payment::STATUS_PAID,
            'invoice_title' => 'required|string|max:255',
            'tax_number' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:255',
            'remark' => 'nullable|string|max:255',
        ]);

        Log::info('Invoice store request', ['user_id' => $user->id, 'validated_data' => $validatedData]);

        // 检查是否已申请过发票
        $existingInvoice = Invoice::where('payment_id', $validatedData['payment_id'])->first();
        if ($existingInvoice) {
            Log::warning('Invoice already applied for this payment', ['user_id' => $user->id, 'payment_id' => $validatedData['payment_id']]);
            return redirect()->back()->with(['message' => '该付款记录已申请过发票。']);
        }

        // 获取付款记录
        $payment = Payment::findOrFail($validatedData['payment_id']);

        // 创建发票申请
        $invoice = new Invoice();
        $invoice->payment_id = $validatedData['payment_id'];
        $invoice->invoice_title = $validatedData['invoice_title'];
        $invoice->tax_number = $validatedData['tax_number'];
        $invoice->address = $validatedData['address'];
        $invoice->phone = $validatedData['phone'];
        $invoice->bank_name = $validatedData['bank_name'];
        $invoice->bank_account = $validatedData['bank_account'];
        $invoice->amount = $payment->total_amount;
        $invoice->remark = $validatedData['remark'];
        $invoice->status = \App\Admin\Repositories\Invoice::STATUS_PENDING;
        $invoice->save();

        return redirect()->route('invoice.index')->with(['message' => '发票申请已提交，请等待审核。']);
    }

    /**
     * 发票详情
     */
    public function detail(Request $request, $id)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取发票详情
        $invoice = Invoice::whereHas('payment', function ($query) use ($nursing_home_id) {
            $query->where('nursing_home_id', $nursing_home_id);
        })->with('payment')->findOrFail($id);

        return view('invoice.detail', compact('invoice'));
    }
}
