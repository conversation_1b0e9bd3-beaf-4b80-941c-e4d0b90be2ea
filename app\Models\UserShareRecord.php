<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserShareRecord extends Model
{
    use HasDateTimeFormatter;
    use HasFactory;

    protected $table = 'user_share_records';

    protected $fillable = [
        'from_user_id',
        'diary_id',
        'nursing_home_id',
        'to_user_id',
        'share_type',
    ];

    // 分享类型常量
    const SHARE_TYPE_VIDEO = 'video';
    const SHARE_TYPE_NURSING_HOME = 'nursing_home';



    /**
     * 关联到转发用户
     */
    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * 关联到被转发的用户(点击链接的用户)
     */
    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * 关联到日记
     */
    public function diary()
    {
        return $this->belongsTo(Diary::class, 'diary_id');
    }

    /**
     * 关联到养老院
     */
    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class, 'nursing_home_id');
    }

    /**
     * 作用域：按转发用户筛选
     */
    public function scopeByFromUser($query, $fromUserId)
    {
        return $query->where('from_user_id', $fromUserId);
    }

    /**
     * 作用域：按日记筛选
     */
    public function scopeByDiary($query, $diaryId)
    {
        return $query->where('diary_id', $diaryId);
    }

    /**
     * 作用域：按接收用户筛选
     */
    public function scopeByToUser($query, $toUserId)
    {
        return $query->where('to_user_id', $toUserId);
    }

    /**
     * 作用域：按养老院筛选
     */
    public function scopeByNursingHome($query, $nursingHomeId)
    {
        return $query->where('nursing_home_id', $nursingHomeId);
    }

    /**
     * 作用域：未被点击的记录（to_user_id为空）
     */
    public function scopeUnclicked($query)
    {
        return $query->whereNull('to_user_id');
    }

    /**
     * 作用域：已被点击的记录（to_user_id不为空）
     */
    public function scopeClicked($query)
    {
        return $query->whereNotNull('to_user_id');
    }

    /**
     * 作用域：视频分享
     */
    public function scopeVideoShares($query)
    {
        return $query->where('share_type', self::SHARE_TYPE_VIDEO);
    }

    /**
     * 作用域：机构主页分享
     */
    public function scopeNursingHomeShares($query)
    {
        return $query->where('share_type', self::SHARE_TYPE_NURSING_HOME);
    }




}
