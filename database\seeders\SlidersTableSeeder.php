<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class SlidersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('sliders')->delete();
        
        \DB::table('sliders')->insert(array (
            0 => 
            array (
                'id' => 1,
                'title' => '滑块一',
                'image_url' => NULL,
                'description' => '滑块一',
                'status' => 1,
                'has_button' => 0,
                'button_link_url' => NULL,
                'is_light' => 1,
                'position' => 3,
                'sort_order' => 0,
                'created_at' => '2022-09-15 11:17:02',
                'updated_at' => '2022-11-02 16:42:21',
            ),
            1 => 
            array (
                'id' => 2,
                'title' => '滑块二',
                'image_url' => NULL,
                'description' => '滑块二',
                'status' => 1,
                'has_button' => 0,
                'button_link_url' => NULL,
                'is_light' => 1,
                'position' => 3,
                'sort_order' => 0,
                'created_at' => '2022-09-15 11:17:02',
                'updated_at' => '2022-11-02 16:42:21',
            ),
        ));
        
        
    }
}