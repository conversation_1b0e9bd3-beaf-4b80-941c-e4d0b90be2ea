<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Admin\Repositories\Slider;
use App\Admin\Repositories\Common;
use Dcat\Admin\Http\Controllers\AdminController;

class SliderController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '滑块列表';
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Slider(), function (Grid $grid) {
            $grid->model()->orderBy('sort_order');
            $grid->column('title');
            $grid->column('has_button')->using(Common::$visibleMap)->badge(Common::$statusColor);
            $grid->column('button_link_url');
            $grid->column('is_light')->using(Slider::$shadeMap)->badge(Common::$statusColor);
            $grid->column('position')->using(Slider::$positionMap);
            $grid->order->orderable();
            $grid->column('status')->using(Common::$activeMap)->badge(Common::$statusColor);

            $grid->quickSearch('id', 'title')
            ->placeholder('输入ID或标题以搜索')
            ->auto(false);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '标题');
                $filter->equal('has_button')->radio(['' => '全部'] + Common::$visibleMap);
                $filter->equal('is_light')->radio(['' => '全部'] + Slider::$shadeMap);
                $filter->equal('position')->radio(['' => '全部'] + Slider::$positionMap);
                $filter->equal('status', '状态')->radio(['' => '全部'] + Common::$activeMap)->default(1);
                $filter->between('created_at')->datetime();
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Slider(), function (Show $show) {
            $show->field('id');
            $show->field('title');
            $show->field('image_url')->image();
            $show->field('description');
            $show->field('has_button')->using(Common::$visibleMap)->dot(Common::$statusColor);
            $show->field('button_link_url');
            $show->field('is_light')->using(Slider::$shadeMap)->dot(Common::$statusColor);
            $show->field('position')->using(Slider::$positionMap);
            $show->field('sort_order');
            $show->field('status')->using(Common::$activeMap)->dot(Common::$statusColor);
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Slider(), function (Form $form) {
            $form->display('id');
            $form->text('title')->required();
            $form->image('image_url')->move('images/' . date("Y/m"))->help("建议尺寸：1920*600")->autoUpload()->uniqueName();
            $form->url('button_link_url');
            $form->textarea('description');
            $form->radio('has_button')->options(Common::$visibleMap)->required();
            $form->radio('is_light')->options(Slider::$shadeMap)->required();
            $form->radio('position')->options(Slider::$positionMap)->required();
            $form->number('sort_order');
            $form->switch('status');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
