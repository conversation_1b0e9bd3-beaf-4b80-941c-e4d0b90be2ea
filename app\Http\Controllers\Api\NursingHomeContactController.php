<?php

namespace App\Http\Controllers\Api;

use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\NursingHomeContact;
use Antto\Sms\Facades\Sms;

class NursingHomeContactController extends ApiController
{

    /**
     * 创建
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'reason'          => 'required',
            'name'            => 'required',
            'phone'           => 'required',
            // 'nursing_home_id' => 'required',
        ],
        [
            'reason.required'          => '请选择联系原因',
            'name.required'            => '请填写称呼',
            'mobile.required'          => '请填写您的电话号码',
            // 'nursing_home_id.required' => '请选择机构',
        ]);

        $result = NursingHomeContact::create([
            'nursing_home_id' => $request->nursing_home_id ?? 0,
            'reason'          => $request->reason,
            'name'            => $request->name,
            'phone'           => $request->phone,
            'message'         => $request->message,
        ]);

        // $result = NursingHome::find($nursing_home_id);
        // 查找机构，存在就发送短信通知机构电话
        // if ($result && $result->mobile) {
        //     // $this->sendMsg($result->mobile, '您的客户' . $request->name . '在' . date('Y-m-d H:i:s') . '联系了您，请及时处理');
        //     $message = '您的客户' . $request->name . '在' . date('Y-m-d H:i:s') . '联系了您，请及时处理';
        //     // Sms::send(to,message, array $gateways = []);
        //     Sms::send('13788921860', $message);
        // }
        return $this->ok('提交成功');
    }
}
