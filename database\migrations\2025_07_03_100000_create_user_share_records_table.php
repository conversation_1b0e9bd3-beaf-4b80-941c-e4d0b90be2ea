<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_share_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('from_user_id')->comment('转发用户ID');
            $table->unsignedBigInteger('diary_id')->nullable()->comment('日记ID');
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('养老院ID(冗余字段)');
            $table->unsignedBigInteger('to_user_id')->nullable()->comment('被转发的用户ID(点击链接的用户)');
            $table->string('share_type', 20)->default('video')->comment('分享类型：video-视频分享，nursing_home-机构主页分享');
            $table->timestamps();

            // 索引
            $table->index(['from_user_id', 'diary_id']);
            $table->index(['diary_id', 'created_at']);
            $table->index(['nursing_home_id', 'created_at']);
            $table->index(['to_user_id', 'created_at']);
            $table->index(['share_type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_share_records');
    }
};
