<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->string('no')->nullable()->comment('订单号');
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('机构');
            $table->tinyInteger('pay_method')->nullable()->comment('支付方式');
            $table->decimal('total_amount')->nullable()->comment('总价');
            $table->dateTime('paid_at')->nullable()->comment('付款时间');
            $table->string('payer_name')->nullable()->comment('付款人');
            $table->string('payer_mobile')->nullable()->comment('付款人手机号');
            $table->string('transaction_no')->nullable()->comment('第三方交易号');
            $table->tinyInteger('status')->default('1')->comment('状态');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments');
    }
}
