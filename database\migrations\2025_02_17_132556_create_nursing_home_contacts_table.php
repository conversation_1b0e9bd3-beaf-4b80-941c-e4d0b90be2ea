<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNursingHomeContactsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('nursing_home_contacts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('养老院');
            $table->text('reason')->nullable()->comment('联系原因');
            $table->string('name')->nullable()->comment('称呼');
            $table->string('phone')->nullable()->comment('电话号码');
            $table->text('message')->nullable()->comment('留言');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nursing_home_contacts');
    }
}
