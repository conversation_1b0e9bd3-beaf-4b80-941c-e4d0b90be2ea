<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class NeedBindMobile
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        if (!$user->mobile) {
            // 返回402，表示需要绑定手机号
            return response()->json([
                'code' => 402,
                'message' => '请先绑定手机号',
            ])->setStatusCode(402);
        }
        return $next($request);
    }
}

