<?php

namespace App\Http\Controllers\Api;

use App\Models\NewsTag;
use Illuminate\Http\Request;
use App\Http\Resources\NewsTagResource;

class NewsTagController extends ApiController
{
    /**
     * 列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 20;
        $result = NewsTag::where('status', 1)
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
        return $this->success(NewsTagResource::collection($result));
    }

    /**
     * 详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $result = NewsTag::find($id);
        if ($result) {
            return $this->success(new NewsTagResource($result));
        }
        $this->errorBadRequest('暂无数据');
    }
}
