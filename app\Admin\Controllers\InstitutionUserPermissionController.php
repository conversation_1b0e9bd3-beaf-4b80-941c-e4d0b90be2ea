<?php

namespace App\Admin\Controllers;

use App\Admin\Controllers\UserNursingHomeController;
use App\Models\InstitutionUserPermission;
use App\Models\User;
use App\Models\NursingHome;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;

class InstitutionUserPermissionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new InstitutionUserPermission(), function (Grid $grid) {
            $grid->model()->with(['user', 'nursingHome']);
            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('user.nickname', '用户');
            $grid->column('nursingHome.name', '养老院');
            $grid->column('can_manage_staff', '员工管理')->display(function ($value) {
                return $value ? '<span class="label bg-success">是</span>' : '<span class="label bg-default">否</span>';
            });
            $grid->column('can_manage_info', '信息管理')->display(function ($value) {
                return $value ? '<span class="label bg-success">是</span>' : '<span class="label bg-default">否</span>';
            });
            $grid->column('can_manage_videos', '视频管理')->display(function ($value) {
                return $value ? '<span class="label bg-success">是</span>' : '<span class="label bg-default">否</span>';
            });
            $grid->column('can_view_data', '数据查看')->display(function ($value) {
                return $value ? '<span class="label bg-success">是</span>' : '<span class="label bg-default">否</span>';
            });
            $grid->column('can_manage_finance', '财务管理')->display(function ($value) {
                return $value ? '<span class="label bg-success">是</span>' : '<span class="label bg-default">否</span>';
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '用户')->select(User::pluck('name', 'id'));
                $filter->equal('nursing_home_id', '养老院')->select(NursingHome::pluck('name', 'id'));
                $filter->between('created_at')->datetime();
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new InstitutionUserPermission(), function (Form $form) {
            $form->display('id');

            $form->select('user_id', '用户')
                ->options(User::where('role', 4)->pluck('nickname', 'id'))
                ->required();

            $form->select('nursing_home_id', '养老院')
                ->options(NursingHome::pluck('name', 'id'))
                ->required();

            $form->divider('权限设置');

            $form->switch('can_manage_staff', '员工管理');
            $form->switch('can_manage_info', '信息管理');
            $form->switch('can_manage_videos', '视频管理');
            $form->switch('can_view_data', '数据查看');
            $form->switch('can_manage_finance', '财务管理');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new InstitutionUserPermission(), function (Show $show) {
            $show->field('id');
            $show->field('user.name', '用户');
            $show->field('nursingHome.name', '养老院');
            $show->field('can_manage_staff', '员工管理')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('can_manage_info', '信息管理')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('can_manage_videos', '视频管理')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('can_view_data', '数据查看')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('can_manage_finance', '财务管理')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('created_at');
            $show->field('updated_at');
        });
    }
}
