<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Diary extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    use HasFactory;

    protected $table = 'diaries';

    protected $fillable = [
        'nursing_home_id',
        'user_id',
        'title',
        'cover_url',
        'cover_vertical_url',
        'upload_video_url',
        'video_url',
        'likes_number',
        'comment_number',
        'browse_number',
        'favorite_number',
        'share_number',
        'is_recommend',
        'publish_at',
        'status',
        'deleted_at',
    ];

    protected $casts = [
        'publish_at' => 'datetime',
    ];

    public function getCoverUrlThumbAttribute()
    {
        if ($this->cover_url) {
            $cover_url = ossThumb($this->cover_url, 267, 200);
            return $cover_url;
        }
        return 'http://diary-oss.laoyangapp.com/snapshot/placeholder.png';
    }

    public function getCoverUrlVerticalAttribute()
    {
        if ($this->cover_url) {
            $cover_url = ossThumb($this->cover_url, 400, 640);
            return $cover_url;
        }
        return 'http://diary-oss.laoyangapp.com/snapshot/placeholder.png';
    }

    // 已发布
    public function scopePublished($query)
    {
        return $query->where('status', 3)->where('publish_at', '<=', now());
    }

    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function collections()
    {
        return $this->hasMany(UserCollection::class);
    }

    public function likes()
    {
        return $this->hasMany(UserLike::class);
    }

    public function follows()
    {
        return $this->hasMany(UserFollow::class);
    }

    public function browseRecords()
    {
        return $this->hasMany(UserBrowseRecord::class);
    }
}
