<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_browse_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户ID');
            $table->unsignedBigInteger('diary_id')->comment('日记ID');
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('养老院ID(冗余字段)');
            $table->integer('browse_duration')->default(0)->comment('浏览时长(秒)');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->text('user_agent')->nullable()->comment('用户代理');
            $table->string('device_type', 20)->nullable()->comment('设备类型(mobile/desktop/tablet)');
            $table->string('platform', 50)->nullable()->comment('平台(iOS/Android/Web)');
            $table->timestamp('browse_started_at')->nullable()->comment('开始浏览时间');
            $table->timestamp('browse_ended_at')->nullable()->comment('结束浏览时间');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'diary_id']);
            $table->index(['diary_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['nursing_home_id', 'created_at']);
            $table->index(['nursing_home_id', 'user_id']);
            $table->index('browse_started_at');

            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('diary_id')->references('id')->on('diaries')->onDelete('cascade');
            $table->foreign('nursing_home_id')->references('id')->on('nursing_homes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_browse_records');
    }
};
