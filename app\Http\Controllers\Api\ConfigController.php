<?php

namespace App\Http\Controllers\Api;

use App\Models\Config;
use Illuminate\Http\Request;
use App\Services\ConfigService;
use App\Admin\Repositories\Common;
use App\Http\Resources\ConfigResource;
use App\Http\Controllers\Api\ApiController;

class ConfigController extends ApiController
{
    /**
     * 列表
     *
     */
    public function index()
    {
        $result = Config::orderBy('created_at', 'desc')->get();
        return $this->success(ConfigResource::collection($result));
    }

    /**
     * 详情
     *
     * @param  $key
     */
    public function show($key)
    {
        $result = Config::where('key', $key)->first();
        if ($result) {
            return $this->success(new ConfigResource($result));
        }
        $this->errorBadRequest('未找到该配置');
    }

    /**
     * 批量查询
     */
    public function search(Request $request)
    {
        $params = $request->params;
        $type = $request->type ?? '';
        if (empty($params)) {
            $this->errorBadRequest('请输入数组参数');
        }
        $result = Config::whereIn('key', $params)->get();
        if ($type) {
            $paramsData = [];
            foreach ($params as $param) {
                $item = $result->firstWhere('key', $param);
                $configService = new ConfigService();
                $getValues = $configService->getValue($item);
                $paramsData[$param] = $getValues;
            }
            return $this->success($paramsData);
        }
        return $this->success(ConfigResource::collection($result));
    }


    /**
     * 星期数组
     */
    public function weekday()
    {
        $result = array_map(
            fn($id, $title) => compact('id', 'title'),
            array_keys(Common::$weekFullMap),
            Common::$weekFullMap
        );

        return $this->success($result);
    }
}
