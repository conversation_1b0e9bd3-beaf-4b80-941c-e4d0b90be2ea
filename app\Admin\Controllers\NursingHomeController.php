<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Services\OssService;
use App\Admin\Repositories\Common;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Repositories\NursingHome;
use App\Admin\Actions\Restore\BatchRestore;
use App\Models\NursingHome as NursingHomeModels;
use Dcat\Admin\Http\Controllers\AdminController;

class NursingHomeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new NursingHome(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('avatar_url')->image('', 40, 40);
            $grid->column('address');
            $grid->column('mobile');
            $grid->column('follow_count', '已关注')->display(function () {
                return $this->userFollows()->count();
            })->label();
            $grid->column('join_count', '已加入')->display(function () {
                return $this->UserNursingHomes()->count();
            })->label();
            $grid->column('status')->using(Common::$activeMap)->badge(Common::$statusColor);
            $grid->column('expired_at', '过期时间')->display(function () {
                return $this->expired_at ? $this->expired_at->format('Y-m-d') : '-';
            });
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name')->width(3);
                $filter->like('address')->width(3);
                $filter->like('mobile')->width(3);
                $filter->equal('status')->radio(['' => '全部'] + Common::$activeMap)->width(3);
                $filter->between('created_at')->datetime()->width(3);
                $filter->between('updated_at')->datetime()->width(3);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(NursingHomeModels::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(NursingHomeModels::class));
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NursingHome(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('avatar_url');
            $show->field('address');
            $show->field('longitude');
            $show->field('latitude');
            $show->field('mobile');
            $show->field('description');
            $show->field('image_urls');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(NursingHome::with(['milestones']), function (Form $form) {

            $form->display('id');

            // 基本信息选项卡
            $form->tab('基本信息', function (Form $form) {
                $form->text('name')->required();
                $form->image('avatar_url')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：600x600")->autoUpload()->uniqueName();
                $form->distpicker(['province', 'city', 'district'], '省市区');
                $form->text('address');
                $form->text('mobile');
                $form->textarea('description');

                $form->date('expired_at', '过期时间')->help('机构账号有效期');
                $form->switch('status')->default(1);
            });

            // 图片选项卡
            $form->tab('图片', function (Form $form) {
                $form->multipleImage('image_urls')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
                $form->multipleImage('price_image_urls')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
                $form->multipleImage('transportation_image_urls')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
                $form->multipleImage('honor_image_urls')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
                $form->multipleImage('other_image_urls')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
            });

            // 视频选项卡
            $form->tab('视频', function (Form $form) {
                // $form->file('upload_video_url')->saveFullUrl()->move('videos/' . date("Y/m"))->autoUpload()->uniqueName();
                // 改为阿里云oss直传
                $ossService = new OssService();
                $configs = $ossService->adminGetUploadPolicy('mp4');
                $full = $configs['full'];
                $filename = $configs['filename'];
                $policy = $configs['policy'];
                $OSSAccessKeyId = $configs['OSSAccessKeyId'];
                $signature = $configs['signature'];
                $callBackBase64 = $configs['callBackBase64'];

                $form->file('upload_video_url', '上传视频')
                    ->disk('oss') // 更改储存驱动
                    ->saveFullUrl()
                    ->autoUpload() // 自动上传
                    // ->accept('mp4') // 限制文件上传格式
                    // ->help('仅支持MP4格式')
                    ->maxSize(512000) // 更改文件最大限制
                    ->removable(false) // 关闭页面删除
                    ->url('https://yang-diary.oss-cn-shanghai.aliyuncs.com') // 设置上传地址
                    // 自定义 webuploader 配置
                    ->options(['fileVal' => 'file']) // Dcat 默认 name 为 _file_
                    // 上传事件
                    ->on(
                        'startUpload',
                        <<<JS
                                function () {
                                    console.log('文件开始上传...', this);
                                    // 上传文件前附加自定义参数到文件上传接口
                                    this.uploader.options.formData['key'] = '$full';
                                    this.uploader.options.formData['fileName'] = "$filename";
                                    this.uploader.options.formData['policy'] = '$policy';
                                    this.uploader.options.formData['OSSAccessKeyId'] = '$OSSAccessKeyId';
                                    this.uploader.options.formData['signature'] = '$signature';
                                    this.uploader.options.formData['success_action_status'] = "200";
                                    this.uploader.options.formData['callback'] = '$callBackBase64';
                                }
                            JS
                    );
                $form->text('video_url')->help('视频地址，优先使用上传视频');
                $form->image('video_thumb_url')->saveFullUrl()->move('images/' . date("Y/m"))->help("建议尺寸：635x400")->autoUpload()->uniqueName();
            });

            // 里程碑选项卡
            $form->tab('里程碑', function (Form $form) {
                $form->hasMany('milestones', '里程碑', function ($form) {
                    $form->text('title', '年份');
                    $form->textarea('description', '描述');
                });
            });

            // 其他选项卡
            $form->tab('其他', function (Form $form) {
                $form->display('created_at');
                $form->display('updated_at');
            });
        });
    }
}
