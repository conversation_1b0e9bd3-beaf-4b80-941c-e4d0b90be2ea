<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center">
      <view class="u-flex-col u-col-center mb-40">
        <view class="form-title u-m-b-30">机构邀请</view>
        <view class="u-font-16 u-m-b-10">邀请管理员加入机构：</view>
        <view class="u-font-16 u-m-b-30">{{ institutionName }}</view>
      </view>

      <view class="px-20 mb-40">
        <u-row>
          <u-col span="6">
            <view
              class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center"
              @click="toContractPayment"
            >
              <u--text
                text="管理数据中心"
                color="#F2AE1E"
                bold
                align="center"
                size="20"
              ></u--text>
              <text class="d-block u-font-18 mt-30">邀请为数据中心管理员</text>
              <text class="d-block mt-30 fc-666">{{
                package_description
              }}</text>
            </view>
          </u-col>
          <u-col span="6">
            <view
              class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center"
              @click="toContractPayment"
            >
              <u--text
                text="管理创作中心"
                color="#F2AE1E"
                bold
                align="center"
                size="20"
              ></u--text>
              <text class="d-block u-font-18 mt-30">邀请为创作中心管理员</text>
              <text class="d-block mt-30 fc-666">{{
                package_description
              }}</text>
            </view>
          </u-col>
        </u-row>
      </view>

      <view class="px-80 my-20">
        <u-row justify="center" gutter="20">
          <u-col span="6">
            <u-button
              color="#f5f5f5"
              customStyle="color:#000"
              shape="circle"
              text="取消"
              @click="goBack"
            ></u-button>
          </u-col>
          <u-col span="6">
            <u-button
              color="#F2AE1E"
              shape="circle"
              text="发送邀请"
              @click="acceptInvite"
              :loading="btnLoading"
            ></u-button>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/common/api";

export default {
  data() {
    return {
      loading: false,
      btnLoading: false,
      institutionName: "",
      nursingHomeId: null,
    };
  },
  onShow() {},
  onLoad(options) {},
  onReady() {},
  methods: {
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
.content {
  min-height: 100vh;
  background-color: #f8f8f8;
  .checkmark img {
    width: 168upx;
    height: 168upx;
  }
}
.btn-grey {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
  color: #332c2b;
}
.form-title {
  font-size: 60upx;
}
.cover-image {
  width: 400upx;
  height: 400upx;
}
.tip-wrap {
  border-radius: 20upx;
  min-height: 320upx;
}
</style>
