<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;


class UserPublicResource extends JsonResource
{
    /**
     * Undocumented function
     *
     * @param [type] $request
     * @return void
     */
    public function toArray($request)
    {
        return [
            'id'              => $this->id,
            'name'            => $this->name ?? '',
            'nickname'        => $this->nickname ?? '',
            'real_name'       => $this->real_name ?? '',
            'avatar_url'      => $this->full_avatar_url,
            'gender'          => $this->gender,
            'province'        => $this->province ?? '',
            'role'            => $this->role,
            'status'          => $this->status,
            'created_at'      => optional($this->created_at)->toDateTimeString(),
        ];
    }
}
