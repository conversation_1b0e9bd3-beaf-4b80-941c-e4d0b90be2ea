@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/institution/staff',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">编辑员工权限</h5>

    <!-- 员工信息 -->
    <section class="border-main rounded mt-1 bg-white p-3 p-lg-5 mb-4">
      <h6 class="mb-3">员工信息</h6>
      <div class="row">
        <div class="col-md-6">
          <p><strong>姓名:</strong> {{ $permission->user->name ?? '未知用户' }}</p>
        </div>
        <div class="col-md-6">
          <p><strong>邮箱:</strong> {{ $permission->user->email ?? '无' }}</p>
        </div>
      </div>
    </section>

    <!-- 权限设置 -->
    <section class="border-main rounded mt-1 bg-white p-3 p-lg-5">
      <h6 class="mb-3">权限设置</h6>
      <form action="{{ route('institution.staff.update', $permission) }}" method="POST">
        @csrf
        @method('PUT')

        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" name="can_manage_staff" id="can_manage_staff" value="1"
              {{ old('can_manage_staff', $permission->can_manage_staff) ? 'checked' : '' }}>
            <label class="form-check-label" for="can_manage_staff">管理员工</label>
            <small class="form-text text-muted">允许管理员工账户和权限</small>
          </div>
        </div>

        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" name="can_manage_info" id="can_manage_info" value="1"
              {{ old('can_manage_info', $permission->can_manage_info) ? 'checked' : '' }}>
            <label class="form-check-label" for="can_manage_info">管理信息</label>
            <small class="form-text text-muted">允许管理机构基本信息</small>
          </div>
        </div>

        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" name="can_manage_videos" id="can_manage_videos" value="1"
              {{ old('can_manage_videos', $permission->can_manage_videos) ? 'checked' : '' }}>
            <label class="form-check-label" for="can_manage_videos">管理视频</label>
            <small class="form-text text-muted">允许上传和管理视频内容</small>
          </div>
        </div>

        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" name="can_view_data" id="can_view_data" value="1"
              {{ old('can_view_data', $permission->can_view_data) ? 'checked' : '' }}>
            <label class="form-check-label" for="can_view_data">查看数据</label>
            <small class="form-text text-muted">允许查看机构数据统计</small>
          </div>
        </div>

        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" name="can_manage_finance" id="can_manage_finance" value="1"
              {{ old('can_manage_finance', $permission->can_manage_finance) ? 'checked' : '' }}>
            <label class="form-check-label" for="can_manage_finance">财务管理</label>
            <small class="form-text text-muted">允许查看和管理财务信息</small>
          </div>
        </div>

        <div class="d-flex justify-content-between">
          <a href="{{ route('institution.staff.index') }}" class="btn btn-secondary">取消</a>
          <button type="submit" class="btn btn-primary">保存权限</button>
        </div>
      </form>
    </section>
  </div>
</div>
@endsection
