<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckInstitutionPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $nursingHomeId = $user->manage_nursing_home_id;
        if (!$nursingHomeId) {
            return redirect()->route('dashboard')->with('error', '您还未绑定机构，请联系管理员。');
        }

        // 检查权限
        if (!$user->hasInstitutionPermission($nursingHomeId, $permission)) {
            return redirect()->route('dashboard')->with('error', '您没有权限访问此页面。');
        }

        return $next($request);
    }
}
