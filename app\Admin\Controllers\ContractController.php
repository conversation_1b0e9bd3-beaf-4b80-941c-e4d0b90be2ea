<?php

namespace App\Admin\Controllers;

use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Admin\Repositories\Contract;
use Dcat\Admin\Http\Controllers\AdminController;

class ContractController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Contract::with('user'), function (Grid $grid) {

            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('user.nickname', '用户昵称');
            $grid->column('mobile');
            $grid->column('nursing_home_name');
            $grid->column('sign_at');
            $grid->column('status')->using(Contract::$statusMap)->badge(Contract::$statusColor);
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('status')->select(Contract::$statusMap)->width(3);
                $filter->equal('user_id')->select(User::all()->pluck('nickname', 'id'))->width(3);
                $filter->equal('mobile')->width(3);
                $filter->equal('nursing_home_name')->width(3);
                $filter->between('sign_at')->date()->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Contract(), function (Show $show) {
            $show->field('id');
            $show->field('mobile');
            $show->field('nursing_home_name');
            $show->field('sign_at');
            $show->field('status');
            $show->field('user_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Contract(), function (Form $form) {
            $form->select('user_id')->options(User::all()->pluck('nickname', 'id'));
            $form->text('mobile');
            $form->text('nursing_home_name');
            $form->datetime('sign_at');
            $form->radio('status')->options(Contract::$statusMap)->default(Contract::STATUS_PENDING);

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
