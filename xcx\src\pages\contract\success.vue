<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view
      class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center"
      v-else-if="showSuccess"
    >
      <!-- 签约成功信息 -->
      <view class="u-flex-col u-col-center mb-80">
        <view class="u-flex u-col-center u-row-center checkmark u-m-b-40">
          <u-image
            width="80rpx"
            height="80rpx"
            src="/static/images/icons/checkmark-orange-yellow.png"
          ></u-image>
        </view>
        <view class="u-m-b-10 u-font-18"
          >恭喜你，你的机构账号已经成功开通，</view
        >
        <view class="u-m-b-10 u-font-18">开通时间</view>
        <view>{{ signTimeText }}</view>
      </view>

      <view class="px-20">
        <u-row>
          <u-col span="6">
            <view
              class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center"
              @click="toContractPayment"
            >
              <u--text
                text="下订单"
                color="#F2AE1E"
                bold
                align="center"
                size="20"
              ></u--text>
              <text class="d-block u-font-18 mt-30">12个月套餐</text>
              <text class="d-block mt-30 fc-666">{{
                package_description
              }}</text>
            </view>
          </u-col>
          <u-col span="6">
            <view
              class="tip-wrap mx-20 p-30 bg-fff u-font-15 u-text-center"
              @click="toContractContact"
            >
              <u--text
                text="联系助教老师"
                color="#F2AE1E"
                bold
                align="center"
                size="20"
              ></u--text>
              <text class="d-block u-font-18 mt-30">{{
                contact_description
              }}</text>
            </view>
          </u-col>
        </u-row>
      </view>

      <view class="px-80 mt-80 mb-20">
        <u-row justify="center" gutter="20">
          <u-col span="6">
            <u-button
              color="#F2AE1E"
              shape="circle"
              text="返回个人中心"
              @click="toUserCenter"
            ></u-button>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/common/api";
export default {
  data() {
    return {
      loading: false,
      showSuccess: false,
      signTimeText: "",
      package_description: "",
      contact_description: "",
    };
  },
  onLoad() {
    this.loadContractData();
    this.loadConfig();
  },
  methods: {
    async loadContractData() {
      this.loading = true;
      try {
        const res = await api.getContracts();
        if (res.data.status === "success" && res.data.data) {
          const contract = res.data.data;

          // 检查是否已签约
          if (contract.status === 1) {
            this.showSuccess = true;
            this.signTimeText = contract.sign_at;
          } else {
            // 未签约，跳回第一步
            uni.showToast({
              title: "请先完成签约",
              icon: "none",
              duration: 1500,
            });
            setTimeout(() => {
              uni.redirectTo({
                url: "/pages/contract/form",
              });
            }, 1500);
          }
        } else {
          // 没有签约信息，跳回第一步
          uni.showToast({
            title: "请先填写签约信息",
            icon: "none",
            duration: 1500,
          });
          setTimeout(() => {
            uni.redirectTo({
              url: "/pages/contract/form",
            });
          }, 1500);
        }
      } catch (error) {
        console.error("加载签约信息失败:", error);
        // 发生错误时跳回第一步
        uni.showToast({
          title: "加载失败，请重试",
          icon: "none",
          duration: 1500,
        });
        setTimeout(() => {
          uni.redirectTo({
            url: "/pages/contract/form",
          });
        }, 1500);
      } finally {
        this.loading = false;
      }
    },
    loadConfig() {
      api.getConfig("package_description").then((res) => {
        this.package_description = res.data.data.value;
      });
      api.getConfig("contact_description").then((res) => {
        this.contact_description = res.data.data.value;
      });
    },

    toUserCenter() {
      uni.navigateTo({
        url: "/pages/user/index",
      });
    },
    toContractPayment() {
      uni.navigateTo({
        url: "/pages/contract/success-payment",
      });
    },
    toContractContact() {
      uni.navigateTo({
        url: "/pages/contract/success-contact",
      });
    },
  },
};
</script>

<style lang="scss">
.content {
  min-height: 100vh;
  background-color: #f8f8f8;
  line-height: 1.6;

  .checkmark img {
    width: 168upx;
    height: 168upx;
  }
}

.tip-wrap {
  border-radius: 20upx;
  min-height: 320upx;
}

.border-yellow {
  border: #f2ae1e solid 1px;
}

.border-white {
  border: white solid 1px;
}
</style>
