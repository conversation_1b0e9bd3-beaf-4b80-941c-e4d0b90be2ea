<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use App\Admin\Repositories\Feedback;
use Dcat\Admin\Http\Controllers\AdminController;

class FeedbackController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Feedback('user'), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user.nickname', '昵称');
            $grid->column('email');
            $grid->column('message', '意见')->limit(20, '...')->modal('意见', function () {
                return $this->message;
            });
            $grid->column('imageurls', '查看图片')->display(function () {
                if ($this->image_urls) {
                    return ' <span>查看图片</span>';
                }
            })->modal('查看图片', function () {
                $images = '';
                foreach ($this->fullimageurls as $image) {
                    $images .= '<img src="'.$image.'" width="300"> &nbsp;&nbsp;';
                }
                return $images;
            });
            $grid->column('status')->using(Feedback::$statusMap)->badge(Feedback::$statusColor);;
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel()->width(2);
                $filter->equal('user.nickname', '昵称')->width(3);
                $filter->equal('email')->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Feedback('user'), function (Form $form) {
            $form->display('id');
            $form->display('user.nickname', '昵称');
            $form->display('email');
            $form->display('message');
            $form->multipleImage('image_urls')->move('images/' . date("Y/m"))->sortable()->autoUpload()->uniqueName();
            $form->radio('status')->options(Feedback::$statusMap)->default('0');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
