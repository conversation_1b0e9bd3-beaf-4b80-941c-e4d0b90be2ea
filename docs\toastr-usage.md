# Toastr 全局配置使用指南

## 概述

项目已经配置了全局的 Toastr 消息提示组件，所有页面都可以直接使用，无需重复配置。

## 全局配置位置

- **Toastr配置和工具函数**：`public/js/custom.js`
- **Session消息处理**：`resources/views/web/partials/footer.blade.php`

## 支持的 Session 消息键名

### 成功消息
- `session('success')`
- `session('successMessage')`
- `session('message')`

### 错误消息
- `session('error')`
- `session('errorMessage')`

### 其他消息类型
- `session('info')` - 信息提示
- `session('warning')` - 警告提示

## JavaScript 全局工具函数

```javascript
// 成功消息
toastr.success('操作成功');

// 错误消息
toastr.error('操作失败');

// 警告消息
toastr.warning('请注意');

// 信息消息
toastr.info('提示信息');
```

## 控制器中的使用

```php
// 成功消息
return redirect()->back()->with('success', '保存成功');

// 错误消息
return redirect()->back()->with('error', '保存失败');

// 警告消息
return redirect()->back()->with('warning', '请注意数据格式');

// 信息消息
return redirect()->back()->with('info', '数据已更新');
```

## Toastr 配置选项

- **位置**: 右上角 (`toast-top-right`)
- **自动关闭**: 5秒后自动消失
- **进度条**: 显示剩余时间
- **关闭按钮**: 可手动关闭
- **动画效果**: 淡入淡出
- **防重复**: 防止相同消息重复显示

## 注意事项

1. 不需要在每个页面重复配置 Toastr
2. 使用统一的 session 键名确保消息正确显示
3. JavaScript 中优先使用 `toastr` 工具函数
4. 所有配置都是全局生效的
