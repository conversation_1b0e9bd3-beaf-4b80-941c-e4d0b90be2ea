<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->timestamp('expired_at')->nullable()->comment('过期时间')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->dropColumn('expired_at');
        });
    }
};