<?php

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Request;
use Illuminate\Contracts\Support\Arrayable;
use Rolandstarke\Thumbnail\Facades\Thumbnail;

function route_class()
{
    return str_replace('.', '-', Route::currentRouteName());
}


/**
 * 手机号加星号
 *
 * @param [type] $mobile
 * @return void
 */
function maskMobile($mobile)
{
    $maskedMobile = $mobile;
    // 检查手机号是否以1开头，并且长度为11位
    if (preg_match('/^1\d{10}$/', $mobile)) {
        // 获取手机号的前3位和后4位
        $prefix = substr($mobile, 0, 3);
        $suffix = substr($mobile, -4);

        // 构建新的手机号字符串，将中间四位用星号替换
        $maskedMobile = $prefix . '****' . $suffix;
    }

    return $maskedMobile;
}

if (!function_exists('getClientIp')) {
    /**
     * 获取客户端IP
     *
     * @return string
     */
    function getClientIp()
    {
        if (getenv('HTTP_CLIENT_IP')) {
            $ip = getenv('HTTP_CLIENT_IP');
        }
        if (getenv('HTTP_X_REAL_IP')) {
            $ip = getenv('HTTP_X_REAL_IP');
        } elseif (getenv('HTTP_X_FORWARDED_FOR')) {
            $ip = getenv('HTTP_X_FORWARDED_FOR');
            $ips = explode(',', $ip);
            $ip = $ips[0];
        } elseif (getenv('REMOTE_ADDR')) {
            $ip = getenv('REMOTE_ADDR');
        } else {
            $ip = '0.0.0.0';
        }

        return $ip;
    }
}

function getIpInfo($ip = '')
{
    if (empty($ip)) {
        return false;
    }

    // 淘宝ip接口
    // $res = @file_get_contents('http://ip.taobao.com/service/getIpInfo.php?ip=' . $ip);
    // if (empty($res)) {
    //     return false;
    // }
    // $json = json_decode($res, true);
    // if (isset($json['code']) && $json['code'] == 0) {
    //     $json['ip'] = $ip;
    //     unset($json['ret']);
    // } else {
    //     return false;
    // }
    // return $json['data'];


    // ip-api.com
    $res = @file_get_contents('http://ip-api.com/json/' . $ip . '?lang=zh-CN');
    if (empty($res)) {
        return false;
    }
    $json = json_decode($res, true);
    if (isset($json['status']) && $json['status'] == 'success') {
        return $json;
    } else {
        return false;
    }
}

if (!function_exists('isMobile')) {
    /**
     * 检测是否是移动端浏览器
     *
     * @return bool
     */
    function isMobile()
    {
        $mobiles = ['Mobile', 'iPad', 'Android', 'iPhone', 'Silk', 'Kindle', 'BlackBerry', 'Opera Mini', 'Opera Mobi'];

        return Str::contains(Request::server('HTTP_USER_AGENT'), $mobiles);
    }
}

if (!function_exists('isWeChat')) {
    /**
     * 检测是否是微信浏览器
     *
     * @return bool
     */
    function isWeChat()
    {
        return Str::contains(Request::server('HTTP_USER_AGENT'), ['MicroMessenger']);
    }
}

if (!function_exists('convertFileSize')) {
    /**
     * 转换文件大小
     *
     * @param int $size
     *
     * @return string
     */
    function convertFileSize($size)
    {
        if ($size >= pow(2, 40)) {
            $size = round($size / pow(2, 40), 2);
            $dw = "TB";
        } elseif ($size >= pow(2, 30)) {
            $size = round($size / pow(2, 30), 2);
            $dw = "GB";
        } elseif ($size >= pow(2, 20)) {
            $size = round($size / pow(2, 20), 2);
            $dw = "MB";
        } elseif ($size >= pow(2, 10)) {
            $size = round($size / pow(2, 10), 2);
            $dw = "KB";
        } else {
            $dw = "Bytes";
        }

        return $size . $dw;
    }
}

if (!function_exists('formatChineseName')) {
    /**
     * @param      $firstName
     * @param null $lastName
     *
     * @return string
     */
    function formatChineseName($firstName, $lastName = '')
    {
        $preg = chr(0xa1) . "-" . chr(0xff);
        $first = preg_match("/[$preg]+/", $firstName, $mF);
        $last = preg_match("/[$preg]+/", $lastName, $mL);
        if ($first || $last) {
            return $lastName . $firstName;
        }

        return $firstName . ' ' . $lastName;
    }
}

if (!function_exists('toArray')) {
    /**
     * 转换为数组
     *
     * @param mixed|null $value
     *
     * @return array
     */
    function toArray($value = null)
    {
        $value = value($value);
        if (is_array($value)) {
            return $value;
        }
        if ($value instanceof Arrayable) {
            return $value->toArray();
        }

        return !empty($value) ? (array)$value : [];
    }
}

if (!function_exists('arrayToTree')) {
    /**
     * 转换为树形
     *
     * @param array       $data
     * @param string|null $p_id
     * @param string      $pIdKey
     * @param string      $idKey
     * @param string      $childrenKey
     *
     * @return array
     */
    function arrayToTree(array $data = [], $p_id = null, $pIdKey = 'p_id', $idKey = 'id', $childrenKey = 'children')
    {
        $branch = [];
        foreach ($data as $item) {
            if ($item[$pIdKey] == $p_id) {
                $item['checkArr'] = [
                    "type" => "0",
                    "checked" => "0",
                ];
                $children = arrayToTree($data, $item[$idKey], $pIdKey, $idKey, $childrenKey);
                if ($children) {
                    $item[$childrenKey] = $children;
                }
                $branch[] = $item;
            }
        }

        return $branch;
    }
}

/**
 * 获取单图片地址
 *
 * @param string $image 图片路径
 */
function getImageUrl($image, $placeholder = '')
{
    $placeholder =  $placeholder ?: asset('images/placeholder.png');

    if ($image) {
        if (Str::startsWith($image, ['http://', 'https://'])) {
            return $image;
        }
        $image_path = storage_path('app/public/' . $image);
        if (file_exists($image_path)) {
            return \Storage::disk('public')->url($image);
        }
    }
    return $placeholder;
}

/**
 * 获取多图片地址
 *
 * @param string $image 图片路径
 * @param boolean $is_more 是否多图
 */
function getImageUrls($images, $placeholder = '')
{
    $placeholder =  $placeholder ?? asset('images/placeholder.png');

    // 处理多图
    $image = json_decode($images);
    $image_urls = [];
    if (is_array($image) && !empty($image)) {
        foreach ($image as $item) {
            // 如果 image 字段本身就已经是完整的 url 就直接返回
            if (Str::startsWith($item, ['http://', 'https://'])) {
                $image_urls[] = $item;
            } else {
                $image_path = storage_path('app/public/' . $item);
                if (file_exists($image_path)) {
                    $image_urls[] = \Storage::disk('public')->url($item);
                } else {
                    $image_urls[] = $placeholder;
                }
            }
        }
    }
    return $image_urls;
}

/**
 * 获取图片地址
 * https://rolandstarke.github.io/laravel-thumbnail/writing-own-filter.html
 * @param string $image 图片路径
 * @param string $width 宽度
 * @param string $height 高度
 * @param boolean $is_more 是否多图
 */
function getThumbUrl($image, $width = "", $height = "", $is_more = false)
{
    // 处理多图
    if ($is_more) {
        $image = json_decode($image);
        $image_urls = [];
        if (is_array($image) && !empty($image)) {
            foreach ($image as $item) {
                // 网络图片不处理
                if (Str::startsWith($item, ['http://', 'https://'])) {
                    return $item;
                } else {
                    $image_path = storage_path('app/public/' . $item);
                    if (file_exists($image_path)) {
                        $image_urls[] = Thumbnail::src($item, 'public')->smartcrop($width, $height)->url(true);
                    } else {
                        $image_urls[] = ''; // TODO: 裁剪placeholder
                    }
                }
            }
        }
        return $image_urls;
    }
    // 处理单图
    if ($image) {
        // 网络图片不处理
        if (Str::startsWith($image, ['http://', 'https://'])) {
            return $image;
        }
        $image_path = storage_path('app/public/' . $image);
        if (file_exists($image_path)) {
            return Thumbnail::src($image, 'public')->smartcrop($width, $height)->url(true);
        }
    }
    return ''; // TODO:裁剪placeholder
}

/**
 * 去除空格和换行符
 *
 * @param [type] $str
 * @return void
 */
function removeSpace($str)
{
    $str = str_replace(PHP_EOL, '', $str);
    $str = str_replace(array(" ", "　", "\t", "\n", "\r"), '', $str);

    return $str;
}

function ossThumb($url, $width = 200, $height = 0)
{
    // 根据实际地址判断是否是oss图片
    if (Str::contains($url, 'diary-oss.')) {
        if ($height == 0) {
            return $url . "?x-oss-process=image/resize,m_lfit,w_" . $width;
        } else {
            return $url . "?x-oss-process=image/resize,m_fill,h_" . $height . ",w_" . $width;
        }
    } else {
        return $url;
    }
}

function safeExplode(?string $input, string $delimiter = ','): array
{
    return empty($input) ? [] : explode($delimiter, $input);
}
