<?php

namespace App\Http\Controllers\Web;

use App\Models\User;
use App\Models\Comment;
use App\Models\UserLike;
use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\UserCollection;
use App\Models\UserShareRecord;
use App\Models\UserBrowseRecord;
use Illuminate\Support\Facades\DB;
use App\Models\NursingHomeUserNote;
use App\Http\Controllers\Web\WebController;


class UserManagementController extends WebController
{
    /**
     * 用户管理列表页面
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 获取当前机构
        $nursingHome = NursingHome::find($nursing_home_id);

        if (!$nursingHome) {
            return redirect()->route('home')->with(['message' => '机构信息不存在。']);
        }

        // 获取用户统计数据 - 基于user_nursing_homes表的关联
        $totalUsers = $nursingHome->users()->count();
        $newUsers24h = $nursingHome->users()->where('users.created_at', '>=', now()->subDay())->count();
        $newUsersWeek = $nursingHome->users()->where('users.created_at', '>=', now()->subWeek())->count();
        $newUsersMonth = $nursingHome->users()->where('users.created_at', '>=', now()->subMonth())->count();

        // 获取筛选参数
        $userType  = $request->get('user_type');
        $channel   = $request->get('channel');
        $userLevel = $request->get('user_level');
        $search    = $request->get('search');
        $timeFilter = $request->get('time_filter');

        // 通过关联关系获取与机构关联的用户
        $usersQuery = $nursingHome->users()
            ->leftJoin('nursing_home_user_notes', function ($join) use ($nursing_home_id) {
                $join->on('users.id', '=', 'nursing_home_user_notes.user_id')
                    ->where('nursing_home_user_notes.nursing_home_id', $nursing_home_id);
            })
            ->select(
                'users.*',
                'nursing_home_user_notes.nickname as note_nickname',
                'nursing_home_user_notes.notes',
                'nursing_home_user_notes.tags',
                'nursing_home_user_notes.user_type',
                'nursing_home_user_notes.channel',
                'nursing_home_user_notes.user_level'
            );

        // 应用筛选条件
        if ($userType !== null && $userType !== '') {
            $usersQuery->where('nursing_home_user_notes.user_type', $userType);
        }

        if ($channel !== null && $channel !== '') {
            $usersQuery->where('nursing_home_user_notes.channel', $channel);
        }

        if ($userLevel !== null && $userLevel !== '') {
            $usersQuery->where('nursing_home_user_notes.user_level', $userLevel);
        }

        // 搜索功能
        if ($search) {
            $usersQuery->where(function ($query) use ($search) {
                $query->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.nickname', 'like', "%{$search}%")
                    ->orWhere('users.email', 'like', "%{$search}%")
                    ->orWhere('nursing_home_user_notes.nickname', 'like', "%{$search}%");
            });
        }

        // 时间筛选功能
        if ($timeFilter) {
            switch ($timeFilter) {
                case '24h':
                    $usersQuery->where('users.created_at', '>=', now()->subDay());
                    break;
                case 'week':
                    $usersQuery->where('users.created_at', '>=', now()->subWeek());
                    break;
                case 'month':
                    $usersQuery->where('users.created_at', '>=', now()->subMonth());
                    break;
            }
        }

        $users = $usersQuery->orderBy('users.created_at', 'desc')->paginate(20);

        return view('user-management.index', compact(
            'users',
            'userType',
            'channel',
            'userLevel',
            'search',
            'timeFilter',
            'totalUsers',
            'newUsers24h',
            'newUsersWeek',
            'newUsersMonth'
        ));
    }

    /**
     * 编辑用户备注页面
     */
    public function edit(Request $request, $userId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 检查用户是否与机构关联，并加载pivot关系数据
        $targetUser = User::with(['nursingHomes' => function ($query) use ($nursing_home_id) {
            $query->where('nursing_home_id', $nursing_home_id);
        }])->find($userId);

        if (!$targetUser) {
            return redirect()->route('user-management.index')->with(['error' => '用户不存在。']);
        }

        // 检查用户是否设置了该机构为"我的养老机构"
        $isAssociated = $targetUser->nursingHomes->isNotEmpty();

        if (!$isAssociated) {
            return redirect()->route('user-management.index')->with(['error' => '该用户未关联到您的机构。']);
        }

        // 设置pivot数据以便在视图中访问
        if ($targetUser->nursingHomes->isNotEmpty()) {
            $targetUser->pivot = $targetUser->nursingHomes->first()->pivot;
        }

        // 获取或创建用户备注记录
        $userNote = NursingHomeUserNote::firstOrCreate(
            [
                'nursing_home_id' => $nursing_home_id,
                'user_id' => $userId
            ],
            [
                'nickname' => '',
                'notes' => '',
                'tags' => [],
                'user_type' => NursingHomeUserNote::USER_TYPE_NONE,
                'channel' => NursingHomeUserNote::CHANNEL_NONE,
                'user_level' => NursingHomeUserNote::LEVEL_NONE,
            ]
        );

        return view('user-management.edit', compact('targetUser', 'userNote'));
    }

    /**
     * 更新用户备注
     */
    public function update(Request $request, $userId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return response()->json(['success' => false, 'message' => '您还未绑定机构。']);
        }

        $request->validate([
            'nickname' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'tags' => 'nullable|array',
            'user_type' => 'nullable|integer|in:0,1,2,3,4',
            'channel' => 'nullable|integer|in:0,1,2,3',
            'user_level' => 'nullable|integer|in:0,1,2,3',
        ]);

        // 检查用户是否与机构关联
        $isAssociated = \App\Models\UserNursingHome::where('user_id', $userId)
            ->where('nursing_home_id', $nursing_home_id)
            ->exists();

        if (!$isAssociated) {
            return response()->json(['success' => false, 'message' => '该用户未关联到您的机构。']);
        }

        // 更新或创建用户备注
        $userNote = NursingHomeUserNote::updateOrCreate(
            [
                'nursing_home_id' => $nursing_home_id,
                'user_id' => $userId
            ],
            [
                'nickname' => $request->nickname,
                'notes' => $request->notes,
                'tags' => $request->tags ?? [],
                'user_type' => $request->user_type ?? NursingHomeUserNote::USER_TYPE_NONE,
                'channel' => $request->channel ?? NursingHomeUserNote::CHANNEL_NONE,
                'user_level' => $request->user_level ?? NursingHomeUserNote::LEVEL_NONE,
            ]
        );

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => '更新成功']);
        }

        return redirect()->route('user-management.index')->with(['success' => '用户信息更新成功。']);
    }

    /**
     * 用户详情
     */
    public function detail(Request $request, $userId)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 获取当前机构
        $nursingHome = NursingHome::find($nursing_home_id);
        if (!$nursingHome) {
            return redirect()->route('dashboard')->with(['message' => '机构信息不存在。']);
        }

        // 获取用户详情
        $targetUser = User::with(['nursingHomes' => function ($query) use ($nursing_home_id) {
            $query->where('nursing_home_id', $nursing_home_id);
        }])->find($userId);

        if (!$targetUser) {
            return redirect()->route('user-management.index')->with(['error' => '用户不存在。']);
        }

        // 检查用户是否设置了该机构为"我的养老机构"
        $isAssociated = $targetUser->nursingHomes->isNotEmpty();

        if (!$isAssociated) {
            return redirect()->route('user-management.index')->with(['error' => '该用户未关联到您的机构。']);
        }

        // 获取日期范围参数
        $dateRange = $request->get('date_range', 'all');
        $startDate = match ($dateRange) {
            '7' => now()->subDays(6)->startOfDay(),
            '30' => now()->subDays(29)->startOfDay(),
            '90' => now()->subDays(89)->startOfDay(),
            '365' => now()->subDays(364)->startOfDay(),
            'all' => now()->subYears(10)->startOfDay(),
            default => now()->subYears(10)->startOfDay()
        };

        // 获取用户备注信息
        $userNote = NursingHomeUserNote::where('nursing_home_id', $nursing_home_id)
            ->where('user_id', $userId)
            ->first();

        // 获取用户详细统计数据
        $userStats = $this->getSingleUserStatistics($userId, $nursing_home_id, $startDate);

        // 获取每日趋势数据
        $dailyTrends = $this->getUserDailyTrends($userId, $nursing_home_id, $startDate);

        // 获取互动内容详情
        $interactionDetails = $this->getUserInteractionDetails($userId, $nursing_home_id, $startDate);

        return view('user-management.detail', compact(
            'targetUser',
            'nursingHome',
            'userNote',
            'userStats',
            'dailyTrends',
            'interactionDetails',
            'dateRange'
        ));
    }

    /**
     * 获取单个用户的详细统计数据
     */
    private function getSingleUserStatistics($userId, $nursingHomeId, $startDate)
    {
        // 浏览统计
        $browseStats = UserBrowseRecord::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(*) as total_views'),
                DB::raw('SUM(browse_duration) as total_duration'),
                DB::raw('AVG(browse_duration) as avg_duration'),
                DB::raw('MIN(created_at) as first_view_at'),
                DB::raw('MAX(created_at) as last_view_at')
            )
            ->first();

        // 分享统计
        $shareStats = UserShareRecord::where('from_user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(*) as total_shares'),
                DB::raw('MIN(created_at) as first_share_at'),
                DB::raw('MAX(created_at) as last_share_at')
            )
            ->first();
        
        // 确保返回的对象不为null
        if (!$shareStats) {
            $shareStats = (object) [
                'total_shares' => 0,
                'first_share_at' => null,
                'last_share_at' => null
            ];
        }

        // 收藏统计
        $collectionStats = UserCollection::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(*) as total_collections'),
                DB::raw('MIN(created_at) as first_collection_at'),
                DB::raw('MAX(created_at) as last_collection_at')
            )
            ->first();
        
        // 确保返回的对象不为null
        if (!$collectionStats) {
            $collectionStats = (object) [
                'total_collections' => 0,
                'first_collection_at' => null,
                'last_collection_at' => null
            ];
        }

        // 点赞统计
        $likeStats = UserLike::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(*) as total_likes'),
                DB::raw('MIN(created_at) as first_like_at'),
                DB::raw('MAX(created_at) as last_like_at')
            )
            ->first();
        
        // 确保返回的对象不为null
        if (!$likeStats) {
            $likeStats = (object) [
                'total_likes' => 0,
                'first_like_at' => null,
                'last_like_at' => null
            ];
        }

        // 评论统计
        $commentStats = Comment::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('COUNT(*) as total_comments'),
                DB::raw('MIN(created_at) as first_comment_at'),
                DB::raw('MAX(created_at) as last_comment_at')
            )
            ->first();
        
        // 确保返回的对象不为null
        if (!$commentStats) {
            $commentStats = (object) [
                'total_comments' => 0,
                'first_comment_at' => null,
                'last_comment_at' => null
            ];
        }

        // 计算参与度评分
        $engagementScore = $this->calculateSingleUserEngagementScore(
            $browseStats,
            $shareStats,
            $collectionStats,
            $likeStats,
            $commentStats
        );

        return [
            'total_views' => $browseStats->total_views ?? 0,
            'total_duration' => $browseStats->total_duration ?? 0,
            'avg_duration' => $browseStats->avg_duration ?? 0,
            'total_shares' => $shareStats->total_shares ?? 0,
            'share_reach' => 0,
            'total_collections' => $collectionStats->total_collections ?? 0,
            'total_likes' => $likeStats->total_likes ?? 0,
            'total_comments' => $commentStats->total_comments ?? 0,
            'engagement_score' => $engagementScore,
            'first_interaction_at' => $browseStats->first_view_at ?? null,
            'last_interaction_at' => $browseStats->last_view_at ?? null,
            'first_share_at' => $shareStats->first_share_at ?? null,
            'last_share_at' => $shareStats->last_share_at ?? null,
            'first_collection_at' => $collectionStats->first_collection_at ?? null,
            'last_collection_at' => $collectionStats->last_collection_at ?? null,
            'first_like_at' => $likeStats->first_like_at ?? null,
            'last_like_at' => $likeStats->last_like_at ?? null,
            'first_comment_at' => $commentStats->first_comment_at ?? null,
            'last_comment_at' => $commentStats->last_comment_at ?? null,
        ];
    }

    /**
     * 获取用户每日趋势数据
     */
    private function getUserDailyTrends($userId, $nursingHomeId, $startDate)
    {
        // 浏览趋势
        $viewTrends = UserBrowseRecord::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as views'),
                DB::raw('SUM(browse_duration) as duration')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 分享趋势
        $shareTrends = UserShareRecord::where('from_user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as shares')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 收藏趋势
        $collectionTrends = UserCollection::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as collections')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 点赞趋势
        $likeTrends = UserLike::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as likes')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 评论趋势
        $commentTrends = Comment::where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as comments')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // 合并所有趋势数据
        $dates = collect()
            ->merge($viewTrends->keys())
            ->merge($shareTrends->keys())
            ->merge($collectionTrends->keys())
            ->merge($likeTrends->keys())
            ->merge($commentTrends->keys())
            ->unique()
            ->sort()
            ->values();

        $trends = [];
        foreach ($dates as $date) {
            $trends[] = [
                'date' => $date,
                'views' => $viewTrends[$date]->views ?? 0,
                'duration' => $viewTrends[$date]->duration ?? 0,
                'shares' => $shareTrends[$date]->shares ?? 0,
                'reach' => 0,
                'collections' => $collectionTrends[$date]->collections ?? 0,
                'likes' => $likeTrends[$date]->likes ?? 0,
                'comments' => $commentTrends[$date]->comments ?? 0,
            ];
        }

        return $trends;
    }

    /**
     * 获取用户互动内容详情
     */
    private function getUserInteractionDetails($userId, $nursingHomeId, $startDate)
    {
        // 最近浏览的内容
        $recentViews = UserBrowseRecord::with(['diary' => function($query) {
                $query->select('id', 'title', 'cover_url', 'created_at');
            }])
            ->where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 最近分享的内容
        $recentShares = UserShareRecord::with(['diary' => function($query) {
                $query->select('id', 'title', 'cover_url', 'created_at');
            }])
            ->where('from_user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 最近收藏的内容
        $recentCollections = UserCollection::with(['diary' => function($query) {
                $query->select('id', 'title', 'cover_url', 'created_at');
            }])
            ->where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 最近点赞的内容
        $recentLikes = UserLike::with(['diary' => function($query) {
                $query->select('id', 'title', 'cover_url', 'created_at');
            }])
            ->where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // 最近评论的内容
        $recentComments = Comment::with(['diary' => function($query) {
                $query->select('id', 'title', 'cover_url', 'created_at');
            }])
            ->where('user_id', $userId)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'recent_views' => $recentViews,
            'recent_shares' => $recentShares,
            'recent_collections' => $recentCollections,
            'recent_likes' => $recentLikes,
            'recent_comments' => $recentComments,
        ];
    }

    /**
     * 计算单个用户的参与度评分
     */
    private function calculateSingleUserEngagementScore($browse, $share, $collection, $like, $comment)
    {
        $score = 0;

        // 浏览权重：1分/次
        $score += ($browse ? $browse->total_views : 0) * 1;

        // 分享权重：5分/次
        $score += ($share ? $share->total_shares : 0) * 5;

        // 收藏权重：3分/次
        $score += ($collection ? $collection->total_collections : 0) * 3;

        // 点赞权重：2分/次
        $score += ($like ? $like->total_likes : 0) * 2;

        // 评论权重：4分/次
        $score += ($comment ? $comment->total_comments : 0) * 4;

        return $score;
    }

    /**
     * 用户数据统计页面
     */
    public function statistics(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 获取当前机构
        $nursingHome = NursingHome::find($nursing_home_id);
        if (!$nursingHome) {
            return redirect()->route('dashboard')->with(['message' => '机构信息不存在。']);
        }

        // 获取筛选参数
        $dateRange = $request->get('date_range', '30'); // 默认30天
        $search = $request->get('search', '');
        $sortBy = $request->get('sort_by', 'total_views'); // 排序字段

        // 计算日期范围
        $startDate = match ($dateRange) {
            '7' => now()->subDays(6)->startOfDay(),
            '30' => now()->subDays(29)->startOfDay(),
            '90' => now()->subDays(89)->startOfDay(),
            '365' => now()->subDays(364)->startOfDay(),
            default => now()->subDays(29)->startOfDay()
        };

        // 获取与机构关联的用户列表
        $usersQuery = $nursingHome->users()
            ->leftJoin('nursing_home_user_notes', function ($join) use ($nursing_home_id) {
                $join->on('users.id', '=', 'nursing_home_user_notes.user_id')
                    ->where('nursing_home_user_notes.nursing_home_id', $nursing_home_id);
            })
            ->select('users.*', 'nursing_home_user_notes.nickname as note_nickname');

        // 搜索功能
        if ($search) {
            $usersQuery->where(function ($query) use ($search) {
                $query->where('users.name', 'like', "%{$search}%")
                    ->orWhere('users.nickname', 'like', "%{$search}%")
                    ->orWhere('users.email', 'like', "%{$search}%")
                    ->orWhere('nursing_home_user_notes.nickname', 'like', "%{$search}%");
            });
        }

        // 获取用户ID列表用于统计
        $userIds = $usersQuery->pluck('users.id')->toArray();

        // 获取用户的综合统计数据
        $userStatistics = $this->getUserStatistics($userIds, $nursing_home_id, $startDate);

        // 应用排序
        $userStatistics = collect($userStatistics)->sortByDesc($sortBy)->values()->all();

        // 分页处理
        $perPage = 20;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedStats = array_slice($userStatistics, $offset, $perPage);

        // 创建分页器
        $paginatedUsers = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedStats,
            count($userStatistics),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // 汇总统计数据
        $summaryStats = $this->getSummaryStatistics($userStatistics);

        return view('user-management.statistics', compact(
            'paginatedUsers',
            'summaryStats',
            'dateRange',
            'search',
            'sortBy',
            'nursingHome'
        ));
    }

    /**
     * 获取用户的综合统计数据
     */
    private function getUserStatistics($userIds, $nursingHomeId, $startDate)
    {
        $statistics = [];

        // 基础用户信息
        $users = User::whereIn('id', $userIds)->get()->keyBy('id');

        // 浏览统计数据
        $browseStats = UserBrowseRecord::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select('user_id', DB::raw('COUNT(*) as view_count'), DB::raw('SUM(browse_duration) as total_duration'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 分享统计数据
        $shareStats = UserShareRecord::whereIn('from_user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select('from_user_id', DB::raw('COUNT(*) as share_count'))
            ->groupBy('from_user_id')
            ->get()
            ->keyBy('from_user_id');

        // 收藏统计数据
        $collectionStats = UserCollection::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select('user_id', DB::raw('COUNT(*) as collection_count'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 点赞统计数据
        $likeStats = UserLike::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select('user_id', DB::raw('COUNT(*) as like_count'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 评论统计数据
        $commentStats = Comment::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->where('created_at', '>=', $startDate)
            ->select('user_id', DB::raw('COUNT(*) as comment_count'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 首次互动时间
        $firstInteractions = UserBrowseRecord::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->select('user_id', DB::raw('MIN(created_at) as first_interaction_at'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 最近互动时间
        $lastInteractions = UserBrowseRecord::whereIn('user_id', $userIds)
            ->where('nursing_home_id', $nursingHomeId)
            ->select('user_id', DB::raw('MAX(created_at) as last_interaction_at'))
            ->groupBy('user_id')
            ->get()
            ->keyBy('user_id');

        // 用户备注信息
        $userNotes = \App\Models\NursingHomeUserNote::where('nursing_home_id', $nursingHomeId)
            ->whereIn('user_id', $userIds)
            ->get()
            ->keyBy('user_id');

        // 构建统计数组
        foreach ($userIds as $userId) {
            $user = $users[$userId] ?? null;
            if (!$user) continue;

            $browse = $browseStats[$userId] ?? null;
            $share = $shareStats[$userId] ?? null;
            $collection = $collectionStats[$userId] ?? null;
            $like = $likeStats[$userId] ?? null;
            $comment = $commentStats[$userId] ?? null;
            $firstInteraction = $firstInteractions[$userId] ?? null;
            $lastInteraction = $lastInteractions[$userId] ?? null;
            $userNote = $userNotes[$userId] ?? null;

            $statistics[] = [
                'user' => $user,
                'user_note' => $userNote,
                'total_views' => $browse ? $browse->view_count : 0,
                'total_duration' => $browse ? $browse->total_duration : 0,
                'total_shares' => $share ? $share->share_count : 0,
                'total_collections' => $collection ? $collection->collection_count : 0,
                'total_likes' => $like ? $like->like_count : 0,
                'total_comments' => $comment ? $comment->comment_count : 0,
                'total_interactions' => ($browse ? $browse->view_count : 0) +
                    ($share ? $share->share_count : 0) +
                    ($collection ? $collection->collection_count : 0) +
                    ($like ? $like->like_count : 0) +
                    ($comment ? $comment->comment_count : 0),
                'first_interaction_at' => $firstInteraction ? $firstInteraction->first_interaction_at : null,
                'last_interaction_at' => $lastInteraction ? $lastInteraction->last_interaction_at : null,
                'avg_duration' => $browse && $browse->view_count > 0 ? round($browse->total_duration / $browse->view_count, 2) : 0,
                'engagement_score' => $this->calculateEngagementScore($browse, $share, $collection, $like, $comment)
            ];
        }

        return $statistics;
    }

    /**
     * 计算用户参与度评分
     */
    private function calculateEngagementScore($browse, $share, $collection, $like, $comment)
    {
        $score = 0;

        // 浏览权重：1分/次
        $score += ($browse ? $browse->view_count : 0) * 1;

        // 分享权重：5分/次
        $score += ($share ? $share->share_count : 0) * 5;

        // 收藏权重：3分/次
        $score += ($collection ? $collection->collection_count : 0) * 3;

        // 点赞权重：2分/次
        $score += ($like ? $like->like_count : 0) * 2;

        // 评论权重：4分/次
        $score += ($comment ? $comment->comment_count : 0) * 4;

        return $score;
    }

    /**
     * 获取汇总统计数据
     */
    private function getSummaryStatistics($userStatistics)
    {
        if (empty($userStatistics)) {
            return [
                'total_users' => 0,
                'total_views' => 0,
                'total_shares' => 0,
                'total_collections' => 0,
                'total_likes' => 0,
                'total_comments' => 0,
                'total_interactions' => 0,
                'total_duration' => 0,
                'avg_engagement' => 0
            ];
        }

        $totalUsers = count($userStatistics);
        $totalViews = array_sum(array_column($userStatistics, 'total_views'));
        $totalShares = array_sum(array_column($userStatistics, 'total_shares'));
        $totalCollections = array_sum(array_column($userStatistics, 'total_collections'));
        $totalLikes = array_sum(array_column($userStatistics, 'total_likes'));
        $totalComments = array_sum(array_column($userStatistics, 'total_comments'));
        $totalInteractions = array_sum(array_column($userStatistics, 'total_interactions'));
        $totalDuration = array_sum(array_column($userStatistics, 'total_duration'));
        $totalEngagement = array_sum(array_column($userStatistics, 'engagement_score'));

        return [
            'total_users' => $totalUsers,
            'total_views' => $totalViews,
            'total_shares' => $totalShares,
            'total_collections' => $totalCollections,
            'total_likes' => $totalLikes,
            'total_comments' => $totalComments,
            'total_interactions' => $totalInteractions,
            'total_duration' => $totalDuration,
            'avg_engagement' => round($totalEngagement / max($totalUsers, 1), 2)
        ];
    }
}
