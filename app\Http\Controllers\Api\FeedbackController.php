<?php

namespace App\Http\Controllers\Api;

use App\Models\Feedback;
use Illuminate\Http\Request;
use App\Services\QcloudService;
use Illuminate\Support\Facades\Mail;
use App\Http\Resources\FeedbackResource;
use App\Http\Controllers\Api\ApiController;

class FeedbackController extends ApiController
{

    /**
     * 意见反馈列表
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $limit = $request->limit ?? 10;

        if (!$user) {
            $this->errorBadRequest('请先登录');
        }

        $result = Feedback::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
        return $this->success(FeedbackResource::collection($result));
    }

    /**
     * 添加意见反馈
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $user = $request->user();
        $request->validate(
            [
                'message'    => 'required'
            ],
            [
                'message.required'    => '请输入意见反馈',
            ]
        );

        // 文本审核
        // $content = $request->message;
        // $qcloud_service = new QcloudService();
        // if (!$qcloud_service->detectText($content)) {
        //     return $this->errorBadRequest('内容含有敏感词，请调整后重新提交');
        // }

        Feedback::create([
            'user_id'    => $user->id ?? 0,
            'email'      => $request->email,
            'image_urls' => $request->image_urls,
            'message'    => $request->message,
        ]);

        // 发送邮件通知
        // Mail::send('emails.feedback', ['email_content' => $request->message], function ($message) {
        //     $to = '<EMAIL>';
        //     $message->to($to)->subject('意见反馈-' . config('app.name'));
        // });

        return $this->ok('提交成功');
    }

    /**
     * 意见反馈详情
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $feedback = Feedback::find($id);
        if (!$feedback) {
            $this->errorBadRequest('暂无数据');
        }
        if ($feedback->user_id != $user->id) {
            $this->errorBadRequest('无权限');
        }
        return $this->success(new FeedbackResource($feedback));
    }

    /**
     * 删除意见反馈
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $user = $request->user();
        $feedback = Feedback::find($id);
        if (!$feedback) {
            $this->errorBadRequest('暂无数据');
        }
        if ($feedback->user_id != $user->id) {
            $this->errorBadRequest('无权限');
        }
        $feedback->delete();
        return $this->ok('删除成功');
    }
}
