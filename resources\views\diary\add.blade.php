@extends('layouts.web')

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">

    <section class="p-3 rounded bg-white">
      <h6 class="mb-0">发布作品</h6>
    </section>

    @if (session('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif
    @if ($errors->any())
    <div class="alert alert-danger">
      <ul>
        @foreach ($errors->all() as $error)
        <li>{{ $error }}</li>
        @endforeach
      </ul>
    </div>
    @endif

    <section class="rounded mt-1 bg-white p-4">
      <form class="form-wrap" method="post" enctype="multipart/form-data" action="{{ route('diary.store') }}">
        @csrf
        <div class="mb-4">
          <input type="text" class="form-control" required name="title" id="title"
            value="{{ old('title') ?? '' }}" placeholder="请输入标题">
        </div>
        <div class="mb-4">
          <input type="file" class="form-control mb-4" accept="video/*" id="upload_video_url" name="file">
        </div>
        <div class="text-right">
          <input type="hidden" id="uploaded_video_url" name="uploaded_video_url" value="">
          <button type="submit" id="submit-btn" class="btn btn-primary text-nowrap btn-lg px-5">提交</button>
        </div>
      </form>
    </section>

  </div>
</div>
@endsection

@section('js')
<script>
  $(document).ready(function() {
    var config = @json($params, JSON_UNESCAPED_SLASHES);
    var deleteUrl = "{{ route('upload.video.delete') }}";

    var uploadData = {};
    uploadData.key = '';
    uploadData.policy = config.policy
    uploadData.OSSAccessKeyId = config.accessid
    uploadData.success_action_status = '200'
    uploadData.signature = config.signature

    var uploadFileUrl = '';
    var file_;
    var fileName;

    // initialize with options
    var $el1 = $("#upload_video_url");
    $el1.fileinput({
      language: 'zh',
      allowedFileExtensions: ['mp4', 'mov'],
      uploadUrl: config.host,
      deleteUrl: deleteUrl,
      uploadAsync: true,
      previewFileType: 'any',
      showUpload: false,
      overwriteInitial: false,
      minFileCount: 1,
      maxFileCount: 1,
      browseOnZoneClick: true,
      initialPreviewAsData: true,
      fileActionSettings: {
        showUpload: false,
        showRemove: true
      }
    }).on('filebeforeload', function(event, file, index, reader) {
      file_ = file;
      var ext = file.name.split('.').pop();
      // 修改文件名生成逻辑
      var timestamp = Date.parse(new Date());
      var randomString = generateRandomString(10);
      fileName = timestamp + '_' + randomString + '.' + ext;
      uploadData.key = config.dir + fileName;
      uploadFileUrl = config.url + fileName;
    }).on("filebatchselected", function(event, files) {
      // 锁定组件防止重复操作
      $el1.fileinput('lock');

      // 禁用提交按钮并显示上传状态
      $('#submit-btn').prop('disabled', true).text('上传中...');

      // 构建表单数据
      const formData = new FormData();
      formData.append('key', uploadData.key);
      formData.append('policy', uploadData.policy);
      formData.append('OSSAccessKeyId', uploadData.OSSAccessKeyId);
      formData.append('success_action_status', uploadData.success_action_status);
      formData.append('signature', uploadData.signature);
      formData.append('file', file_);

      const ossUrl = `${config.url}${fileName}`;

      // 自定义上传
      $.ajax({
        url: config.host,
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        complete: () => {
          $el1.fileinput('unlock'); // 无论成功失败都解锁
        },
        success: (response) => {
          // 更新组件状态
          $("#uploaded_video_url").val(ossUrl);
          toastr.success('上传成功');
          // 启用提交按钮
          $('#submit-btn').prop('disabled', false).text('提交');
        },
        error: (xhr) => {
          $el1.fileinput('clear');
          const errorMsg = $(xhr.responseText).find('Message').text();
          toastr.error(`上传失败: ${errorMsg}`);
          // 启用提交按钮
          $('#submit-btn').prop('disabled', false).text('提交');
        }
      });
    }).on('fileremoved', function(event, id, index) {
      console.log('fileremoved');
      $.ajax({
        url: deleteUrl,
        type: 'POST',
        data: {
          key: uploadData.key
        },
        success: () => {
          $("#uploaded_video_url").val('');
          toastr.success('文件已删除');
        },
        error: () => {
          toastr.error('删除失败，文件可能仍存在');
          $el1.fileinput('undo'); // 恢复已删除的文件
        }
      })
    }).on('fileuploaded', function(event, data, previewId, index, fileId) {
      console.log('fileuploaded');
    });

    $('.fileinput-remove-button').click(function() {
      $.ajax({
        url: deleteUrl,
        type: 'POST',
        data: {
          key: uploadData.key
        },
        success: () => {
          $el1.fileinput('clear');
          $("#uploaded_video_url").val('');
          toastr.success('文件已删除');
        },
        error: () => {
          toastr.error('删除失败，文件可能仍存在');
          $el1.fileinput('undo'); // 恢复已删除的文件
        }
      })
    })
  });

  // 生成随机字符串的函数
  function generateRandomString(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }
</script>
@endsection
