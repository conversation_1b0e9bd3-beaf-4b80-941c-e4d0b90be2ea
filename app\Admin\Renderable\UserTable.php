<?php

namespace App\Admin\Renderable;

use App\Models\User;
use Dcat\Admin\Grid;
use App\Models\NursingHome;
use Dcat\Admin\Grid\LazyRenderable;

class UserTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new User(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('mobile', '手机号');
            $grid->column('name', '姓名');
            $grid->column('nickname', '昵称');
            $grid->column('created_at');

            $grid->quickSearch(['id', 'mobile', 'name', 'nickname']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('mobile', '手机号')->width(4);
                $filter->like('name', '姓名')->width(4);
                $filter->like('nickname', '昵称')->width(4);
                $filter->equal('nursing_home_id', '养老院')->select(NursingHome::all()->pluck('name', 'id'))->width(4);
            });
        });
    }
}
