<?php

namespace App\Admin\Actions\Form;

use App\Models\NewsCategory;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;
use App\Admin\Actions\Imports\NewsImport;

class NewsForm extends Form
{
    public function handle(array $input)
    {
        try {
            //上传文件位置，这里默认是在storage中，如有修改请对应替换
            $file = storage_path('/app/public/' . $input['file']);

            // 带分类参数的导入
            $import = new NewsImport($input['params_id']);
            Excel::import($import, $file);

            return $this->response()->success('数据已导入' . $import->getSuccessRows() . '行，未导入' . $import->getFailedRows() . '行')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    public function form()
    {
        $this->select('params_id', '选择分类')
            ->options(NewsCategory::get()
            ->pluck('title', 'id'))->required();

        $this->file('file', '上传数据（Excel）')
            ->rules('required', ['required' => '文件不能为空'])
            ->move('import/upload/')->autoUpload();
    }

    /**
     * 返回表单数据，如不需要可以删除此方法
     *
     * @return array
     */
    public function default()
    {
        return [
            'params_id' => null,
            'file'       => ''
        ];
    }

}
