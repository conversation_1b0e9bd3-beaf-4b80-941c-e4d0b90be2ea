<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;

class DashboardController extends WebController
{

    /**
     * 用户登录后的欢迎页
     */
    public function dashboard(Request $request)
    {
        // 确保用户已登录
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return view('institution.empty');
        }

        return view('dashboard');
    }
}
