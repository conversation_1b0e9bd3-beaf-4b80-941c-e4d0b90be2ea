<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Diary;
use App\Models\Contract;
use App\Models\NursingHome;
use Illuminate\Http\Request;
use App\Models\UserNursingHome;
use App\Http\Resources\ContractResource;
use App\Services\QywxNotificationService;
use App\Admin\Repositories\User as UserRepo;
use App\Admin\Repositories\Contract as ContractRepo;

class ContractController extends ApiController
{
    /**
     * 签约详情
     *
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $user_id = $user->id;
        $result = Contract::where('user_id', $user_id)->first();
        if ($result) {
            return $this->success(new ContractResource($result));
        }
        // 返回空数据而不是错误，让前端更好处理
        return $this->success(null);
    }

    /**
     * 签约第一步
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $user_id = $user->id;
        $request->validate(
            [
                'mobile'            => 'required',
                'nursing_home_name' => 'required',
            ],
            [
                'mobile.required'            => '手机号不能为空',
                'nursing_home_name.required' => '机构名称不能为空',
            ]
        );

        $mobile = $request->mobile;
        $nursing_home_name = $request->nursing_home_name;

        $result = Contract::updateOrCreate(
            [
                'user_id' => $user_id,
            ],
            [
                'mobile'            => $mobile,
                'nursing_home_name' => $nursing_home_name,
                'sign_at'           => null,
                'status'            => ContractRepo::STATUS_PENDING,
            ]
        );
        return $this->ok('保存成功');
    }

    /**
     * 签约第二步
     */
    public function update(Request $request)
    {
        $user = $request->user();
        $user_id = $user->id;

        $result = Contract::where('user_id', $user_id)->first();
        if ($result) {
            $result->update([
                'sign_at' => now(),
                'status' => ContractRepo::STATUS_SIGNED,
            ]);

            // 创建养老院，并设置过期时间为30天
            $nursingHome = NursingHome::create([
                'name'       => $result->nursing_home_name,
                'mobile'     => $result->mobile,
                'created_by' => $user_id,
                'expired_at' => now()->addDays(30),    // 默认有1个月的有效期
                'status'     => 0,                            // 未审核
            ]);

            // 申请开通的机构自动加入
            UserNursingHome::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'nursing_home_id' => $nursingHome->id,
                ]
            );

            // 设置用户的默认养老院，角色为机构超级管理员
            $user->update([
                'nursing_home_id'        => $nursingHome->id,
                'role'                   => UserRepo::ROLE_NURSINGHOME,
                'manage_nursing_home_id' => $nursingHome->id,
            ]);

            // 复制一条默认日记给当前机构
            $defaultDiary = Diary::find(21);
            if ($defaultDiary) {
                // 复制后，修改nursing_home_id
                $newDiary = $defaultDiary->replicate();
                $newDiary->nursing_home_id = $nursingHome->id;
                $newDiary->save();
            }

            // 发送企业微信消息通知
            $qywxService = new QywxNotificationService();

            $webhookUrl = config('app.qywx.robot.new_signing');
            $qywxService->setWebhookUrl($webhookUrl);

            // 通知对象是企业微信销售群，告知客户签约成功。
            $mobile = $user->mobile;

            if ($user->sales_id) {
                $sales = User::where('id', $user->sales_id)->first();
                $sales_name = $sales->nickname;
            } else {
                $sales_name = '无';
            }

            $content = "客户 {$user->nickname} 已成功签约";
            $content .= "\n";
            $content .= "签约机构：{$result->nursing_home_name}";
            $content .= "\n";
            $content .= "联系电话：{$mobile}";
            $content .= "\n";
            $content .= "销售人员：{$sales_name}";

            $qywxService->sendTextNotification($content);

            return $this->ok('签约成功');
        }
        // 返回空数据而不是错误，让前端更好处理
        return $this->success(null);
    }
}
