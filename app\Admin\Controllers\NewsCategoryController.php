<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\NewsCategory as NewsCategoryModels;
use App\Admin\Repositories\NewsCategory;
use App\Admin\Repositories\Common;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Exports\NewsCategoryExport;
use App\Admin\Actions\Restore\BatchRestore;
use Dcat\Admin\Http\Controllers\AdminController;

class NewsCategoryController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new NewsCategory(), function (Grid $grid) {
            $grid->title->tree(true);
            $grid->order->orderable();
            $grid->column('status')->using(Common::$activeMap)->badge(Common::$statusColor);
            $grid->column('updated_at')->sortable();
            $grid->column('添加子页面')->display(function () {
                return ' <a href="' . admin_route('news-categories.create', ['parent_id' => $this->id]) . '" class="btn btn-sm btn-outline-info" target="_blank">添加</a>';
            });
            // 筛选
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title');
                $filter->equal('status', '状态')->radio(Common::$activeMap);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(NewsCategoryModels::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(NewsCategoryModels::class));
                }
            });
            $grid->tools([
                '<a class="btn btn-primary btn-mini btn-outline pull-right ml-1" style="color:' .  admin_color('primary') . '" href="' . admin_route('news-categories.export', request()->all()) . '" target="_blank"><i class="fa fa-download"></i> 导出</a>',
            ]);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NewsCategory(), function (Show $show) {
            $show->parent_id()->as(function () {
                $show_text = '顶级';
                if($this->parent_id){
                    $find_parent = NewsCategory::find($this->parent_id);
                    if($find_parent){
                        $show_text = json_decode($find_parent, true)['title'];
                    }
                }
                return $show_text;
            });
            $show->field('title');
            $show->field('sort_order');
            $show->field('status')->using(Common::$activeMap)->dot(Common::$statusColor);
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new NewsCategory(), function (Form $form) {
            if (request('parent_id')) {
                $page = NewsCategoryModels::find(request('parent_id'));
                $form->display('parent_id', '父级')->default($page->title);
                $form->hidden('parent_id')->default(request('parent_id'));
            } else {
                $form->select('parent_id', '父级')->options(NewsCategoryModels::selectOptions())->required()->default(0);
            }
            $form->text('title')->required();
            $form->number('sort_order');
            $form->switch('status')->default(1);
            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public function export()
    {
        return (new NewsCategoryExport(request()->all()))->download(date("Ymd") . "-分类.xlsx");
    }
}
