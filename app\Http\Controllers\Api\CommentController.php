<?php

namespace App\Http\Controllers\Api;

use App\Models\Diary;
use App\Models\Comment;
use Illuminate\Http\Request;
use App\Services\QcloudService;
use App\Http\Resources\CommentResource;

class CommentController extends ApiController
{
    /**
     * 评论
     *
     * @param  $request
     * @return \Illuminate\Http\Response
     */
    public function comment(Request $request)
    {
        $user = $request->user();

        $request->validate(
            [
                'diary_id' => 'required',
                'content'  => 'required',
            ],
            [
                'diary_id.required' => '日记ID不能为空',
                'content.required'  => '评论不能为空！',
            ]
        );

        // 文本审核
        $content = $request->content;
        $qcloud_service = new QcloudService();
        if (!$qcloud_service->detectText($content)) {
            return $this->errorBadRequest('内容含有敏感词，请调整后重新提交');
        }

        $comment = Comment::find($request->comment_id);

        switch (true) {
            case $comment && $comment->comment_id == null:
                $first_comment_id = $comment->id;
                break;
            case $comment && $comment->comment_id != null:
                $first_comment_id = $comment->first_comment_id;
                break;
            default:
                $first_comment_id = null;
                break;
        }

        switch (true) {
            case $comment && $comment->comment_id != null && $comment->second_comment_id == null:
                $second_comment_id = $comment->id;
                break;
            case $comment && $comment->comment_id != null && $comment->second_comment_id != null:
                $second_comment_id = $comment->second_comment_id;
                break;
            default:
                $second_comment_id = null;
                break;
        }

        $comment = Comment::create([
            'user_id'           => $user->id,
            'diary_id'          => $request->diary_id,
            'comment_id'        => $request->comment_id ?: null,
            'first_comment_id'  => $first_comment_id,
            'second_comment_id' => $second_comment_id,
            'content'           => $request->content,
            'status'            => 1,
        ]);

        $diary = Diary::find($request->diary_id);
        if ($diary) {
            $diary->increment('comment_number');
        }

        return $this->ok('评论成功！');
    }

    /**
     * 删除评论
     *
     * @param  $request
     * @return \Illuminate\Http\Response
     */
    public function deleteComment(Request $request)
    {
        $user = $request->user();
        $request->validate(
            [
                'comment_id' => 'required',
            ],
            [
                'comment_id.required' => '评论ID不能为空',
            ]
        );

        $comment = Comment::find($request->comment_id);
        if (!$comment) {
            return $this->errorBadRequest('评论不存在！');
        }

        if ($comment->user_id != $user->id) {
            $this->errorBadRequest('您没有权限删除该评论！');
        }

        $comment->delete();
        $diary = Diary::find($request->diary_id);
        if ($diary && $diary->comment_number > 0) {
            $diary->decrement('comment_number');
        }

        return $this->message('删除成功！');
    }

    /**
     * 评论列表
     */
    public function comments($id, Request $request)
    {
        $limit = $request->input('limit', 20);
        $comments = Comment::with(['user', 'diary'])
            ->mainComment()
            ->withCount('secondComments')
            ->where('diary_id', $id)
            ->where('status', 1)
            ->latest()
            ->paginate($limit);

        $comments->each(function ($item) {
            $item->load('subComments');
        });
        return $this->success(CommentResource::collection($comments));
    }

    /**
     * 评论的回复列表
     */
    public function subComments(Request $request)
    {
        $limit = $request->input('limit', 20);
        // 是否查询三级完整对话
        $complete = $request->input('complete', false);
        $comment = Comment::findOrFail($request->comment_id);

        if ($complete) {
            $query = $comment->thirdComments();
        } else {
            $query = $comment->secondComments();
        }
        $comments = $query->withCount('like')
            ->with(['user', 'diary', 'comment'])
            ->where('status', 1)
            ->latest()
            ->paginate($limit);
        return $this->success(CommentResource::collection($comments));
    }
}
