@extends('layouts.web')
@section('content')
<div class="wrapper">
  <div class="py-4 bg-light">
    <div class="container">
      <div class="main-title">
        <h1 class="text-3xl">联系我们</h1>
        <div class="breadcrumbs">
          <a href="{{ url('/') }}">首页</a> » <span>联系我们</span>
        </div>
      </div>
    </div>
  </div>
  <div class="container content">
    <div class="row justify-content-around">
      <div class="col-12 col-sm-6">

        @if (session('success'))
        <div class="alert alert-success">
          {{ session('success') }}
        </div>
        @endif
        @if ($errors->any())
        <div class="alert alert-danger">
          <ul>
            @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
          </ul>
        </div>
        @endif
        <form class="form-horizontal global-form" method="POST" action="{{ route('contact.store') }}">
          {{ csrf_field() }}
          <div class="mb-4">
            <label class="form-label">如何称呼您？</label>
            <input class="form-control" name="name" required value="{{ old('name') }}">
          </div>
          <div class="mb-4">
            <label class="form-label">电话</label>
            <input class="form-control" name="telephone" required value="{{ old('telephone') }}">
          </div>
          <div class="mb-4">
            <label class="form-label">邮箱</label>
            <input class="form-control" name="email" value="{{ old('email') }}">
          </div>
          <div class="mb-4">
            <label class="form-label">留言</label>
            <textarea class="form-control" name="message">{{ old('message') }}</textarea>
          </div>
          <button type="submit" class="btn btn-primary">提交</button>
        </form>
      </div>
    </div>
  </div>
</div>
@endsection
