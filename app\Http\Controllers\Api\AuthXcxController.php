<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use Overtrue\LaravelWeChat\EasyWeChat;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Api\ApiController;

class AuthXcxController extends ApiController
{
    /**
     * 小程序登录 - openid作为唯一标识
     */
    public function loginByUniqueOpenid(Request $request)
    {

        if (!defined('CURL_SSLVERSION_TLSv1_2')) {
            define('CURL_SSLVERSION_TLSv1_2', 6);
        }

        $sales_id = $request->sales_id ?? null;

        // 测试环境跳过授权，直接返回用户信息
        $fakeUserId = config('app.FAKE_USER_ID');
        if (app()->environment('local') && !empty($fakeUserId)) {
            $user = User::find($fakeUserId);
            if (!$user) {
                $this->errorBadRequest('用户不存在');
            }
            $token = $user->createToken('token-name');
            return $this->success([
                'user'  => new UserResource($user),
                'token' => $token->plainTextToken
            ]);
        }

        $request->validate(
            [
                'code'    => 'required',
            ],
            [
                'code.required'    => '获取凭证失败',
            ]
        );

        // 获取openid
        $app = EasyWeChat::miniApp();
        try {
            $result = $app->getUtils()->codeToSession($request->code);
            if (!isset($result['openid'])) {
                Log::error('微信响应缺少 openid: ' . json_encode($result));
                $this->errorBadRequest('微信授权失败，请稍后重试');
            }
            $openid = $result['openid'];
        } catch (\Exception $e) {
            report($e);
            // 检查 AppID 或 Secret 是否正确
            $this->errorBadRequest('服务端发生错误，请稍后重试');
        }

        $user = User::where('xcx_openid', $openid)->first();

        $sales_nursing_home_id = 0;
        if ($sales_id) {
            $sales_user = User::where('id', $sales_id)->first();
            $sales_nursing_home_id = $sales_user->nursing_home_id;
        }

        if (!$user) {
            // 创建用户
            $fields = [
                'nickname'        => "用户_" . Str::random(6),
                'xcx_openid'      => $openid,
                'sales_id'        => $sales_id,
                'nursing_home_id' => $sales_nursing_home_id,
                'status'          => 1
            ];
            $user = User::create($fields);
            $is_new = 1;
        } else {
            $is_new = 0;
        }

        if ($user->nursing_home_id == 0) {
            $user->update([
                'nursing_home_id' => $sales_nursing_home_id
            ]);
        }

        return $this->success([
            'user'   => new UserResource($user),
            'token'  => $user->createToken('token-name')->plainTextToken,
            'is_new' => $is_new,
        ]);
    }

    /**
     * 小程序绑定手机 - openid作为唯一标识
     */
    public function bindByUniqueOpenid(Request $request)
    {
        $request->validate(
            [
                'openid'          => 'required',
                'code'            => 'required',
            ],
            [
                'openid.required' => '获取openid失败',
                'code.required'   => '获取手机凭证失败',
            ]
        );
        // openid
        $openid = $request->openid;
        // 手机凭证
        $code = $request->code;

        // EasyWeChat
        $app = EasyWeChat::miniApp();
        //获取手机号
        try {
            $data = [
                'code' => $code,
            ];
            $mobile_result = $app->getClient()->postJson('wxa/business/getuserphonenumber', $data);

            // 记录返回日志
            // Log::info('手机号授权返回: ', ['mobile_result' => $mobile_result]);
            // {
            //     "errcode":0,
            //     "errmsg":"ok",
            //     "phone_info":{
            //         "phoneNumber":"***********",
            //         "purePhoneNumber":"***********",
            //         "countryCode":"86",
            //         "watermark":{
            //             "timestamp":1722221700,
            //             "appid":"wx176ec282badcd062"
            //         }
            //     }
            // }

            if (!isset($mobile_result['phone_info'])) {
                Artisan::call('cache:clear');
                Log::error('手机号授权缺少 phone_info: ' . json_encode($mobile_result));
                $this->errorBadRequest('授权失败，请稍后重试');
            }
            $mobile = $mobile_result['phone_info']['purePhoneNumber'];
        } catch (\Exception $e) {
            // report($e);
            $this->errorBadRequest('发生错误，请稍后重试');
        }

        $user = User::Where('xcx_openid', $openid)->first();
        if ($user) {
            $user->update([
                'mobile' => $mobile
            ]);
        } else {
            $this->errorBadRequest('绑定失败，未找到用户');
        }

        return $this->success([
            'user'  => new UserResource($user),
            'token' => $user->createToken('token-name')->plainTextToken
        ]);
    }

    /**
     * 小程序登录 - mobile作为唯一标识
     *
     * @return void
     */
    public function loginByUniqueMobile(Request $request)
    {
        if (!defined('CURL_SSLVERSION_TLSv1_2')) {
            define('CURL_SSLVERSION_TLSv1_2', 6);
        }

        // 测试环境跳过授权，直接返回用户信息
        $fakeUserId = config('app.FAKE_USER_ID');
        if (app()->environment('local') && !empty($fakeUserId)) {
            $user = User::find($fakeUserId);
            if (!$user) {
                $this->errorBadRequest('用户不存在');
            }
            $token = $user->createToken('token-name');
            return $this->success([
                'user'  => new UserResource($user),
                'token' => $token->plainTextToken
            ]);
        }

        $request->validate(
            [
                'code'    => 'required',
            ],
            [
                'code.required'    => '获取凭证失败',
            ]
        );

        // 获取openid
        $app = EasyWeChat::miniApp();
        try {
            $result = $app->getUtils()->codeToSession($request->code);
            $openid = $result['openid'];
        } catch (\Exception $e) {
            report($e);
            Log::error(json_encode($e));
            $this->errorBadRequest('发生错误，请稍后重试');
        }

        $user = User::where('xcx_openid', $openid)->first();
        if (!$user) {
            return $this->success([
                'user' => [
                    'status'     => 0,
                    'xcx_openid' => $openid
                ],
                'token' => '',
            ]);
        }
        return $this->success([
            'user'  => new UserResource($user),
            'token' => $user->createToken('token-name')->plainTextToken
        ]);
    }

    /**
     * 小程序手机登录 - mobile作为唯一标识
     *
     * @return void
     */
    public function bindByUniqueMobile(Request $request)
    {
        if (!defined('CURL_SSLVERSION_TLSv1_2')) {
            define('CURL_SSLVERSION_TLSv1_2', 6);
        }
        $request->validate(
            [
                'openid'          => 'required',
                'code'            => 'required',
            ],
            [
                'openid.required' => '获取openid失败',
                'code.required'   => '获取手机凭证失败',
            ]
        );
        // openid
        $openid = $request->openid;
        // 手机凭证
        $code = $request->code;

        // EasyWeChat
        $app = EasyWeChat::miniApp();
        //获取手机号
        try {
            $data = [
                'code' => $code,
            ];
            $mobile_result = $app->getClient()->postJson('wxa/business/getuserphonenumber', $data);

            if (!isset($mobile_result['phone_info'])) {
                Log::error('手机号授权缺少 phone_info: ' . json_encode($mobile_result));
                $this->errorBadRequest('授权失败，请稍后重试');
            }

            $mobile = $mobile_result['phone_info']['purePhoneNumber'];
        } catch (\Exception $e) {
            report($e);
            $this->errorBadRequest('发生错误，请稍后重试');
        }

        $user = User::Where('mobile', $mobile)->first();
        if ($user) {
            // 有用户更新此用户xcx_openid
            $user->update([
                'xcx_openid' => $openid
            ]);
        } else {
            // 没有用户就通过xcx_openid进行查找
            $user = User::Where('xcx_openid', $openid)->first();
            if ($user) {
                // 有用户就更新
                $user->update([
                    'mobile' => $mobile
                ]);
            } else {
                // 没有用户就创建
                $fields = [
                    'mobile'      => $mobile,
                    'nickname'    => "用户_" . Str::random(6),
                    'xcx_openid'  => $openid,
                    'status'      => 1
                ];
                $user = User::create($fields);
            }
        }

        return $this->success([
            'user'  => new UserResource($user),
            'token' => $user->createToken('token-name')->plainTextToken
        ]);
    }
}
