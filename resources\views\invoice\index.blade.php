@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/invoice/index',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">发票列表</h5>
    <div class="row">
      <div class="col-md-12">
        <div class="card mb-4">
          <div class="card-header">
            <div class="card-tools">
              <a href="{{ route('invoice.create') }}" class="btn btn-primary btn-sm">申请发票</a>
            </div>
          </div>
          <div class="card-body">
            @if(session('message'))
            <div class="alert alert-info">
              {{ session('message') }}
            </div>
            @endif

            <div class="table-responsive">
              <table class="table table-striped table-bordered">
                <thead>
                  <tr>
                    <th>关联订单号</th>
                    <th>发票抬头</th>
                    <th>金额</th>
                    <th>申请时间</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  @forelse($invoices as $invoice)
                  <tr>
                    <td>{{ $invoice->payment->no }}</td>
                    <td>{{ $invoice->invoice_title }}</td>
                    <td>{{ $invoice->amount }}</td>
                    <td>{{ $invoice->created_at }}</td>
                    <td>
                      @if($invoice->status == \App\Admin\Repositories\Invoice::STATUS_PENDING)
                      <span class="badge bg-warning">待处理</span>
                      @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_APPROVED)
                      <span class="badge bg-info">已批准</span>
                      @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_REJECTED)
                      <span class="badge bg-danger">已拒绝</span>
                      @elseif($invoice->status == \App\Admin\Repositories\Invoice::STATUS_ISSUED)
                      <span class="badge bg-success">已开具</span>
                      @endif
                    </td>
                    <td>
                      <a href="{{ route('invoice.detail', $invoice->id) }}" class="btn btn-sm btn-primary">查看</a>
                    </td>
                  </tr>
                  @empty
                  <tr>
                    <td colspan="7" class="text-center">暂无发票记录</td>
                  </tr>
                  @endforelse
                </tbody>
              </table>
            </div>

            <!-- 分页 -->
            <div class="d-flex justify-content-center">
              {{ $invoices->links() }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
