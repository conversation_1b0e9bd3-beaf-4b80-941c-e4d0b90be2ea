<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserNursingHome;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\UserResource;
use App\Http\Resources\UserPublicResource;
use App\Http\Controllers\Api\ApiController;

class UserController extends ApiController
{
    /**
     * 1101-用户列表
     * 开放的用户信息接口，使用精简的UserPublicResource资源
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 10;
        $role = $request->input('role');
        $result = User::where('status', 1)
            ->when($role, function ($query) use ($role) {
                return $query->where('role', $role);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
        return $this->success(UserPublicResource::collection($result));
    }

    /**
     * 1102-用户详情
     */
    public function show($id)
    {
        $result = User::withCount(['collections', 'follows', 'nursingHomes'])->find($id);
        if ($result) {
            return $this->success(new UserResource($result));
        }
        $this->errorBadRequest('暂无数据');
    }

    /**
     * 1103-编辑个人信息
     * 昵称，真实姓名，邮箱，性别，生日，头像
     * 根据传入的值，更新用户信息。如果未传入则不更新
     */
    public function update(Request $request, $id)
    {
        $user = $request->user();
        if ($user->id != $id) {
            $this->errorBadRequest('无权修改');
        }

        $request->validate(
            [
                'real_name'  => 'nullable|string|max:50',
                'email'      => 'nullable|email',
                'gender'     => 'nullable|in:0,1,2',
                'birthdate'  => 'nullable|date',
            ],
            [
                'real_name.string' => '真实姓名必须是字符串',
                'real_name.max'    => '真实姓名不能超过50个字符',
                'email.email'      => '请输入正确的邮箱',
                'gender.in'        => '请选择正确的格式',
                'birthdate.date'   => '请输入正确的日期格式',
            ]
        );

        $user->nickname   = $request->nickname ?? $user->nickname;
        $user->real_name  = $request->real_name ?? $user->real_name;
        $user->email      = $request->email ?? $user->email;
        $user->gender     = $request->gender ?? $user->gender;
        $user->birthdate  = $request->birthdate ?? $user->birthdate;
        $user->avatar_url = $request->avatar_url ?? $user->avatar_url;
        $user->save();

        return $this->ok('修改成功');
    }

    /**
     * 1120-当前用户详情
     */
    public function current(Request $request)
    {
        $user = $request->user();
        $result = User::withCount(['collections', 'follows', 'nursingHomes'])->find($user->id);
        return $this->success(new UserResource($result));
    }

    /**
     * 1121-青少年密码设置
     */
    public function setPassword(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate(
            [
                'password' => 'required|string|min:4|max:4',
            ],
            [
                'password.required' => '请输入密码',
                'password.min'      => '密码长度不能小于4位',
                'password.max'      => '密码长度不能大于4位',
            ]
        );

        try {

            // 获取当前用户
            $user = $request->user();

            // 设置新密码并保存
            $user->password = bcrypt($validated['password']);
            $user->save();

            // \Log::info('用户 [' . $user->id . '] 成功设置了新密码');

            return $this->ok('设置密码成功');
        } catch (\Exception $e) {
            // 记录异常日志
            Log::error('设置密码时发生错误: ' . $e->getMessage());
            // 返回错误响应
            $this->errorBadRequest('设置密码时发生错误，请稍后再试');
        }
    }

    /**
     * 1122-青少年密码校验
     */
    public function checkPassword(Request $request)
    {
        $validated = $request->validate(
            [
                'password' => 'required|string|min:4|max:4',
            ],
            [
                'password.required' => '请输入密码',
                'password.min'      => '密码长度不能小于4位',
                'password.max'      => '密码长度不能大于4位',
            ]
        );

        $user = $request->user();

        // 验证密码是否正确
        if (password_verify($validated['password'], $user->password)) {
            return $this->ok('密码正确');
        } else {
            $this->errorBadRequest('密码错误');
        }
    }

    /**
     * 用户绑定的养老院列表
     */
    public function userNursingHomeList(Request $request)
    {
        $user = $request->user();
        $list = UserNursingHome::with('nursingHome')
            ->where('user_id', $user->id)
            ->get();
        $result = [];
        foreach ($list as $item) {
            $result[] = [
                'id'    => $item->nursing_home_id,
                'label' => $item->nursingHome->name,
            ];
        }
        return $this->success($result);
    }

    // 切换养老院
    public function changeNursingHome(Request $request)
    {
        $request->validate(
            [
                'nursing_home_id' => 'required',
            ],
            [
                'nursing_home_id.required' => '请选择养老院',
            ]
        );
        $user = $request->user();
        $nursingHomeId = $request->nursing_home_id;
        $user->update(['nursing_home_id' => $nursingHomeId]);

        return $this->ok('设定养老院成功');
    }
}
