<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UserShareRecord;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserShareRecordController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserShareRecord(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('from_user_id');
            $grid->column('diary_id');
            $grid->column('nursing_home_id');
            $grid->column('to_user_id');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserShareRecord(), function (Show $show) {
            $show->field('id');
            $show->field('from_user_id');
            $show->field('diary_id');
            $show->field('nursing_home_id');
            $show->field('to_user_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserShareRecord(), function (Form $form) {
            $form->display('id');
            $form->text('from_user_id');
            $form->text('diary_id');
            $form->text('nursing_home_id');
            $form->text('to_user_id');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
