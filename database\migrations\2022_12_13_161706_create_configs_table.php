<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateConfigsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('configs', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('标题');
            $table->string('key')->comment('键');
            $table->tinyInteger('type')->comment('类型');
            $table->text('value1')->nullable()->comment('值');
            $table->text('value2')->nullable()->comment('值');
            $table->text('value3')->nullable()->comment('值');
            $table->text('value4')->nullable()->comment('值');
            $table->text('value5')->nullable()->comment('值');
            $table->text('value6')->nullable()->comment('值');
            $table->text('value7')->nullable()->comment('值');
            $table->text('value8')->nullable()->comment('值');
            $table->text('value9')->nullable()->comment('值');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('configs');
    }
}
