<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Payment;

class PaymentController extends Controller
{
    /**
     * 付款记录列表
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取付款记录列表
        $payments = Payment::where('nursing_home_id', $nursing_home_id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('payment.index', compact('payments'));
    }
}
