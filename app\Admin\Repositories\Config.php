<?php

namespace App\Admin\Repositories;

use App\Models\Config as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Config extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public static function type()
    {
        return [
            1 => '普通文本',
            2 => '富文本',
            3 => '单图',
            4 => '多图',
            5 => '单文件',
            6 => '多文件',
            7 => '日期',
            8 => '日期时间',
            9 => '开关',
        ];
    }
}
