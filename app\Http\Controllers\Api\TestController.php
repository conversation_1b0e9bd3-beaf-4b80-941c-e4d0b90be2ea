<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Services\QywxNotificationService;
use App\Http\Controllers\Api\ApiController;

class TestController extends ApiController
{
    /**
     * 测试
     *
     * http://laolin.test/api/test
     */
    public function index()
    {
        $qywxService = new QywxNotificationService();

        // $webhookUrl = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fb8d8705-fa1d-439d-9be8-d0e15e2a4ae3';
        // $qywxService->setWebhookUrl($webhookUrl);

        $content = "客户 杨 已成功签约";
        $content .= "\n";
        $content .= "签约机构：养老院A";
        $content .= "\n";
        $content .= "联系电话：13800000000";
        $content .= "\n";
        $content .= "销售人员：李";

        $qywxService->sendTextNotification($content);
    }
}
