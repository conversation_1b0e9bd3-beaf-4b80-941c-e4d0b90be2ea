<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DiaryShortResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $like_number_text = $this->likes_number > 9999 ? round($this->likes_number / 10000, 1) . '万' : $this->likes_number;

        return [
            'id'                 => $this->id,
            'nursing_home'       => $this->whenLoaded('nursingHome') ? new NursingHomeShortResource($this->nursingHome): '',
            'title'              => $this->title,
            'image'              => $this->cover_vertical_url ?? $this->cover_url,
            'likes_number'       => $this->likes_number ?? 0,
            'like_number_text'   => $like_number_text,
        ];
    }
}
