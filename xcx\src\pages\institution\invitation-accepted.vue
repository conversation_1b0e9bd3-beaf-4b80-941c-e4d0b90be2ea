<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view
      class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center"
      v-else-if="!isExpired"
    >
      <view class="u-flex-col u-col-center mb-40">
        <view class="u-flex u-col-center u-row-center u-m-b-40">
          <image
            src="/static/images/cover.png"
            class="cover-image"
            mode="aspectFill"
          ></image>
        </view>

        <view class="form-title u-m-b-30">机构邀请</view>
        <view class="u-font-16 u-m-b-10">您被邀请加入机构：</view>
        <view class="u-font-16 u-m-b-30">{{ institutionName }}</view>
      </view>

      <!-- 姓名和手机号输入 -->
      <view class="px-80 mb-40">
        <u-form :model="info" ref="infoForm" :rules="rules">
          <u-form-item
            label="姓名"
            prop="name"
            label-width="140"
            :required="true"
            borderBottom
          >
            <u-input
              inputAlign="right"
              v-model="info.name"
              border="none"
              :clearable="false"
              placeholder="请输入姓名"
            />
          </u-form-item>
          <u-form-item
            label="手机号码"
            prop="mobile"
            label-width="140"
            :required="true"
          >
            <view class="input-wrapper">
              <u-input
                inputAlign="right"
                v-model="info.mobile"
                border="none"
                :clearable="false"
                placeholder="请点击授权手机号码"
                :disabled="btnLoading"
              />
              <button
                class="wechat-phone-btn"
                open-type="getPhoneNumber"
                @getphonenumber="handleGetPhone"
              ></button>
            </view>
          </u-form-item>
        </u-form>
      </view>

      <view class="px-80 my-20">
        <u-row justify="center" gutter="20">
          <u-col span="6">
            <u-button
              color="#f5f5f5"
              customStyle="color:#000"
              shape="circle"
              text="拒绝"
              @click="toCancel"
            ></u-button>
          </u-col>
          <u-col span="6">
            <u-button
              color="#F2AE1E"
              shape="circle"
              text="同意邀请"
              @click="acceptInvite"
              :loading="btnLoading"
            ></u-button>
          </u-col>
        </u-row>
      </view>
    </view>
    <view
      class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center"
      v-else
    >
      <view class="u-flex-col u-col-center u-m-b-80">
        <view class="u-flex u-col-center u-row-center checkmark u-m-b-40">
          <u-image
            width="80rpx"
            height="80rpx"
            src="/static/images/icons/error.png"
          ></u-image>
        </view>

        <view class="u-font-xl fw-bold u-m-b-30">二维码已过期</view>
        <view class="u-m-b-10">该邀请二维码已过期，请联系机构重新生成。</view>
      </view>
      <view class="u-p-l-80 u-p-r-80 u-m-b-20">
        <u-button
          color="#F2F2F2"
          customStyle="color:#332C2B"
          class="btn-grey"
          shape="circle"
          text="返回"
          @click="toCancel"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/common/api";

export default {
  data() {
    return {
      loading: true,
      btnLoading: false,
      institutionName: "",
      nursingHomeId: null,
      timestamp: null,
      isExpired: false,
      info: {
        name: "",
        mobile: "",
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["blur", "change"],
          },
        ],
        mobile: [
          {
            required: true,
            message: "请点击授权手机号码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  onShow() {},
  onLoad(options) {
    // 从二维码参数中获取养老院ID和时间戳
    if (options.scene) {
      let scanUrl = decodeURIComponent(options.scene);

      // 解析参数 (手动解析URL参数，因为URLSearchParams在某些环境中不可用)
      const parseParams = (url) => {
        const params = {};
        if (url) {
          const pairs = url.split("&");
          for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split("=");
            if (pair.length === 2) {
              params[decodeURIComponent(pair[0])] = decodeURIComponent(
                pair[1] || ""
              );
            }
          }
        }
        return params;
      };

      const params = parseParams(scanUrl);
      this.nursingHomeId = params["n_id"];
      this.timestamp = params["ts"];

      // 检查二维码是否过期（24小时有效期）
      if (
        !this.timestamp ||
        Math.floor(Date.now() / 1000) - this.timestamp > 24 * 60 * 60
      ) {
        this.isExpired = true;
        this.loading = false;
        return;
      }

      // 获取机构信息
      this.loadInstitutionInfo();
    } else {
      this.loading = false;
    }
  },
  onReady() {
    this.$nextTick(() => {
      if (this.$refs.infoForm && this.$refs.infoForm.setRules) {
        this.$refs.infoForm.setRules(this.rules);
      }
    });
  },
  methods: {
    async loadInstitutionInfo() {
      console.log(this.nursingHomeId);
      if (!this.nursingHomeId) {
        this.loading = false;
        return;
      }

      api.getNursingHomeDetail(this.nursingHomeId).then((res) => {
        if ((res.data.code = 200)) {
          this.institutionName = res.data.data.nursing_home.name;
          this.loading = false;
        }
      });
    },

    // 检查是否已绑定手机号
    checkMobileBound() {
      const user = uni.getStorageSync("user");
      if (user && user.mobile) {
        this.info.mobile = user.mobile;
        return true;
      }
      return false;
    },

    async handleGetPhone(e) {
      console.log("获取手机号事件:", e.detail);
      this.btnLoading = true;

      try {
        if (e.detail.errMsg !== "getPhoneNumber:ok") {
          throw new Error("用户拒绝授权");
        }

        const { code } = e.detail;
        console.log("手机号code:", code);

        // 获取本地存储的用户信息
        const user = uni.getStorageSync("user") || {};

        if (!user || !user.xcx_openid) {
          throw new Error("登录状态失效，请重新登录");
        }

        // 调用绑定手机号API（使用code方式）
        const res = await api.bindMobileByOpenid({
          data: {
            code: code,
            openid: user.xcx_openid,
          },
          method: "POST",
        });

        this.btnLoading = false;

        if (res.data.status === "success") {
          if (res.data.data && res.data.data.user) {
            const userData = res.data.data;

            // 更新手机号显示
            if (userData.user.mobile) {
              this.info.mobile = userData.user.mobile;
            }

            // 更新用户信息
            uni.setStorageSync("user", userData.user);

            // 更新token
            if (userData.token) {
              uni.setStorageSync("token", userData.token);
            }

            // 触发自定义事件
            this.$emit("mobile-bound", userData.user.mobile);
          } else {
            throw new Error("手机号获取失败");
          }
        } else {
          throw new Error(res.data.message || "绑定失败");
        }
      } catch (error) {
        this.btnLoading = false;
        console.error("获取手机号失败:", error);

        // 显示错误提示
        if (error.message.includes("用户拒绝授权")) {
          uni.showToast({
            title: "需要授权才能获取手机号",
            icon: "none",
            duration: 2000,
          });
        } else {
          this.$refs.uToast.show({
            message: error.message || "手机号绑定失败",
            duration: 3000,
          });
        }
      }
    },

    async acceptInvite() {
      this.btnLoading = true;
      // 验证表单
      this.$refs.infoForm
        .validate()
        .then(async (res) => {
          console.log("提交数据:", this.info);

          if (!this.nursingHomeId || !this.timestamp) {
            this.$refs.uToast.show({
              type: "error",
              message: "邀请信息无效",
            });
            this.btnLoading = false;
            return;
          }

          // 检查二维码是否过期
          if (Math.floor(Date.now() / 1000) - this.timestamp > 24 * 60 * 60) {
            this.isExpired = true;
            this.$refs.uToast.show({
              type: "error",
              message: "二维码已过期，请重新生成",
            });
            this.btnLoading = false;
            return;
          }

          try {
            // 调用API处理邀请，传递姓名和手机号
            const response = await api.acceptInvite({
              nursing_home_id: this.nursingHomeId,
              timestamp: this.timestamp,
              name: this.info.name,
              mobile: this.info.mobile,
            });

            if (response.data.code === 200) {
              this.$refs.uToast.show({
                type: "success",
                message: "成功加入机构",
              });

              // 延迟跳转到首页
              setTimeout(() => {
                uni.reLaunch({
                  url: "/pages/index/index",
                });
              }, 1500);
            } else {
              this.$refs.uToast.show({
                type: "error",
                message: response.data.message || "处理邀请失败",
              });
            }
          } catch (error) {
            console.error("处理邀请失败:", error);
            this.$refs.uToast.show({
              type: "error",
              message: "处理邀请失败",
            });
          } finally {
            this.btnLoading = false;
          }
        })
        .catch((errors) => {
          uni.$u.toast("请完善表单信息");
          this.btnLoading = false;
        });
    },

    toCancel() {
      console.log("取消邀请");
      uni.reLaunch({
        url: "/pages/index/index",
      });
    },
  },
};
</script>

<style lang="scss">
.content {
  min-height: 100vh;
  background-color: #fff;
  .checkmark img {
    width: 168upx;
    height: 168upx;
  }
}
.btn-grey {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
  color: #332c2b;
}
.form-title {
  font-size: 60upx;
}
.cover-image {
  width: 400upx;
  height: 400upx;
}
</style>
