<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />
    <view class="content container">
      <view>
        <view class="u-flex u-row-center u-col-center">
          <view class="avatar-wrap u-relative bg-000">
            <button
              open-type="chooseAvatar"
              class="u-reset-button"
              @chooseavatar="chooseWxAvatar"
            >
              <view class="avatar u-relative">
                <u-avatar
                  :src="form.avatar_url"
                  size="82"
                  shape="square"
                  default-url="/static/icon-video/default-avatar.png"
                ></u-avatar>
                <view class="u-absolute edit-icon">
                  <u-icon name="edit-pen" color="#fff" size="20"></u-icon>
                </view>
              </view>
            </button>
          </view>
        </view>
        <u--form
          labelPosition="left"
          :model="form"
          ref="Form"
          labelWidth="80"
          errorType="toast"
        >
          <!-- 昵称 -->
          <view class="card u-m-t-40 u-p-b-20">
            <u-form-item label="昵称" prop="nickname" required>
              <input
                type="nickname"
                :maxlength="16"
                @change="changeName"
                v-model="form.nickname"
                color="#000"
                placeholder="限2-20个字"
              />
            </u-form-item>
            <u-icon
              label="请设置2-20个字"
              size="18"
              labelSize="15"
              labelColor="#999"
              space="6"
              name="/static/images/icons/i-prompt.png"
            ></u-icon>
          </view>
          <!-- 电话 -->
          <view class="card" v-if="form.role == 3">
            <u-form-item label="手机" prop="mobile">
              <u--input
                v-model="form.mobile"
                disabled
                disabledColor="#ffffff"
                border="none"
                placeholder="请输入您的手机号码"
              ></u--input>
            </u-form-item>
          </view>
          <!-- 邮箱 -->
          <!-- <view class="card">
            <u-form-item label="邮箱" prop="email">
              <u--input
                v-model="form.email"
                border="none"
                placeholder="请输入您的邮箱"
              ></u--input>
            </u-form-item>
          </view> -->
          <!-- 性别 -->
          <view class="card">
            <u-form-item label="性别" prop="gender">
              <u-radio-group v-model="form.gender" activeColor="#000">
                <u-radio
                  :customStyle="{ marginLeft: '10px' }"
                  v-for="(item, index) in genderList"
                  :key="index"
                  :label="item.name"
                  :name="item.id"
                >
                </u-radio>
              </u-radio-group>
            </u-form-item>
          </view>
          <!-- 生日 -->
          <view class="card">
            <u-form-item
              label="生日"
              prop="birthdate"
              @click="showBirthday = true"
            >
              <u--input
                v-model="form.birthdate"
                disabled
                disabledColor="#fff"
                placeholder="请选择您的生日"
                border="none"
              ></u--input>
              <u-icon slot="right" name="arrow-right" color="#000"></u-icon>
            </u-form-item>
          </view>
        </u--form>
      </view>
      <view>
        <u-button
          type="primary"
          @click="submit"
          size="large"
          color="#FFDB7F"
          :customStyle="btnStyle"
          >确认</u-button
        >
      </view>
    </view>
    <u-datetime-picker
      :show="showBirthday"
      :value="birthdate"
      :maxDate="birthdate"
      :minDate="minDate"
      mode="date"
      closeOnClickOverlay
      @confirm="birthdayConfirm"
      @cancel="birthdayClose"
      @close="birthdayClose"
    ></u-datetime-picker>
  </view>
</template>

<script>
import api from "@/common/api";
import { mapActions } from "vuex";
export default {
  data() {
    return {
      loading: true,
      showBirthday: false,
      changed: false,
      birthdate: Number(new Date()),
      minDate: Number(new Date(1900, 0, 1)),
      btnStyle: {
        marginBottom: "40upx",
        borderRadius: "24upx",
        height: "100upx",
        color: "#000",
        fontSize: "32upx",
        fontWeight: "bold",
      },
      genderList: [
        {
          id: 0,
          name: "保密",
        },
        {
          id: 1,
          name: "男",
        },
        {
          id: 2,
          name: "女",
        },
      ],
      form: {
        avatar_url: "",
        nickname: "",
        name: "",
        mobile: "",
        email: "",
        gender: "",
        birthdate: "",
      },
      rules: {
        nickname: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur, change",
          },
          {
            max: 20,
            message: "最多20个字",
            trigger: "blur, change",
          },
        ],
      },
    };
  },

  onLoad() {
    this.loadData();
  },
  onReady() {
    this.$refs.Form.setRules(this.rules);
  },
  methods: {
    ...mapActions("permissionToast", ["requstPermission"]),
    birthdayClose() {
      this.showBirthday = false;
    },
    birthdayConfirm(e) {
      this.showBirthday = false;
      this.form.birthdate = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
    },
    changeName(e) {
      if (e.detail.value.trim()) {
        this.form.nickname = e.detail.value;
      }
    },
    loadData() {
      api.getCurrentDetail().then((res) => {
        this.form = res.data.data;
        this.avatar_url = this.form.avatar_url;
        this.$nextTick(() => {
          this.loading = false;
        });
      });
    },

    // 获取微信头像及其他图片
    chooseWxAvatar(e) {
      this.avatar_url = e.detail.avatarUrl;
      if (this.avatar_url != this.form.avatar_url) {
        this.changed = true;
      }
      this.form.avatar_url = this.avatar_url;
    },
    // 选择图片
    async getUploadFile() {
      /* #ifdef APP-PLUS */
      if (!(await this.requstPermission("CAMERA"))) return;
      /* #endif */
      uni.chooseImage({
        sizeType: ["compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], //从拍照选择器
        count: 1,
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          this.avatar_url = tempFilePaths[0];
          if (this.avatar_url != this.form.avatar_url) {
            this.changed = true;
          }
          this.form.avatar_url = this.avatar_url;
        },
      });
    },
    //修改信息
    submit() {
      this.$refs.Form.validate()
        .then((valid) => {
          if (valid) {
            this.save();
          } else {
            console.log("验证失败");
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //修改信息
    save() {
      if (this.changed) {
        api
          .uploadImages({
            filePath: this.avatar_url,
            name: "image",
            formData: {
              type: "avatar",
              index: 0,
            },
            method: "UPLOAD",
          })
          .then((res) => {
            if (res.data.code == 200) {
              this.avatar_url = res.data.data.image;
              this.changed = false;
              this.updateInfo();
              uni.showToast({
                message: res.data.message,
                icon: "none",
                mask: true,
              });
            } else {
              this.$refs.uNotify.show({
                message: res.data.message,
                type: "error",
                duration: "1500",
              });
            }
          })
          .catch((error) => {
            console.log("error", error);
          });
      } else {
        this.updateInfo();
      }
    },
    updateInfo() {
      api
        .editUser(
          {
            avatar_url: this.avatar_url,
            nickname: this.form.nickname,
            mobile: this.form.mobile,
            email: this.form.email || "",
            gender: this.form.gender || "",
            birthdate: this.form.birthdate || "",
          },
          this.form.id
        )
        .then((res) => {
          if (res.data.status === "success") {
            uni.showToast({
              title: res.data.message,
              icon: "none",
              mask: true,
            });
            // 1.5秒后返回
            setTimeout(() => {
              uni.navigateBack({ delta: 1 });
            }, 1500);
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
              mask: true,
            });
          }
        });
    },
  },
  onPullDownRefresh() {
    this.loadData();
    uni.stopPullDownRefresh();
  },
};
</script>

<style lang="scss">
.content {
  background-color: #f5f5f5;
  padding-top: 80upx;
  padding-bottom: 60upx;
  min-height: 100vh;
}
.bg-000 {
  background-color: #6f6f6f;
  border-radius: 16upx;
}
.card {
  background-color: #fff;
  border-radius: 24upx;
  padding: 8upx 36upx;
  margin-bottom: 20upx;
}
.avatar-wrap {
  display: flex;
  justify-content: center;
  margin-bottom: 30upx;
  position: relative;
  .uploading {
    position: absolute;
    right: 20upx;
    top: 20upx;
  }
  .avatar {
    line-height: 0;
    position: relative;
  }
}
.edit-icon {
  display: inline-block;
  right: 0;
  bottom: 0;
  background-color: #3f3f3f;
  border-radius: 8upx;
  position: absolute !important;
}
.u-radio-group {
  flex: 0 !important;
  justify-content: flex-end;
}
</style>
