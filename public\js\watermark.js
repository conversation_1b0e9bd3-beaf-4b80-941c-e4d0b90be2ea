/**
 * 水印功能
 * 水印现在直接在HTML中渲染，无需JavaScript监听
 * 此文件保留用于向后兼容和可能的扩展功能
 */
class ScreenshotWatermark {
    constructor(options = {}) {
        this.options = {
            text: options.text || '未知用户',
            fontSize: options.fontSize || '14px',
            color: options.color || 'rgba(0, 0, 0, 0.1)',
            position: options.position || 'bottom-right',
            zIndex: options.zIndex || 9999,
            ...options
        };

        // 水印现在直接在HTML中渲染，无需初始化
        console.log('水印功能已启用，水印直接在页面中渲染');
    }

    // 更新水印文本（保留用于向后兼容）
    updateText(newText) {
        this.options.text = newText;
        console.log('水印文本已更新为:', newText);
    }

    // 销毁方法（保留用于向后兼容）
    destroy() {
        console.log('水印功能已销毁');
    }
}

// 全局初始化函数（保留用于向后兼容）
window.initScreenshotWatermark = function(userInfo) {
    if (window.screenshotWatermark) {
        window.screenshotWatermark.destroy();
    }

    window.screenshotWatermark = new ScreenshotWatermark({
        text: userInfo || '未知用户',
        fontSize: '14px',
        color: 'rgba(0, 0, 0, 0.7)'
    });
};

// 页面加载完成后自动初始化（如果有用户信息）
document.addEventListener('DOMContentLoaded', function() {
    // 这里会在布局文件中通过PHP传入用户信息
    if (typeof window.currentUserMobile !== 'undefined') {
        window.initScreenshotWatermark(window.currentUserMobile);
    }
});
