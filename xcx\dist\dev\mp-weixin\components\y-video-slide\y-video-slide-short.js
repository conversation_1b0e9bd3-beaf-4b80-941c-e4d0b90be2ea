(global["webpackChunklaolin"] = global["webpackChunklaolin"] || []).push([["components/y-video-slide/y-video-slide-short"],{

/***/ "./src/components/y-video-slide/y-video-slide-short.vue":
/*!**************************************************************!*\
  !*** ./src/components/y-video-slide/y-video-slide-short.vue ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true& */ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true&");
/* harmony import */ var _y_video_slide_short_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./y-video-slide-short.vue?vue&type=script&lang=js& */ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js&");
/* harmony import */ var _y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true& */ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js");

var renderjs
;

;


/* normalize component */

var component = (0,_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _y_video_slide_short_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.render,
  _y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "d25e36ea",
  null,
  false,
  _y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.components,
  renderjs
)

component.options.__file = "components/y-video-slide/y-video-slide-short.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true&":
/*!*********************************************************************************************************!*\
  !*** ./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true& ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   components: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.components; },
/* harmony export */   recyclableRender: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.recyclableRender; },
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_42_0_rules_0_use_0_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_template_id_d25e36ea_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true& */ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true&");


/***/ }),

/***/ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js&":
/*!***************************************************************************************!*\
  !*** ./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_40_0_rules_0_use_0_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_40_0_rules_0_use_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./y-video-slide-short.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js&");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_40_0_rules_0_use_0_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_40_0_rules_0_use_1_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true&":
/*!************************************************************************************************************************!*\
  !*** ./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true& ***!
  \************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!../../../node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true& */ "./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true&");
/* harmony import */ var _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);
 /* harmony default export */ __webpack_exports__["default"] = ((_node_modules_mini_css_extract_plugin_dist_loader_js_clonedRuleSet_22_0_rules_0_use_0_node_modules_css_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_1_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_2_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_3_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_clonedRuleSet_22_0_rules_0_use_4_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_clonedRuleSet_22_0_rules_0_use_5_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_y_video_slide_short_vue_vue_type_style_index_0_id_d25e36ea_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default())); 

/***/ }),

/***/ "./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true&":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-42[0].rules[0].use[0]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=template&id=d25e36ea&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   components: function() { return /* binding */ components; },
/* harmony export */   recyclableRender: function() { return /* binding */ recyclableRender; },
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.videoList.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      $event.stopPropagation()
      _vm.muted = !_vm.muted
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js&":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-40[0].rules[0].use[0]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-40[0].rules[0].use[1]!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _common_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/common/api */ "./src/common/api.js");
/* provided dependency */ var uni = __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ "./node_modules/@dcloudio/uni-mp-weixin/dist/index.js")["default"];
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ __webpack_exports__["default"] = ({
  props: {
    // 视频容器高度
    videoHeight: {
      type: String,
      default: "100vh"
    },
    // 列表数据
    data: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    // 当前视频下标
    videoIndex: {
      type: [Number, String],
      default: "0"
    }
  },
  data: function data() {
    return {
      // 视频列表数据
      videoList: this.data,
      // 视频实际高度，用于滚动计算
      videoRealHeight: 0,
      // 双击点赞记录
      fabulousArr: [],
      // 记录点击时间判断单击还是双击，单击暂停，双击点赞
      clickTimer: null,
      // 记录点击时间
      clickTime: 0,
      // 屏幕高度
      windowHeight: uni.getSystemInfoSync().windowHeight || window.innerHeight,
      // 动画效果
      transition: "none",
      // 触摸开始值
      startY: 0,
      // 触摸移动值
      moveY: 0,
      // 滑动开始时间
      startTime: 0,
      // 向上滑动距离
      marginTop: 0,
      // 每次向上滑动的距离
      fixMarginTop: 0,
      // 滑动方向
      moveDirection: "",
      // 当前轮播的index
      currentIndex: 0,
      // 当前页的视频对象
      currentVideo: null,
      // 静音
      muted: false,
      currentPage: 1,
      totalPages: 0,
      loading: false,
      currentStatus: "play",
      // 视频状态：播放/暂停
      // 爱心动画双击
      fabulousTime: 0,
      fabulousTimer: null,
      currentCommentList: [],
      // 当前视频的评论
      commontShow: false,
      // 是否显示评论
      currentcomment_number: 0,
      showInput: false,
      // 显示评论输入框
      commentPlaceholder: "写个评论吧~",
      // 评论提示 💗
      replyType: "video",
      replyObj: {},
      // 回复对象
      replyIndex: 0,
      replyParentIndex: 0,
      replyContent: "",
      // 评论内容

      width: uni.getSystemInfoSync().screenWidth,
      lineWidth: 0,
      duration: 0,
      // 视频的总时长
      currentTime: 0,
      // 当前播放时间
      progress: 0,
      // 进度条的值
      controls: false,
      isSeeking: false,
      isExpanded: false,
      isOverflow: false,
      showRate: false,
      currentRate: 1,
      // 初始倍速为1

      // 浏览记录相关
      browseTimer: null,
      // 浏览记录定时器
      browseStartTime: 0 // 开始播放时间
    };
  },
  watch: {
    data: {
      handler: function handler(n) {
        this.videoList = n;
      },
      deep: true
    },
    currentIndex: {
      immediate: true,
      // 立即触发一次
      handler: function handler(val) {
        var _this$currentVideo;
        this.showRate = false;
        this.currentRate = 1;
        (_this$currentVideo = this.currentVideo) === null || _this$currentVideo === void 0 || _this$currentVideo.playbackRate(1);
        // 确保进度重置（可选）
        this.currentTime = 0;
        this.lineWidth = 0;

        // 切换视频时重新开始浏览记录追踪
        if (this.videoList.length > 0 && val >= 0) {
          this.startBrowseTracking();
        }
      }
    }
  },
  mounted: function mounted() {
    var _this = this;
    console.log(this.videoList);
    // 初始化视频容器计算高度

    var query = uni.createSelectorQuery().in(this);
    query.select(".video-item-box").boundingClientRect(function (data) {
      _this.videoRealHeight = data.height;
    }).exec();

    // 初始化第一个视频播放器
    this.currentVideo = uni.createVideoContext(this.videoList[0].id, this);

    // 定时清除动画元素
    this.clearFabulousArr();
  },
  methods: {
    // 视频出错
    videoError: function videoError(item, index) {
      uni.showToast({
        title: "视频出错了!",
        icon: "none"
      });
    },
    onTimeUpdate: function onTimeUpdate(e, index) {
      // 如果正在拖动进度条，则不更新进度条位置
      if (this.isSeeking) {
        return;
      }
      if (this.loading) this.loading = false;
      if (!this.playing) this.playing = true;
      if (!this.duration || this.duration == "00:00") {
        this.durationSeconds = e.detail.duration;
        this.duration = this.secondToTime(e.detail.duration, 1);
      }
      this.currentTime = this.secondToTime(e.detail.currentTime);
      this.lineWidth = e.detail.currentTime / e.detail.duration * this.width;
    },
    secondToTime: function secondToTime(s, n) {
      var t = parseInt(s);
      var hours = Math.floor(t / (60 * 60));
      var seconds = t % (60 * 60);
      var minutes = Math.floor(seconds / 60);
      var seconds2 = seconds % 60;
      if (hours > 0) {
        return "".concat(hours, ":").concat(minutes.toString().padStart(2, "0"), ":").concat(seconds2.toString().padStart(2, "0"));
      } else {
        return "".concat(minutes.toString().padStart(2, "0"), ":").concat(seconds2.toString().padStart(2, "0"));
      }
    },
    // 触摸开始
    touchStart: function touchStart(e) {
      this.transition = "none";
      this.startY = e.touches[0].pageY;
      this.startTime = new Date().getTime();
    },
    // 触摸滑动
    touchMove: function touchMove(e) {
      // 如果评论面板打开，禁止滑动
      if (this.commontShow) {
        return;
      }
      this.moveY = e.touches[0].pageY;
      if (this.moveY > this.startY) {
        // 返回上一个视频
        var range = this.fixMarginTop + this.moveY - this.startY;
        if (this.currentIndex == 0) {
          this.marginTop = range > 50 ? 50 : range;
        } else {
          this.marginTop = range;
        }
        this.moveDirection = "up";
      } else {
        if (this.videoList.length == 0) {
          return;
        }
        // 查看下一个视频
        var _range = this.fixMarginTop - this.startY + this.moveY;
        if (this.currentIndex == this.videoList.length - 1) {
          // 最后一个视频的位置
          var lastVideo = -this.currentIndex * this.videoRealHeight + -50;
          this.marginTop = _range < lastVideo ? lastVideo : _range;
        } else {
          this.marginTop = _range;
        }
        this.moveDirection = "down";
      }
    },
    // 触摸结束
    touchEnd: function touchEnd(e) {
      this.transition = "all .2s";
      if (this.currentIndex == 0 && this.marginTop >= 45) {
        // 下拉刷新
        this.$emit("refresh");
        this.marginTop = 0;
        return;
      }
      if (this.currentIndex == this.videoList.length - 1 && this.marginTop < this.fixMarginTop - 45) {
        this.marginTop = this.fixMarginTop;
        // 加载更多
        this.$emit("loadMore");
        return;
      }
      var millisecond = new Date().getTime() - this.startTime;
      var condition1 = this.moveY > 0 && Math.abs(this.moveY - this.startY) > 50 && millisecond < 500;
      var condition2 = this.moveY > 0 && Math.abs(this.moveY - this.startY) > this.videoRealHeight / 3;
      if (condition1 || condition2) {
        if (this.moveDirection == "up") {
          // 返回上一个
          if (this.currentIndex == 0) {
            this.marginTop = 0;
          } else {
            this.marginTop = this.fixMarginTop + this.videoRealHeight;
            this.currentIndex = this.currentIndex - 1;
            this.$emit("update:videoIndex", this.currentIndex);
            // this.currentComment = this.videoList[this.currentIndex].commentObj;
            this.currentcomment_number = 0;
            this.currentCommentList = [];
            this.videoPlayChange();
          }
        } else {
          // 查看下一个
          if (this.currentIndex == this.videoList.length - 1) {
            this.marginTop = this.fixMarginTop;
          } else {
            this.marginTop = this.fixMarginTop - this.videoRealHeight;
            this.currentIndex = this.currentIndex + 1;
            this.$emit("update:videoIndex", this.currentIndex);
            // this.currentComment = this.videoList[this.currentIndex].commentObj;
            this.currentcomment_number = 0;
            this.currentCommentList = [];
            this.videoPlayChange();
          }
        }
      } else {
        this.marginTop = this.fixMarginTop;
      }
      this.fixMarginTop = this.marginTop;
      this.startY = 0;
      this.moveY = 0;
    },
    // 滑动切换
    videoPlayChange: function videoPlayChange() {
      this.stopOtherVideo();
      var video = uni.createVideoContext(this.videoList[this.currentIndex].id, this);
      this.currentVideo = video;
      this.currentVideo.play();
      this.currentStatus = "play";
    },
    // 暂停其他视频
    stopOtherVideo: function stopOtherVideo() {
      var _this2 = this;
      // 清除浏览记录定时器
      this.clearBrowseTimer();
      this.videoList.map(function (v) {
        var video = uni.createVideoContext(v.id, _this2);
        video.seek(1);
        video.pause();
      });
    },
    // 暂停/播放视频/双击点赞事件
    pauseVideo: function pauseVideo(e, item) {
      var _this3 = this;
      // 视频对象为空,返回
      if (!this.currentVideo) return;
      if (this.commontShow) {
        this.commontShow = false;
        return;
      }
      clearTimeout(this.clickTimer);
      var currentTime = new Date().getTime();
      var timeRange = currentTime - this.clickTime;
      if (timeRange < 300) {
        // 双击事件
        if (!item.is_liked) {
          this.$emit("fabulous", item, true);
        }
        this.dbClickAnimation(e);
      } else {
        // 单击事件
        this.clickTimer = setTimeout(function () {
          e.preventDefault();
          e.stopPropagation();
          if (_this3.currentStatus == "play") {
            _this3.currentVideo.pause();
            _this3.currentStatus = "pause";
            // 暂停时清除浏览记录定时器
            _this3.clearBrowseTimer();
          } else {
            _this3.currentVideo.play();
            _this3.currentStatus = "play";
            // 恢复播放时重新开始浏览记录追踪
            _this3.startBrowseTracking();
          }
        }, 300);
      }
      this.clickTime = new Date().getTime();
    },
    // 已有爱心叠加双击事件
    fabulousDbClick: function fabulousDbClick(e) {
      clearTimeout(this.fabulousTimer);
      var currentTime = new Date().getTime();
      var timeRange = currentTime - this.fabulousTime;
      if (timeRange < 300) {
        // 双击事件
        this.dbClickAnimation(e);
      } else {}
      this.fabulousTime = new Date().getTime();
    },
    // 双击点赞动画
    dbClickAnimation: function dbClickAnimation(e) {
      var _this4 = this;
      var deg = Math.round(Math.random() * 40 + 5);
      this.fabulousArr.push({
        left: e.detail.x - 25 + "px",
        top: e.detail.y - 25 + "px",
        transform: "rotate(" + (deg % 2 == 0 ? deg : -deg) + "deg)",
        createTime: new Date().getTime()
      });
      var index = this.fabulousArr.length > 0 ? this.fabulousArr.length - 1 : 0;
      setTimeout(function () {
        _this4.$set(_this4.fabulousArr, index, Object.assign(_this4.fabulousArr[index], {
          opacity: 0,
          transform: "scale(3) " + _this4.fabulousArr[index].transform
        }));
        setTimeout(function () {
          _this4.fabulousArr.splice(index, 1, null);
        }, 1000);
      }, 500);
      if (!this.videoList[this.currentIndex].is_liked) {
        this.$set(this.videoList, this.currentIndex, Object.assign(this.videoList[this.currentIndex], {
          is_liked: 1,
          likes_number: this.videoList[this.currentIndex].is_liked + 1
        }));
      }
    },
    // 定时清除点赞动画元素
    clearFabulousArr: function clearFabulousArr() {
      var _this5 = this;
      var indexArr = this.fabulousArr.map(function (v, i) {
        if (v.opacity == "0") {
          return i;
        }
      });
      indexArr.forEach(function (v) {
        _this5.fabulousArr.splice(v, 1);
      });
    },
    // 点赞切换
    toggleFabulous: function toggleFabulous(item, index) {
      this.$emit("fabulous", item, item.is_liked ? false : true);
      this.$set(this.videoList, index, Object.assign(item, {
        is_liked: item.is_liked ? 0 : 1,
        likes_number: item.is_liked ? item.likes_number - 1 : item.likes_number + 1
      }));
    },
    // 收藏
    toggleCollect: function toggleCollect(item, index) {
      this.$emit("collect", item, item.is_collected ? false : true);
      this.$set(this.videoList, index, Object.assign(item, {
        is_collected: item.is_collected ? 0 : 1,
        favorite_number: item.is_collected ? item.favorite_number - 1 : item.favorite_number + 1
      }));
    },
    // 评论
    commontAdd: function commontAdd(count) {
      this.commontShow = true;
      this.currentcomment_number = count;
      this.comment_();
      // this.replyType = "video";
      // this.replyObj = this.currentVideo;
    },
    // 分享
    shareFunc: function shareFunc(item) {
      this.$emit("share", item);
    },
    // 关注
    followFunc: function followFunc(index, item) {
      var flag = item.is_follow == 1 ? false : true;
      if (flag) {
        uni.showToast({
          title: "关注成功",
          icon: "none"
        });
      } else {
        uni.showToast({
          title: "取消关注",
          icon: "none"
        });
      }
      this.$emit("follow", item, flag);
      this.$set(this.videoList, index, Object.assign(item, {
        is_follow: item.is_follow == 1 ? 0 : 1
      }));
    },
    // 评论点赞
    commentFabulous: function commentFabulous(level, item, index, childIndex) {
      var result = item.is_liked == "1" ? "0" : "1";
      item.is_liked = result;
      if (result == "1") {
        item.likes_number = item.likes_number + 1;
      } else {
        item.likes_number = item.likes_number - 1;
      }
      if (level === "first") {
        this.currentComment.list[index] = item;
      } else {
        this.currentComment.list[index].children[childIndex] = item;
      }
      this.$emit("commentFabulous", item);
    },
    // 回复评论
    reply: function reply(level, item, index, childIndex) {
      this.showInput = true;
      this.commentPlaceholder = "\u56DE\u590D@".concat(item.userName);
      // this.replyIndex = childIndex;
      // this.replyType = level;
      // this.replyParentIndex = index;
      this.replyObj = item;
    },
    // 提交评论
    commentCommit: function commentCommit() {
      var _this6 = this;
      this.showInput = false;
      uni.showLoading({
        mask: true
      });
      _common_api__WEBPACK_IMPORTED_MODULE_0__["default"].postComment({
        content: this.replyContent,
        post_id: Number(this.videoList[this.currentIndex].id),
        comment_id: this.replyObj.id || ''
      }).then(function (res) {
        if (res.data.code == 200) {
          uni.showToast({
            title: "评论成功",
            icon: "none"
          });
          _this6.replyContent = "";
          _this6.replyObj = {};
          _this6.currentPage = 1;
          _this6.comment_();
        } else {
          uni.showToast({
            title: "评论失败",
            icon: "none"
          });
        }
      });
    },
    // 收起键盘
    commentBlur: function commentBlur() {
      this.commentPlaceholder = "\u770B\u4E86\u8FD9\u4E48\u591A\uFF0C\u8BF4\u70B9\u4EC0\u4E48\u5427\uFF5E";
      this.showInput = false;
      // this.replyType = "video";
      this.replyObj = this.currentVideo;
    },
    comment_: function comment_() {
      var _this7 = this;
      this.loading = true;
      _common_api__WEBPACK_IMPORTED_MODULE_0__["default"].getPostCommentList({
        params: {
          page: this.currentPage
        }
      }, this.videoList[this.currentIndex].id).then(function (res) {
        var _res$data$data$meta;
        if (_this7.currentPage == 1) {
          _this7.currentCommentList = res.data.data.list;
        } else {
          _this7.currentCommentList = _this7.currentCommentList.concat(res.data.data.list);
        }
        _this7.totalPages = ((_res$data$data$meta = res.data.data.meta) === null || _res$data$data$meta === void 0 || (_res$data$data$meta = _res$data$data$meta.pagination) === null || _res$data$data$meta === void 0 ? void 0 : _res$data$data$meta.total_pages) || 1;
        _this7.$nextTick(function () {
          _this7.loading = false;
        });
      });
    },
    scrollToLower: function scrollToLower() {
      if (this.totalPages > this.currentPage) {
        this.currentPage++;
        this.comment_();
      } else {
        uni.showToast({
          title: "没有更多",
          icon: "none"
        });
      }
    },
    goUserMainPage: function goUserMainPage(nursing_home_id) {
      uni.navigateTo({
        url: "/pages/index/detail?id=" + nursing_home_id
      });
    },
    toggleExpand: function toggleExpand() {
      this.isExpanded = !this.isExpanded;
    },
    onTouchStart: function onTouchStart(e) {
      this.isSeeking = true;
      this.seek(e);
    },
    onTouchMove: function onTouchMove(e) {
      if (this.isSeeking) {
        this.updateProgressBar(e);
      }
    },
    onTouchEnd: function onTouchEnd(e) {
      var _this8 = this;
      if (this.isSeeking) {
        this.isSeeking = false;
        // 延时0.5秒后再执行seek操作
        setTimeout(function () {
          _this8.seek(e);
        }, 500);
      }
    },
    updateProgressBar: function updateProgressBar(e) {
      var touch = e.touches[0] || e.changedTouches[0];
      var offsetX = touch.pageX;
      var systemInfo = uni.getSystemInfoSync();
      var progressWidth = systemInfo.screenWidth;
      var seekTime = offsetX / progressWidth * this.durationSeconds;

      // 更新进度条位置
      this.lineWidth = seekTime / this.durationSeconds * this.width;
    },
    seek: function seek(e) {
      var touch = e.touches[0] || e.changedTouches[0];
      var offsetX = touch.pageX;
      var systemInfo = uni.getSystemInfoSync();
      var progressWidth = systemInfo.screenWidth;
      var seekTime = offsetX / progressWidth * this.durationSeconds;
      // 执行seek操作
      this.currentVideo.seek(seekTime);
    },
    beisu: function beisu() {
      this.showRate = !this.showRate;
    },
    playbackRate: function playbackRate(rate) {
      this.currentVideo.playbackRate(rate);
      this.currentRate = rate;
      this.showRate = false;
    },
    // 开始浏览记录追踪
    startBrowseTracking: function startBrowseTracking() {
      var _this9 = this;
      // 清除之前的定时器
      this.clearBrowseTimer();

      // 重置状态
      this.browseStartTime = Date.now();

      // 每5秒发送一次浏览记录
      this.browseTimer = setInterval(function () {
        if (_this9.currentStatus === "play") {
          var duration = Math.floor((Date.now() - _this9.browseStartTime) / 1000);
          _this9.sendBrowseRecord(duration);
        }
      }, 5000);
    },
    // 发送浏览记录
    sendBrowseRecord: function sendBrowseRecord(duration) {
      var currentVideo = this.videoList[this.currentIndex];
      if (!currentVideo || !currentVideo.id) {
        return;
      }
      _common_api__WEBPACK_IMPORTED_MODULE_0__["default"].browse({
        diary_id: currentVideo.id,
        browse_duration: duration
      }).then(function (res) {
        if (res.data.code === 200) {
          console.log("\u6D4F\u89C8\u8BB0\u5F55\u53D1\u9001\u6210\u529F: \u89C6\u9891ID ".concat(currentVideo.id, ", \u65F6\u957F ").concat(duration, "\u79D2"));
        } else {
          console.error('浏览记录发送失败:', res.data.message);
        }
      }).catch(function (error) {
        console.error('浏览记录发送错误:', error);
      });
    },
    // 清除浏览记录定时器
    clearBrowseTimer: function clearBrowseTimer() {
      if (this.browseTimer) {
        clearInterval(this.browseTimer);
        this.browseTimer = null;
      }
    }
  },
  // 组件销毁时清除定时器
  beforeDestroy: function beforeDestroy() {
    this.clearBrowseTimer();
  },
  // 组件销毁时清除定时器（Vue 3 兼容）
  beforeUnmount: function beforeUnmount() {
    this.clearBrowseTimer();
  },
  // 组件销毁后清除定时器
  destroyed: function destroyed() {
    this.clearBrowseTimer();
  },
  // 组件卸载后清除定时器（Vue 3 兼容）
  unmounted: function unmounted() {
    this.clearBrowseTimer();
  }
});

/***/ }),

/***/ "./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true&":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??clonedRuleSet-22[0].rules[0].use[0]!./node_modules/css-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[1]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[2]!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[3]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??clonedRuleSet-22[0].rules[0].use[4]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??clonedRuleSet-22[0].rules[0].use[5]!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./src/components/y-video-slide/y-video-slide-short.vue?vue&type=style&index=0&id=d25e36ea&lang=scss&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin

/***/ })

}]);
;(global["webpackChunklaolin"] = global["webpackChunklaolin"] || []).push([
    ['components/y-video-slide/y-video-slide-short-create-component'],
    {},
    function(__webpack_require__){
      __webpack_require__('./node_modules/@dcloudio/uni-mp-weixin/dist/index.js')['createComponent'](__webpack_require__("./src/components/y-video-slide/y-video-slide-short.vue"))
    }
]);
