<?php
namespace App\Http\Controllers\Api;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\ApiController;

class FileController extends ApiController
{
    public function index(Request $request)
    {
        // $allowed_ext = ["txt", "doc", "png", "jpg", "pcm", "aac", "mp3", "wav"];
        $index = $request->input('index') ? $request->input('index') : '0';
        $folder_name = 'files/' . date("Y/m");
        $file_path = '';
        if ($request->file('file')) {
            $file = $request->file('file');
            // 获取文件的后缀名
            $extension = strtolower($file->getClientOriginalExtension()) ?: '';
            // 判断是否是允许传入的格式
            // if (!in_array($extension, $allowed_ext)) {
            //     return false;
            // }
            $filename = time() . '_' . Str::random(10) . '.' . $extension;
            // 判断是否有文件夹，没有则创建
            if (!file_exists(storage_path('app/public/' . $folder_name))) {
                mkdir(storage_path('app/public/' . $folder_name), 0777, true);
            }
            $file_path = $file->storeAs($folder_name, $filename, 'public');
        }
        $return_data = [
            'file' => $file_path,
            'index' => $index
        ];
        return $this->success($return_data);
    }
}
