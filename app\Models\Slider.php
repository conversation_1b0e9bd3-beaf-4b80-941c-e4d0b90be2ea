<?php

namespace App\Models;

use Spatie\EloquentSortable\Sortable;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Spatie\EloquentSortable\SortableTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class Slider extends Model implements Sortable
{
    use HasDateTimeFormatter, SortableTrait;
    use SoftDeletes;
    protected $fillable = [
        'id',
        'title',
        'image_url',
        'button_link_url',
        'description',
        'sort_order',
        'status',
        'has_button',
        'is_light',
        'position',
        'deleted_at'
    ];

    // 排序
    protected $orderColumn = 'sort_order';
    protected $sortable = [
        // 设置排序字段名称
        'order_column_name' => 'sort_order',
        // 是否在创建时自动排序，此参数建议设置为true
        'sort_when_creating' => true,
    ];

    public function getFullImageUrlAttribute()
    {
        return getImageUrl($this->image_url);
    }

    public function getFullThumbUrlAttribute()
    {
        return getThumbUrl($this->image_url, '1920', '1080');
    }

    /**
     * 过滤启用的状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

}
