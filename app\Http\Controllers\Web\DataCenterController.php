<?php

namespace App\Http\Controllers\Web;

use App\Models\Diary;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserBrowseRecord;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Web\WebController;

class DataCenterController extends WebController
{
    /**
     * 排行
     */
    public function ranks(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        if (!$nursing_home_id) {
            return redirect()->route('home')->with(['message' => '您还未绑定机构，请联系管理员。']);
        }

        // 获取查询参数
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $dateRange = $request->get('date_range', '7'); // 默认7天
        $sortBy = $request->get('sort_by', 'latest'); // latest, duration, views
        $search = $request->get('search', '');

        // 计算日期范围
        $startDate = match ($dateRange) {
            '1' => now()->startOfDay(),
            '7' => now()->subDays(6)->startOfDay(),
            '30' => now()->subDays(29)->startOfDay(),
            '90' => now()->subDays(89)->startOfDay(),
            default => now()->subDays(6)->startOfDay()
        };

        // 热门视频排行
        $popularVideos = $this->getPopularVideos($nursing_home_id, $startDate);

        // 活跃用户排行
        $activeUsers = $this->getActiveUsers($nursing_home_id, $startDate);

        // 浏览时长分布
        $durationDistribution = $this->getDurationDistribution($nursing_home_id, $startDate);

        // 获取最多转发的内容
        $mostForwardedDiaries = Diary::where('nursing_home_id', $nursing_home_id)
            ->where('status', 3) // 已发布
            ->orderBy('share_number', 'desc')
            ->limit(10)
            ->get();

        return view('data-center.ranks', compact('popularVideos', 'activeUsers', 'durationDistribution', 'mostForwardedDiaries'));
    }

    /**
     * 获取热门视频排行
     */
    private function getPopularVideos($nursing_home_id, $startDate)
    {
        return DB::table('user_browse_records')
            ->join('diaries', 'user_browse_records.diary_id', '=', 'diaries.id')
            ->where('user_browse_records.nursing_home_id', $nursing_home_id)
            ->where('user_browse_records.created_at', '>=', $startDate)
            ->select(
                'diaries.id',
                'diaries.title',
                'diaries.cover_url',
                DB::raw('COUNT(*) as view_count'),
                DB::raw('SUM(user_browse_records.browse_duration) as total_duration'),
                DB::raw('AVG(user_browse_records.browse_duration) as avg_duration'),
                DB::raw('COUNT(DISTINCT user_browse_records.user_id) as unique_viewers')
            )
            ->groupBy('diaries.id', 'diaries.title', 'diaries.cover_url')
            ->orderBy('view_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 获取活跃用户排行
     */
    private function getActiveUsers($nursing_home_id, $startDate)
    {
        return User::select([
            'users.id',
            'users.nickname',
            'users.real_name',
            'users.avatar_url',
            DB::raw('COUNT(user_browse_records.id) as view_count'),
            DB::raw('SUM(user_browse_records.browse_duration) as total_duration'),
            DB::raw('COUNT(DISTINCT user_browse_records.diary_id) as videos_watched')
        ])
            ->join('user_browse_records', 'users.id', '=', 'user_browse_records.user_id')
            ->where('user_browse_records.nursing_home_id', $nursing_home_id)
            ->where('user_browse_records.created_at', '>=', $startDate)
            ->whereNotNull('user_browse_records.user_id')
            ->groupBy('users.id', 'users.nickname', 'users.real_name', 'users.avatar_url')
            ->orderBy('view_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * 获取浏览时长分布
     */
    private function getDurationDistribution($nursing_home_id, $startDate)
    {
        $records = UserBrowseRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate)
            ->pluck('browse_duration');

        $distribution = [
            '0-10秒' => 0,
            '11-30秒' => 0,
            '31-60秒' => 0,
            '1-3分钟' => 0,
            '3-5分钟' => 0,
            '5分钟以上' => 0,
        ];

        foreach ($records as $duration) {
            if ($duration <= 10) {
                $distribution['0-10秒']++;
            } elseif ($duration <= 30) {
                $distribution['11-30秒']++;
            } elseif ($duration <= 60) {
                $distribution['31-60秒']++;
            } elseif ($duration <= 180) {
                $distribution['1-3分钟']++;
            } elseif ($duration <= 300) {
                $distribution['3-5分钟']++;
            } else {
                $distribution['5分钟以上']++;
            }
        }

        return $distribution;
    }
}
