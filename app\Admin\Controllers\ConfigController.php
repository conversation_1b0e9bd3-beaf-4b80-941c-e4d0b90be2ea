<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Config;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ConfigController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Config(), function (Grid $grid) {
            $grid->paginate(100);
            $grid->column('title');
            $grid->column('key');
            $grid->column('type')->using(Config::type())->badge();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title')->width(3);
                $filter->like('key')->width(3);
                $filter->equal('status')
                    ->radio(['' => '全部'] + Config::type())
                    ->default(1);
                $filter->between('created_at')->datetime();
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Config(), function (Form $form) {
            $form->text('title');
            $form->text('key');
            $form->radio('type')
                ->options(Config::type())->default(1)
                ->when(1, function (Form $form) {
                    $form->textarea('value1', '值');
                })->when(2, function (Form $form) {
                    $form->editor('value2', '值')->imageDirectory('images/' . date("Y/m"))->height('600');
                })->when(3, function (Form $form) {
                    $form->image('value3', '值')->move('images/' . date("Y/m"))->autoUpload()->saveFullUrl()->uniqueName();
                })->when(4, function (Form $form) {
                    $form->multipleImage('value4', '值')->move('images/' . date("Y/m"))->limit(50)->sortable()->autoUpload()->saveFullUrl()->uniqueName();
                })->when(5, function (Form $form) {
                    $form->file('value5', '值')->move('files/' . date("Y/m"))->autoUpload()->uniqueName();
                })->when(6, function (Form $form) {
                    $form->multipleFile('value6', '值')->move('files/' . date("Y/m"))->sortable()->autoUpload()->uniqueName();
                })->when(7, function (Form $form) {
                    $form->date('value7', '值');
                })->when(8, function (Form $form) {
                    $form->datetime('value8', '值');
                })->when(9, function (Form $form) {
                    $form->switch('value9');
                });
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
