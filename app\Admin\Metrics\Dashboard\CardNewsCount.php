<?php

namespace App\Admin\Metrics\Dashboard;

use App\Models\News;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Metrics\Card;
use Illuminate\Contracts\Support\Renderable;

class CardNewsCount extends Card
{
    /**
     * 卡片底部内容.
     *
     * @var string|Renderable|\Closure
     */
    protected $footer;

    // 保存自定义参数
    protected $data = [];

    // 构造方法参数必须设置默认值
    public function __construct(array $data = [])
    {
        $this->data = [];

        parent::__construct();
    }

    protected function init()
    {
        parent::init();

        // 设置标题
        $this->title('新闻数量统计');
    }

    /**
     * 处理请求.
     *
     * @param Request $request
     *
     * @return void
     */
    public function handle(Request $request)
    {
        $order_count = News::count();
        $this->content($order_count);

        $footer_link = admin_url('news?status=0');
        $this->footer(<<<HTML
<a href="{$footer_link}" class="text-80 font-weight-bold">
    查看详情
    <i class="feather icon-arrow-right"></i>
</a>
HTML
        );
    }

    // 传递自定义参数到 handle 方法
    public function parameters(): array
    {
        return $this->data;
    }

    /**
     * 设置卡片底部内容
     *
     * @param string|Renderable|\Closure $footer
     *
     * @return $this
     */
    public function footer($footer)
    {
        $this->footer = $footer;

        return $this;
    }

    /**
     * 渲染卡片内容.
     *
     * @return string
     */
    public function renderContent()
    {
        $content = parent::renderContent();

        return <<<HTML
<div class="d-flex justify-content-between align-items-center mt-1" style="margin-bottom: 2px">
    <h2 class="ml-1 font-large"><i class="fa fa-calendar-plus-o fa-align-left mr-1"></i>{$content}</h2>
</div>
<div class="ml-1 mt-1 font-weight-bold text-80">
    {$this->renderFooter()}
</div>
HTML;
    }

    /**
     * @return string
     */
    public function renderFooter()
    {
        return $this->toString($this->footer);
    }
}
