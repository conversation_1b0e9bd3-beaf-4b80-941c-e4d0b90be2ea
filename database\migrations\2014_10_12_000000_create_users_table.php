<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 50)->unique()->nullable();
            $table->string('mobile', 20)->comment('手机(帐号)')->nullable()->index();
            $table->string('email')->unique()->nullable();
            $table->string('password')->nullable();
            $table->string('nickname')->comment('昵称')->nullable();
            $table->string('real_name')->comment('姓名')->nullable();
            $table->string('avatar_url')->comment('头像')->nullable();
            $table->tinyInteger('gender')->unsigned()->default(0)->nullable()->comment('性别 0：保密 1：男，2：女')->index();
            $table->date('birthdate')->comment('出生日期')->nullable();
            $table->string('province')->nullable()->comment('省份');
            $table->string('city')->nullable()->comment('城市');
            $table->string('district')->nullable()->comment('地区');
            $table->string('address')->comment('详细地址')->nullable();
            $table->tinyInteger('role')->comment('用户角色')->default(1);
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('养老院');
            $table->tinyInteger('status')->comment('帐号状态')->default(0);
            $table->string('wx_nickname', 80)->comment('微信昵称')->nullable();
            $table->string('wx_avatar')->comment('微信头像')->nullable();
            $table->string('wx_openid', 80)->comment('公众号openid')->nullable();
            $table->string('xcx_openid')->comment('小程序openid')->nullable();
            $table->string('unionid')->comment('unionid')->nullable();
            $table->string('oauth_scope')->comment('微信授权方式 1：静默授权，2：用户信息授权')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
