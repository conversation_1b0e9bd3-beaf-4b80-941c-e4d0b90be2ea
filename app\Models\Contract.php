<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $fillable = ['user_id', 'mobile', 'nursing_home_name', 'sign_at', 'status'];

    protected $casts = [
        'sign_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
