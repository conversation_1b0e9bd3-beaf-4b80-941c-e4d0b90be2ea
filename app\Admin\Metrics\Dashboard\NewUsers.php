<?php

namespace App\Admin\Metrics\Dashboard;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Widgets\Metrics\Line;

class NewUsers extends Line
{
    /**
     * 初始化卡片内容
     *
     * @return void
     */
    protected function init()
    {
        parent::init();

        $this->title('新用户数量');
        $this->dropdown([
            '7'   => '近一周',
            '30'  => '近一月',
            '365' => '近一年',
        ]);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return mixed|void
     */
    public function handle(Request $request)
    {
        $global_count = User::where('status', 1);
        $global_filter = User::selectRaw("COUNT(status) AS count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
            ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
            ->where('status', 1);
        $count = 0;
        $result = [];

        switch ($request->get('option')) {
            case '365':
                $count = $global_count->whereBetween('created_at', [now()->subYear(), now()])->count();

                $dateList = [];
                for ($i = 12; $i >= 1; $i--) {
                    $dateList[] = [
                        'start' => now()->subMonth($i),
                        'end' => now()->subMonth($i - 1)
                    ];
                }
                $result = [];
                foreach ($dateList as $value) {
                    $result_filter = User::selectRaw("COUNT(status) AS count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
                        ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
                        ->where('status', 1)->whereBetween('created_at', [$value['start'], $value['end']])->get();
                    $result[] = $result_filter->sum('count');
                }
                break;
            case '30':
                $count = $global_count->whereBetween('created_at', [now()->subMonth(), now()])->count();
                $result_filter = $global_filter->whereBetween('created_at', [now()->subMonth(), now()])->get();

                $dateList = [];
                for ($i = 29; $i >= 0; $i--) {
                    $dateList[] = now()->subDay($i)->toDateString();
                }
                $result = [];
                foreach ($dateList as $value) {
                    $item = $result_filter->firstWhere('day', $value);
                    $result[] = $item ? $item->count : 0;
                }
                break;
            case '7':
            default:
                $count = $global_count->whereBetween('created_at', [now()->subWeek(), now()])->count();
                $result_filter = $global_filter->whereBetween('created_at', [now()->subWeek(), now()])->get();

                $dateList = [];
                for ($i = 6; $i >= 0; $i--) {
                    $dateList[] = now()->subDay($i)->toDateString();
                }
                $result = [];
                foreach ($dateList as $value) {
                    $item = $result_filter->firstWhere('day', $value);
                    $result[] = $item ? $item->count : 0;
                }
                break;
        }
        // 卡片内容
        $this->withContent($count);
        // 图表数据
        $this->withChart($result);
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data)
    {
        return $this->chart([
            'series' => [
                [
                    'name' => $this->title,
                    'data' => $data,
                ],
            ],
        ]);
    }

    /**
     * 设置卡片内容.
     *
     * @param string $content
     *
     * @return $this
     */
    public function withContent($content)
    {
        return $this->content(
            <<<HTML
                <div class="d-flex justify-content-between align-items-center mt-4" style="margin-bottom: 2px">
                    <h2 class="ml-1 font-lg-1">{$content}</h2>
                    <span class="mb-4 mr-1 text-80">{$this->title}</span>
                </div>
            HTML
        );
    }
}
