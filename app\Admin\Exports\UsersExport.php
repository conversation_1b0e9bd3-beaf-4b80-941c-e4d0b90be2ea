<?php

namespace App\Admin\Exports;

use Dcat\Admin\Grid;
use App\Admin\Repositories\Common;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Dcat\Admin\Grid\Exporters\AbstractExporter;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class UsersExport extends AbstractExporter implements FromCollection, WithEvents, WithHeadings, WithMapping, WithColumnWidths, ShouldAutoSize
{
    use Exportable, RegistersEventListeners;

    public function __construct()
    {
        parent::__construct();
    }

    public function collection()
    {
        return $this->buildData();
    }

    /**
     * Get data with export query.
     */
    public function buildData(?int $page = null, ?int $perPage = null)
    {
        $model = $this->getGridModel();
        // current page
        if ($this->scope === Grid\Exporter::SCOPE_CURRENT_PAGE) {
            $page = $model->getCurrentPage();
            $perPage = $model->getPerPage();
        }

        $model->usePaginate(false);
        if ($page && $this->scope !== Grid\Exporter::SCOPE_SELECTED_ROWS) {
            $perPage = $perPage ?: $this->getChunkSize();
            $model->forPage($page, $perPage);
        }
        $data = $this->grid->processFilter();
        $model->reset();
        return $data;
    }

    public function headings(): array
    {
        return [
            [
                'id'         => '序号',
                'nickname'   => '用户名',
                'mobile'     => '手机号',
                'created_at' => '注册时间',
                'status'     => '状态',
            ]
        ];
    }

    public function map($row): array
    {
        return [
            '',
            $row->nickname,
            $row->mobile,
            $row->created_at,
            $row->status == Common::STATUS_TRUE ? '激活' : '禁用',
        ];
    }

    public static function afterSheet(AfterSheet $event)
    {
        $work_sheet = $event->sheet->getDelegate();
        $count_row = $work_sheet->getHighestDataRow(); // 获取有数据的行数

        $collection = collect([]);
        for ($i = 2; $i <= $count_row; $i++) {
            $work_sheet->getCell("A$i")->setValue($i - 1);
        }

        // 设置整体样式
        $work_sheet->getStyle("A1:E$count_row")->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '00000000'],
                ],
            ],
            'font' => [
                'name' => '宋体',
                'size' => 14,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                'wrapText' => true, // 自动换行
            ],
        ]);

        // 调整其它单元格样式
        $work_sheet->getStyle("A1:E1")->getFont()->setBold(true)->setSize(14);
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 20,
            'C' => 20,
            'D' => 20,
            'E' => 10,
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function export()
    {
        $this->download($this->getFilename() . '.xlsx')->prepare(request())->send();
        exit;
    }
}
