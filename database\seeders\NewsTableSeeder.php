<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class NewsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('news')->delete();
        
        \DB::table('news')->insert(array (
            0 => 
            array (
                'id' => 1,
                'title' => '新闻一',
                'news_category_id' => 1,
                'banner_url' => NULL,
                'excerpt' => '测试新闻',
                'content' => '<p>测试新闻</p>',
                'read_count' => 100,
                'is_recommend' => 1,
                'status' => 1,
                'created_at' => '2022-11-02 17:06:28',
                'updated_at' => '2022-11-02 17:06:28',
            ),
            1 => 
            array (
                'id' => 2,
                'title' => '新闻二',
                'news_category_id' => 2,
                'banner_url' => NULL,
                'excerpt' => '测试新闻',
                'content' => '<p>测试新闻</p>',
                'read_count' => 100,
                'is_recommend' => 1,
                'status' => 1,
                'created_at' => '2022-11-02 17:06:28',
                'updated_at' => '2022-11-02 17:06:28',
            ),
            2 => 
            array (
                'id' => 3,
                'title' => '新闻三',
                'news_category_id' => 3,
                'banner_url' => NULL,
                'excerpt' => '测试新闻',
                'content' => '<p>测试新闻</p>',
                'read_count' => 100,
                'is_recommend' => 1,
                'status' => 1,
                'created_at' => '2022-11-02 17:06:28',
                'updated_at' => '2022-11-02 17:06:28',
            ),
        ));
        
        
    }
}