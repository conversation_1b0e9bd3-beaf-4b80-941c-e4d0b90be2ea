/*!
 * Library to detect file mime type of a Uint8Array.
 *
 * Modified from https://github.com/sindresorhus/file-type to be used standalone on browser based apps.
 *
 * This library requires Node "buffer" module as a pre-requisite. The "buffer" module is made available in this repo
 * for standalone use via the `buffer.js` script which needs to be loaded before this file on the page.
 *
 * Author: <PERSON><PERSON><PERSON>, Krajee.com
 */
"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var r,n,a=[],o=!0,f=!1;try{for(i=i.call(e);!(o=(r=i.next()).done)&&(a.push(r.value),!t||a.length!==t);o=!0);}catch(e){f=!0,n=e}finally{try{o||null==i.return||i.return()}finally{if(f)throw n}}return a}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,t=function(){};return{s:t,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:t}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,a=!0,o=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){o=!0,n=e},f:function(){try{a||null==i.return||i.return()}finally{if(o)throw n}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=new Array(t);i<t;i++)r[i]=e[i];return r}function _instanceof(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):e instanceof t}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function _classCallCheck(e,t){if(!_instanceof(e,t))throw new TypeError("Cannot call a class as a function")}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _createSuper(i){var r=_isNativeReflectConstruct();return function(){var e,t=_getPrototypeOf(i);return _possibleConstructorReturn(this,r?(e=_getPrototypeOf(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _wrapNativeSuper(e){var i="function"==typeof Map?new Map:void 0;return(_wrapNativeSuper=function(e){if(null===e||!_isNativeFunction(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==i){if(i.has(e))return i.get(e);i.set(e,t)}function t(){return _construct(e,arguments,_getPrototypeOf(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(t,e)})(e)}function _construct(e,t,i){return(_construct=_isNativeReflectConstruct()?Reflect.construct.bind():function(e,t,i){var r=[null];r.push.apply(r,t);r=new(Function.bind.apply(e,r));return i&&_setPrototypeOf(r,i.prototype),r}).apply(null,arguments)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _isNativeFunction(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var KrajeeFileTypeConfig={minimumBytes:4100,defaultMessages:"End-Of-Stream",tarHeaderChecksumMatches:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,i=Number.parseInt(e.toString("utf8",148,154).replace(/\0.*$/,"").trim(),8);if(Number.isNaN(i))return!1;for(var r=256,n=t;n<t+148;n++)r+=e[n];for(var a=t+156;a<t+512;a++)r+=e[a];return i===r},uint32SyncSafeToken:{get:function(e,t){return 127&e[t+3]|e[t+2]<<7|e[t+1]<<14|e[t]<<21},len:4},dv:function(e){return new DataView(e.buffer,e.byteOffset)},Token:{UINT8:{len:1,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getUint8(t)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setUint8(t,i),t+1}},UINT16_LE:{len:2,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getUint16(t,!0)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setUint16(t,i,!0),t+2}},UINT16_BE:{len:2,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getUint16(t)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setUint16(t,i),t+2}},INT32_BE:{len:4,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getInt32(t)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setInt32(t,i),t+4}},UINT32_LE:{len:4,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getUint32(t,!0)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setUint32(t,i,!0),t+4}},UINT32_BE:{len:4,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getUint32(t)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setUint32(t,i),t+4}},UINT64_LE:{len:8,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getBigUint64(t,!0)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setBigUint64(t,i,!0),t+8}},UINT64_BE:{len:8,get:function(e,t){return KrajeeFileTypeConfig.dv(e).getBigUint64(t)},put:function(e,t,i){return KrajeeFileTypeConfig.dv(e).setBigUint64(t,i),t+8}}}},EndOfStreamError=function(){_inherits(t,_wrapNativeSuper(Error));var e=_createSuper(t);function t(){return _classCallCheck(this,t),e.call(this,KrajeeFileTypeConfig.defaultMessages)}return _createClass(t)}(),StringType=function(){function i(e,t){_classCallCheck(this,i),this.len=e,this.encoding=t}return _createClass(i,[{key:"get",value:function(e,t){return Buffer.from(e).toString(this.encoding,t,t+this.len)}}]),i}();async function fileTypeFromTokenizer(e){try{return(new FileTypeParser).parse(e)}catch(e){if(!_instanceof(e,EndOfStreamError))throw e}}var BufferTokenizer=function(){function i(e,t){_classCallCheck(this,i),this.position=0,this.numBuffer=new Uint8Array(8),this.fileInfo=t||{},this.uint8Array=e,this.fileInfo.size=this.fileInfo.size||e.length}return _createClass(i,[{key:"readToken",value:async function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.position,i=Buffer.alloc(e.len);if(await this.readBuffer(i,{position:t})<e.len)throw new EndOfStreamError;return e.get(i,0)}},{key:"peekToken",value:async function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.position,i=Buffer.alloc(e.len);if(await this.peekBuffer(i,{position:t})<e.len)throw new EndOfStreamError;return e.get(i,0)}},{key:"readBuffer",value:async function(e,t){if(t&&t.position){if(t.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");this.position=t.position}t=await this.peekBuffer(e,t);return this.position+=t,t}},{key:"peekBuffer",value:async function(e,t){var i=this.normalizeOptions(e,t),t=Math.min(this.uint8Array.length-i.position,i.length);if(!i.mayBeLess&&t<i.length)throw new EndOfStreamError;return e.set(this.uint8Array.subarray(i.position,i.position+t),i.offset),t}},{key:"readNumber",value:async function(e){if(await this.readBuffer(this.numBuffer,{length:e.len})<e.len)throw new EndOfStreamError;return e.get(this.numBuffer,0)}},{key:"peekNumber",value:async function(e){if(await this.peekBuffer(this.numBuffer,{length:e.len})<e.len)throw new EndOfStreamError;return e.get(this.numBuffer,0)}},{key:"close",value:async function(){}},{key:"ignore",value:async function(e){if(void 0!==this.fileInfo.size){var t=this.fileInfo.size-this.position;if(t<e)return this.position+=t,t}return this.position+=e,e}},{key:"normalizeOptions",value:function(e,t){if(t&&void 0!==t.position&&t.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");return t?{mayBeLess:!0===t.mayBeLess,offset:t.offset||0,length:t.length||e.length-(t.offset||0),position:t.position||this.position}:{mayBeLess:!1,offset:0,length:e.length,position:this.position}}}]),i}(),FileTypeParser=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"_check",value:function(e,t,i){i={offset:0,...i};var r=_createForOfIteratorHelper(t.entries());try{for(r.s();!(a=r.n()).done;){var n=_slicedToArray(a.value,2),a=n[0],n=n[1];if(i.mask){if(n!==(i.mask[a]&e[a+i.offset]))return!1}else if(n!==e[a+i.offset])return!1}}catch(e){r.e(e)}finally{r.f()}return!0}},{key:"check",value:function(e,t){return this._check(this.buffer,e,t)}},{key:"stringToBytes",value:function(e){return _toConsumableArray(e).map(function(e){return e.charCodeAt(0)})}},{key:"checkString",value:function(e,t){return this.check(this.stringToBytes(e),t)}},{key:"parse",value:async function(e){if(!(_instanceof(e,Uint8Array)||_instanceof(e,ArrayBuffer)||_instanceof(e,BufferTokenizer)))throw new TypeError("Expected the `input` argument to be of type `Uint8Array` or `Buffer` or `ArrayBuffer`, got `".concat(_typeof(e),"`"));var t=e;if(!_instanceof(t,BufferTokenizer)){e=_instanceof(e,Uint8Array)?e:new Uint8Array(e);if(!(e&&1<e.length))return;t=new BufferTokenizer(e)}try{return this.parseTokenizer(t)}catch(e){if(!_instanceof(e,EndOfStreamError))throw e}}},{key:"parseTokenizer",value:async function(n){var a=KrajeeFileTypeConfig.Token;if(this.buffer=Buffer.alloc(KrajeeFileTypeConfig.minimumBytes),void 0===n.fileInfo.size&&(n.fileInfo.size=Number.MAX_SAFE_INTEGER),await(this.tokenizer=n).peekBuffer(this.buffer,{length:12,mayBeLess:!0}),this.check([66,77]))return{ext:"bmp",mime:"image/bmp"};if(this.check([11,119]))return{ext:"ac3",mime:"audio/vnd.dolby.dd-raw"};if(this.check([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(this.check([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if(this.check([37,33]))return await n.peekBuffer(this.buffer,{length:24,mayBeLess:!0}),this.checkString("PS-Adobe-",{offset:2})&&this.checkString(" EPSF-",{offset:14})?{ext:"eps",mime:"application/eps"}:{ext:"ps",mime:"application/postscript"};if(this.check([31,160])||this.check([31,157]))return{ext:"Z",mime:"application/x-compress"};if(this.check([71,73,70]))return{ext:"gif",mime:"image/gif"};if(this.check([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(this.check([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(this.check([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(this.check([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(this.checkString("ID3")){await n.ignore(6);var e=await n.readToken(KrajeeFileTypeConfig.uint32SyncSafeToken);return n.position+e>n.fileInfo.size?{ext:"mp3",mime:"audio/mpeg"}:(await n.ignore(e),fileTypeFromTokenizer(n))}if(this.checkString("MP+"))return{ext:"mpc",mime:"audio/x-musepack"};if((67===this.buffer[0]||70===this.buffer[0])&&this.check([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(this.checkString("FLIF"))return{ext:"flif",mime:"image/flif"};if(this.checkString("8BPS"))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};if(this.checkString("WEBP",{offset:8}))return{ext:"webp",mime:"image/webp"};if(this.checkString("MPCK"))return{ext:"mpc",mime:"audio/x-musepack"};if(this.checkString("FORM"))return{ext:"aif",mime:"audio/aiff"};if(this.checkString("icns",{offset:0}))return{ext:"icns",mime:"image/icns"};if(this.check([80,75,3,4])){try{for(;n.position+30<n.fileInfo.size;){await n.readBuffer(this.buffer,{length:30});var t={compressedSize:this.buffer.readUInt32LE(18),uncompressedSize:this.buffer.readUInt32LE(22),filenameLength:this.buffer.readUInt16LE(26),extraFieldLength:this.buffer.readUInt16LE(28)};if(t.filename=await n.readToken(new StringType(t.filenameLength,"utf-8")),await n.ignore(t.extraFieldLength),"META-INF/mozilla.rsa"===t.filename)return{ext:"xpi",mime:"application/x-xpinstall"};if(t.filename.endsWith(".rels")||t.filename.endsWith(".xml"))switch(t.filename.split("/")[0]){case"_rels":break;case"word":return{ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"};case"ppt":return{ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"};case"xl":return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}if(t.filename.startsWith("xl/"))return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};if(t.filename.startsWith("3D/")&&t.filename.endsWith(".model"))return{ext:"3mf",mime:"model/3mf"};if("mimetype"===t.filename&&t.compressedSize===t.uncompressedSize)switch((await n.readToken(new StringType(t.compressedSize,"utf-8"))).trim()){case"application/epub+zip":return{ext:"epub",mime:"application/epub+zip"};case"application/vnd.oasis.opendocument.text":return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};case"application/vnd.oasis.opendocument.spreadsheet":return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};case"application/vnd.oasis.opendocument.presentation":return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"}}if(0===t.compressedSize)for(var i=-1;i<0&&n.position<n.fileInfo.size;)await n.peekBuffer(this.buffer,{mayBeLess:!0}),i=this.buffer.indexOf("504B0304",0,"hex"),await n.ignore(0<=i?i:this.buffer.length);else await n.ignore(t.compressedSize)}}catch(e){if(!_instanceof(e,EndOfStreamError))throw e}return{ext:"zip",mime:"application/zip"}}if(this.checkString("OggS")){await n.ignore(28);var r=Buffer.alloc(8);return await n.readBuffer(r),this._check(r,[79,112,117,115,72,101,97,100])?{ext:"opus",mime:"audio/opus"}:this._check(r,[128,116,104,101,111,114,97])?{ext:"ogv",mime:"video/ogg"}:this._check(r,[1,118,105,100,101,111,0])?{ext:"ogm",mime:"video/ogg"}:this._check(r,[127,70,76,65,67])?{ext:"oga",mime:"audio/ogg"}:this._check(r,[83,112,101,101,120,32,32])?{ext:"spx",mime:"audio/ogg"}:this._check(r,[1,118,111,114,98,105,115])?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"}}if(this.check([80,75])&&(3===this.buffer[2]||5===this.buffer[2]||7===this.buffer[2])&&(4===this.buffer[3]||6===this.buffer[3]||8===this.buffer[3]))return{ext:"zip",mime:"application/zip"};if(this.checkString("ftyp",{offset:4})&&0!=(96&this.buffer[8])){var o=this.buffer.toString("binary",8,12).replace("\0"," ").trim();switch(o){case"avif":case"avis":return{ext:"avif",mime:"image/avif"};case"mif1":return{ext:"heic",mime:"image/heif"};case"msf1":return{ext:"heic",mime:"image/heif-sequence"};case"heic":case"heix":return{ext:"heic",mime:"image/heic"};case"hevc":case"hevx":return{ext:"heic",mime:"image/heic-sequence"};case"qt":return{ext:"mov",mime:"video/quicktime"};case"M4V":case"M4VH":case"M4VP":return{ext:"m4v",mime:"video/x-m4v"};case"M4P":return{ext:"m4p",mime:"video/mp4"};case"M4B":return{ext:"m4b",mime:"audio/mp4"};case"M4A":return{ext:"m4a",mime:"audio/x-m4a"};case"F4V":return{ext:"f4v",mime:"video/mp4"};case"F4P":return{ext:"f4p",mime:"video/mp4"};case"F4A":return{ext:"f4a",mime:"audio/mp4"};case"F4B":return{ext:"f4b",mime:"audio/mp4"};case"crx":return{ext:"cr3",mime:"image/x-canon-cr3"};default:return o.startsWith("3g")?o.startsWith("3g2")?{ext:"3g2",mime:"video/3gpp2"}:{ext:"3gp",mime:"video/3gpp"}:{ext:"mp4",mime:"video/mp4"}}}if(this.checkString("MThd"))return{ext:"mid",mime:"audio/midi"};if(this.checkString("wOFF")&&(this.check([0,1,0,0],{offset:4})||this.checkString("OTTO",{offset:4})))return{ext:"woff",mime:"font/woff"};if(this.checkString("wOF2")&&(this.check([0,1,0,0],{offset:4})||this.checkString("OTTO",{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(this.check([212,195,178,161])||this.check([161,178,195,212]))return{ext:"pcap",mime:"application/vnd.tcpdump.pcap"};if(this.checkString("DSD "))return{ext:"dsf",mime:"audio/x-dsf"};if(this.checkString("LZIP"))return{ext:"lz",mime:"application/x-lzip"};if(this.checkString("fLaC"))return{ext:"flac",mime:"audio/x-flac"};if(this.check([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(this.checkString("wvpk"))return{ext:"wv",mime:"audio/wavpack"};if(this.checkString("%PDF")){await n.ignore(1350);r=Buffer.alloc(Math.min(10485760,n.fileInfo.size));return await n.readBuffer(r,{mayBeLess:!0}),r.includes(Buffer.from("AIPrivateData"))?{ext:"ai",mime:"application/postscript"}:{ext:"pdf",mime:"application/pdf"}}if(this.check([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(this.check([73,73])){var f=await this.readTiffHeader(!1);if(f)return f}if(this.check([77,77])){f=await this.readTiffHeader(!0);if(f)return f}if(this.checkString("MAC "))return{ext:"ape",mime:"audio/ape"};if(this.check([26,69,223,163])){var c=async function(){for(var e=await n.peekNumber(a.UINT8),t=128,i=0;0==(e&t);)++i,t>>=1;var r=Buffer.alloc(i+1);return await n.readBuffer(r),r},s=async function(){var e=await c(),t=await c();t[0]^=128>>t.length-1;var i=Math.min(6,t.length);return{id:e.readUIntBE(0,e.length),len:t.readUIntBE(t.length-i,i)}};switch(await async function(e){for(;0<e;){var t=await s();if(17026===t.id)return(await n.readToken(new StringType(t.len,"utf-8"))).replace(/\00.*$/g,"");await n.ignore(t.len),--e}}((await s()).len)){case"webm":return{ext:"webm",mime:"video/webm"};case"matroska":return{ext:"mkv",mime:"video/x-matroska"};default:return}}if(this.check([82,73,70,70])){if(this.check([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(this.check([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(this.check([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(this.checkString("SQLi"))return{ext:"sqlite",mime:"application/x-sqlite3"};if(this.check([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(this.checkString("Cr24"))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(this.checkString("MSCF")||this.checkString("ISc("))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(this.check([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(this.check([197,208,211,198]))return{ext:"eps",mime:"application/eps"};if(this.check([40,181,47,253]))return{ext:"zst",mime:"application/zstd"};if(this.check([127,69,76,70]))return{ext:"elf",mime:"application/x-elf"};if(this.check([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(this.checkString("#!AMR"))return{ext:"amr",mime:"audio/amr"};if(this.checkString("{\\rtf"))return{ext:"rtf",mime:"application/rtf"};if(this.check([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(this.checkString("IMPM"))return{ext:"it",mime:"audio/x-it"};if(this.checkString("-lh0-",{offset:2})||this.checkString("-lh1-",{offset:2})||this.checkString("-lh2-",{offset:2})||this.checkString("-lh3-",{offset:2})||this.checkString("-lh4-",{offset:2})||this.checkString("-lh5-",{offset:2})||this.checkString("-lh6-",{offset:2})||this.checkString("-lh7-",{offset:2})||this.checkString("-lzs-",{offset:2})||this.checkString("-lz4-",{offset:2})||this.checkString("-lz5-",{offset:2})||this.checkString("-lhd-",{offset:2}))return{ext:"lzh",mime:"application/x-lzh-compressed"};if(this.check([0,0,1,186])){if(this.check([33],{offset:4,mask:[241]}))return{ext:"mpg",mime:"video/MP1S"};if(this.check([68],{offset:4,mask:[196]}))return{ext:"mpg",mime:"video/MP2P"}}if(this.checkString("ITSF"))return{ext:"chm",mime:"application/vnd.ms-htmlhelp"};if(this.check([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(this.checkString("<?xml "))return{ext:"xml",mime:"application/xml"};if(this.check([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(this.check([82,97,114,33,26,7])&&(0===this.buffer[6]||1===this.buffer[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(this.checkString("solid "))return{ext:"stl",mime:"model/stl"};if(this.checkString("BLENDER"))return{ext:"blend",mime:"application/x-blender"};if(this.checkString("!<arch>"))return await n.ignore(8),"debian-binary"===await n.readToken(new StringType(13,"ascii"))?{ext:"deb",mime:"application/x-deb"}:{ext:"ar",mime:"application/x-unix-archive"};if(this.check([137,80,78,71,13,10,26,10])){await n.ignore(8);do{var u=await async function(){return{length:await n.readToken(a.INT32_BE),type:await n.readToken(new StringType(4,"binary"))}}();if(u.length<0)return;switch(u.type){case"IDAT":return{ext:"png",mime:"image/png"};case"acTL":return{ext:"apng",mime:"image/apng"};default:await n.ignore(u.length+4)}}while(n.position+8<n.fileInfo.size);return{ext:"png",mime:"image/png"}}if(this.check([65,82,82,79,87,49,0,0]))return{ext:"arrow",mime:"application/x-apache-arrow"};if(this.check([103,108,84,70,2,0,0,0]))return{ext:"glb",mime:"model/gltf-binary"};if(this.check([102,114,101,101],{offset:4})||this.check([109,100,97,116],{offset:4})||this.check([109,111,111,118],{offset:4})||this.check([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(this.check([239,187,191])&&this.checkString("<?xml",{offset:3}))return{ext:"xml",mime:"application/xml"};if(this.check([73,73,82,79,8,0,0,0,24]))return{ext:"orf",mime:"image/x-olympus-orf"};if(this.checkString("gimp xcf "))return{ext:"xcf",mime:"image/x-xcf"};if(this.check([73,73,85,0,24,0,0,0,136,231,116,216]))return{ext:"rw2",mime:"image/x-panasonic-rw2"};if(this.check([48,38,178,117,142,102,207,17,166,217])){for(await n.ignore(30);n.position+24<n.fileInfo.size;){var m=await async function(){var e=Buffer.alloc(16);return await n.readBuffer(e),{id:e,size:Number(await n.readToken(a.UINT64_LE))}}(),h=m.size-24;if(this._check(m.id,[145,7,220,183,183,169,207,17,142,230,0,192,12,32,83,101])){m=Buffer.alloc(16);if(h-=await n.readBuffer(m),this._check(m,[64,158,105,248,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"audio/x-ms-asf"};if(this._check(m,[192,239,25,188,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"video/x-ms-asf"};break}await n.ignore(h)}return{ext:"asf",mime:"application/vnd.ms-asf"}}if(this.check([171,75,84,88,32,49,49,187,13,10,26,10]))return{ext:"ktx",mime:"image/ktx"};if((this.check([126,16,4])||this.check([126,24,4]))&&this.check([48,77,73,69],{offset:4}))return{ext:"mie",mime:"application/x-mie"};if(this.check([39,10,0,0,0,0,0,0,0,0,0,0],{offset:2}))return{ext:"shp",mime:"application/x-esri-shape"};if(this.check([0,0,0,12,106,80,32,32,13,10,135,10]))switch(await n.ignore(20),await n.readToken(new StringType(4,"ascii"))){case"jp2 ":return{ext:"jp2",mime:"image/jp2"};case"jpx ":return{ext:"jpx",mime:"image/jpx"};case"jpm ":return{ext:"jpm",mime:"image/jpm"};case"mjp2":return{ext:"mj2",mime:"image/mj2"};default:return}if(this.check([255,10])||this.check([0,0,0,12,74,88,76,32,13,10,135,10]))return{ext:"jxl",mime:"image/jxl"};if(this.check([254,255,0,60,0,63,0,120,0,109,0,108])||this.check([255,254,60,0,63,0,120,0,109,0,108,0]))return{ext:"xml",mime:"application/xml"};if(this.check([0,0,1,186])||this.check([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};if(this.check([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(this.check([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(this.check([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(this.check([208,207,17,224,161,177,26,225]))return{ext:"cfb",mime:"application/x-cfb"};if(await n.peekBuffer(this.buffer,{length:Math.min(256,n.fileInfo.size),mayBeLess:!0}),this.checkString("BEGIN:")){if(this.checkString("VCARD",{offset:6}))return{ext:"vcf",mime:"text/vcard"};if(this.checkString("VCALENDAR",{offset:6}))return{ext:"ics",mime:"text/calendar"}}if(this.checkString("FUJIFILMCCD-RAW"))return{ext:"raf",mime:"image/x-fujifilm-raf"};if(this.checkString("Extended Module:"))return{ext:"xm",mime:"audio/x-xm"};if(this.checkString("Creative Voice File"))return{ext:"voc",mime:"audio/x-voc"};if(this.check([4,0,0,0])&&16<=this.buffer.length){var p=this.buffer.readUInt32LE(12);if(12<p&&this.buffer.length>=p+16)try{var l=this.buffer.slice(16,p+16).toString();if(JSON.parse(l).files)return{ext:"asar",mime:"application/x-asar"}}catch(e){console.log(e)}}if(this.check([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(this.checkString("SCRM",{offset:44}))return{ext:"s3m",mime:"audio/x-s3m"};if(this.check([71])&&this.check([71],{offset:188}))return{ext:"mts",mime:"video/mp2t"};if(this.check([71],{offset:4})&&this.check([71],{offset:196}))return{ext:"mts",mime:"video/mp2t"};if(this.check([66,79,79,75,77,79,66,73],{offset:60}))return{ext:"mobi",mime:"application/x-mobipocket-ebook"};if(this.check([68,73,67,77],{offset:128}))return{ext:"dcm",mime:"application/dicom"};if(this.check([76,0,0,0,1,20,2,0,0,0,0,0,192,0,0,0,0,0,0,70]))return{ext:"lnk",mime:"application/x.ms.shortcut"};if(this.check([98,111,111,107,0,0,0,0,109,97,114,107,0,0,0,0]))return{ext:"alias",mime:"application/x.apple.alias"};if(this.check([76,80],{offset:34})&&(this.check([0,0,1],{offset:8})||this.check([1,0,2],{offset:8})||this.check([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(this.check([6,6,237,245,216,29,70,229,189,49,239,231,254,116,183,29]))return{ext:"indd",mime:"application/x-indesign"};if(await n.peekBuffer(this.buffer,{length:Math.min(512,n.fileInfo.size),mayBeLess:!0}),KrajeeFileTypeConfig.tarHeaderChecksumMatches(this.buffer))return{ext:"tar",mime:"application/x-tar"};if(this.check([255,254,255,14,83,0,107,0,101,0,116,0,99,0,104,0,85,0,112,0,32,0,77,0,111,0,100,0,101,0,108,0]))return{ext:"skp",mime:"application/vnd.sketchup.skp"};if(this.checkString("-----BEGIN PGP MESSAGE-----"))return{ext:"pgp",mime:"application/pgp-encrypted"};if(2<=this.buffer.length&&this.check([255,224],{offset:0,mask:[255,224]})){if(this.check([16],{offset:1,mask:[22]}))return this.check([8],{offset:1,mask:[8]}),{ext:"aac",mime:"audio/aac"};if(this.check([2],{offset:1,mask:[6]}))return{ext:"mp3",mime:"audio/mpeg"};if(this.check([4],{offset:1,mask:[6]}))return{ext:"mp2",mime:"audio/mpeg"};if(this.check([6],{offset:1,mask:[6]}))return{ext:"mp1",mime:"audio/mpeg"}}return{}}},{key:"readTiffTag",value:async function(e){var t=KrajeeFileTypeConfig.Token,i=null;try{i=await this.tokenizer.readToken(e?t.UINT16_BE:t.UINT16_LE)}catch(e){if(_instanceof(e,EndOfStreamError))return null;throw e}switch(this.tokenizer.ignore(10),i){case 50341:return{ext:"arw",mime:"image/x-sony-arw"};case 50706:return{ext:"dng",mime:"image/x-adobe-dng"};default:return null}}},{key:"readTiffIFD",value:async function(e){for(var t=KrajeeFileTypeConfig.Token,i=await this.tokenizer.readToken(e?t.UINT16_BE:t.UINT16_LE),r=0;r<i;++r){var n=await this.readTiffTag(e);if(n)return n}return null}},{key:"readTiffHeader",value:async function(e){var t=KrajeeFileTypeConfig.Token,i=(e?t.UINT16_BE:t.UINT16_LE).get(this.buffer,2),e=(e?t.UINT32_BE:t.UINT32_LE).get(this.buffer,4),t={ext:"tif",mime:"image/tiff"};if(42===i){if(6<=e){if(this.checkString("CR",{offset:8}))return{ext:"cr2",mime:"image/x-canon-cr2"};if(8<=e&&(this.check([28,0,254,0],{offset:8})||this.check([31,0,11,0],{offset:8})))return{ext:"nef",mime:"image/x-nikon-nef"}}await this.tokenizer.ignore(e);e=await this.readTiffIFD(!1);return e||t}if(43===i)return t}}]),e}();