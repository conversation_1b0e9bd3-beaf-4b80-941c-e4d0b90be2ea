@extends('layouts.web')

@section('title', '服务器错误')

@section('content')
<div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
  <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-800 mb-4">500</h1>
      <h2 class="text-xl font-semibold text-gray-700 mb-4">服务器错误</h2>
      <div class="text-gray-600 mb-6">
        <p>抱歉，服务器遇到了一个错误。</p>
        <p>我们正在努力修复这个问题，请稍后再试。</p>
      </div>

      <div class="py-5 text-center">
        <svg style="width: 4rem; height: 4rem;" class="d-inline-block text-danger" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4 mt-6">
        <a href="{{ route('home') }}" class="btn btn-primary px-4 py-2 transition">
          返回首页
        </a>
        <button onclick="location.reload()" class="btn btn-outline-primary px-4 py-2 transition">
          刷新页面
        </button>
      </div>
    </div>
  </div>
</div>
@endsection
