<?php

namespace App\Console\Commands\Cron;

use \Exception;
use App\Models\NursingHome;
use Illuminate\Console\Command;
use AlibabaCloud\Tea\Utils\Utils;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Mts\V20140618\Mts;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\SDK\Mts\V20140618\Models\QuerySnapshotJobListRequest;

class NursingHomeMpsSnapListJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:nursing-home-mps-snaplistjob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '媒体处理 MPS 截图任务查询';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始执行截图任务查询');

        $oss_url = 'https://' . getenv("OSS_DOMAIN") . '/';
        $client = self::createClient();
        $listJobRequest = new QuerySnapshotJobListRequest([
            "state"                      => "TranscodeSuccess",
            "maximumPageSize"            => "100",
            // "startOfJobCreatedTimeRange" => "2024-10-30T00:00:00Z",
            // "endOfJobCreatedTimeRange"   => "2024-10-31T00:00:00Z"
        ]);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $client->querySnapshotJobListWithOptions($listJobRequest, $runtime);
            // dd($response);

            $jobs = $response->body->snapshotJobList->snapshotJob;
            // dd($jobs);

            if (!$jobs)
                return;

            foreach ($jobs as $job) {
                // dump($job);
                $jobId = $job->id;
                // dump($jobId);

                $jobStatus = $job->state;
                if ($jobStatus == "Success") {
                    $jobInputUrl = $job->input->object;
                    $jobOutputUrl = $job->snapshotConfig->outputFile->object;

                    // dump($jobInputUrl);
                    // dump($jobOutputUrl);

                    $video_url = $oss_url . urldecode($jobInputUrl);
                    // 根据链接查询机构
                    $post = NursingHome::where('upload_video_url', $video_url)
                        ->whereNull('video_thumb_url')
                        ->first();

                    if ($post) {
                        $this->info('截图任务' . $post->id . ' 完成');
                        // 更新机构的poster_url
                        $post->video_thumb_url = $oss_url . urldecode($jobOutputUrl);
                        $post->save();
                    }
                }
            }
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }

        return 0;
    }

    /**
     * 使用AK&SK初始化账号Client
     * @return Mts Client
     */
    public static function createClient()
    {
        // 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考。
        // 建议使用更安全的 STS 方式，更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/311677.html。
        $config = new Config([
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
            "accessKeyId" => getenv("OSS_ACCESS_KEY_ID"),
            // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
            "accessKeySecret" => getenv("OSS_ACCESS_KEY_SECRET")
        ]);

        // Endpoint 请参考 https://api.aliyun.com/product/Mts
        $config->endpoint = "mts.cn-shanghai.aliyuncs.com"; // 华东2（上海）
        return new Mts($config);
    }
}
