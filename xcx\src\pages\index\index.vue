<template>
  <!-- 页面说明：我的养老院 + 机构主页 -->
  <view class="page-content">
    <bind-phone-modal />

    <!-- 当前用户设定的机构未过期，显示机构视频 -->
    <y-video-slide
      ref="videoSlide"
      v-if="list.length > 0 && user.is_nursing_home_valid !== 0"
      video-height="calc(100vh - 20px)"
      :data="list"
      :videoIndex.sync="currentIndex"
      @refresh="refresh"
      @loadMore="loadMore"
      @share="share"
      @fabulous="fabulous"
      @follow="follow"
      @collect="collect"
      @commentFabulous="commentFabulous"
    >
    </y-video-slide>
    <view class="prompt-text-wrap" v-else>
      <!-- 当前用户设定的机构已过期，显示提示 -->
      <u-empty
        icon-size="300"
        icon="/static/images/icons/empty.png"
        text="当前机构审核中或已过期"
        margin-top="200"
        v-if="user.is_nursing_home_valid == 0"
      ></u-empty>
      <text class="prompt-text" v-else>{{ promptText }}</text>
    </view>

    <!-- 顶部导航 -->
    <view class="tab-wrap pt-160 px-50">
      <view class="custom-tabs">
        <!-- 左侧logo -->
        <view class="tab-left logo-wrap">
          <image
            src="@/static/images/logo.png"
            style="width: 76rpx; height: 76rpx"
          />
        </view>

        <!-- tab内容 -->
        <view class="tab-content pl-30 pb-20">
          <view
            class="tab-item"
            :class="tabCurrent === 0 ? 'tab-active' : ''"
            @click="switchToMyNursingHome"
          >
            我的养老院
          </view>
          <view
            class="tab-item"
            :class="tabCurrent === 1 ? 'tab-active' : ''"
            @click="switchToInstitutionHome"
          >
            机构主页
          </view>
        </view>

        <!-- 右侧用户图标 -->
        <view class="tab-right" @tap="userIndex">
          <u-icon name="account" size="60rpx" color="#fff"></u-icon>
        </view>
      </view>
    </view>

    <!-- 隐私协议 -->
    <privacy-popup-modal ref="privacyComponent"></privacy-popup-modal>

    <!-- 补全资料弹出 -->
    <complete-user-info
      :show="showUserInfoModal"
      @close="handleUserInfoModalClose"
      @success="handleUserInfoUpdateSuccess"
    />
  </view>
</template>

<script>
import api from "@/common/api";
import PrivacyPopupModal from "@/components/privacy-popup/privacy-popup-modal.vue";
import CompleteUserInfo from "@/components/complete-user-info/complete-user-info.vue";
export default {
  components: {
    PrivacyPopupModal,
    CompleteUserInfo,
  },
  data() {
    return {
      tabID: 4, // 4:我的养老院，6:机构主页
      tabCurrent: 0,
      promptText: "加载中...",
      list: [],
      currentPage: 1,
      totalPages: 0,
      id: 0,
      currentIndex: 0,
      type: 3,
      shareVideoInfo: {},
      user: {},
      showUserInfoModal: false,
      hasCheckedUserInfo: false,
      userNeedComplete: false,
    };
  },
  onLoad(options) {
    // 分享的参数：diary_id=21&nursing_home_id=8&share=1&from=1144
    this.diary_id = options.diary_id || 0;
    this.shareType = options.share || 0;
    this.nursing_home_id = options.nursing_home_id || "";
    this.from = options.from || "";

    // 页面初始化时，默认加载第一个tab的数据
    this.tabCurrent = 0;
    this.tabID = 4;

    if (this.shareType == 1) {
      this.tabCurrent = 2; // 不选中
      this.tabID = 7;
    }

    // 初始化用户信息
    const user = this.getUserInfo();
    if (user) {
      this.user = user;
    }
    this.checkUserInfoComplete(this.user); // 初始化状态

    // 记录分享成功 延迟2秒后执行
    setTimeout(() => {
      this.recordShareClick();
    }, 2000);
  },

  onShow() {
    if (this.$store.getters.getFollowId) {
      this.setFollow(
        this.$store.getters.getFollowId,
        this.$store.getters.getFollowStatus
      );
    }
    this.loadData();
  },

  onHide() {
    // 页面隐藏时清除浏览记录定时器
    if (this.$refs.videoSlide && this.$refs.videoSlide.clearBrowseTimer) {
      this.$refs.videoSlide.clearBrowseTimer();
    }
  },
  onUnload() {
    // 页面卸载时清除浏览记录定时器
    if (this.$refs.videoSlide && this.$refs.videoSlide.clearBrowseTimer) {
      this.$refs.videoSlide.clearBrowseTimer();
    }
  },
  methods: {
    // 检查隐私协议
    checkPrivacyAgreement() {
      if (this.$refs.privacyComponent) {
        this.$refs.privacyComponent.checkPrivacyAgreementStatus();
      }
    },
    // 切换到我的养老院tab
    switchToMyNursingHome() {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      this.tabCurrent = 0;
      this.tabID = 4;
      this.promptText =
        '想看到你家人所在机构，老人们的生活点滴，马上就点击右上角的人头像，把你家人所在的机构设定为"我的"机构，我在这里等你哦！';
      this.currentPage = 1;
      this.currentIndex = 0;
      this.list = [];
      this.loadData();
    },

    // 切换到机构主页tab
    switchToInstitutionHome() {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      const user = this.getUserInfo();

      // 如果用户有关联的养老院，直接跳转到机构详情页（不改变tab状态）
      if (user && user.user_nursing_home_id) {
        uni.navigateTo({
          url: "/pages/index/detail?id=" + user.user_nursing_home_id,
        });
      } else {
        // 如果获取不到用户信息或没有关联机构，尝试重新获取用户信息
        this.refreshUserInfo().then(() => {
          const refreshedUser = this.getUserInfo();
          if (refreshedUser && refreshedUser.user_nursing_home_id) {
            uni.navigateTo({
              url:
                "/pages/index/detail?id=" + refreshedUser.user_nursing_home_id,
            });
          } else {
            // 没有关联的养老院，切换到机构主页tab并加载数据
            this.tabCurrent = 1;
            this.tabID = 6;
            this.promptText = "暂无视频...";
            this.currentPage = 1;
            this.currentIndex = 0;
            this.list = [];
            this.loadData();
          }
        });
      }
    },

    // 获取用户信息（优先级：store > user > userInfo）
    getUserInfo() {
      const storeUser = this.$store.getters.getUser;
      const storageUser = uni.getStorageSync("user");
      const userInfo = uni.getStorageSync("userInfo");

      // 优先使用store中的数据，然后是storage，最后是userInfo
      let user = storeUser;
      if (!user || Object.keys(user).length === 0) {
        user = storageUser;
      }
      if (!user || Object.keys(user).length === 0) {
        user = userInfo;
      }
      this.user = user;

      return user;
    },

    // 刷新用户信息
    async refreshUserInfo() {
      try {
        const res = await api.getCurrentDetail();
        if (res.data.code === 200 && res.data.data) {
          this.$store.commit("SET_USER", res.data.data);
        }
      } catch (error) {
        console.error("刷新用户信息失败:", error);
      }
    },

    userIndex() {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      uni.navigateTo({
        url: "/pages/user/index",
      });
    },
    refresh() {
      this.currentPage = 1;
      this.currentIndex = 0;
      this.list = [];
      this.loadData();
    },
    loadMore() {
      if (this.totalPages > this.currentPage) {
        this.currentPage++;
        this.loadData();
      } else {
        uni.showToast({
          title: "没有更多了",
          icon: "none",
        });
      }
    },
    share(item) {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行分享操作
      if (this.userNeedComplete) return;

      this.shareVideoInfo = item;
    },
    fabulous(item) {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行点赞操作
      if (this.userNeedComplete) return;

      api
        .like({
          diary_id: item.id,
        })
        .then((res) => {
          if (res.data.code == 200) {
            item.is_liked = !item.is_liked;
            // 数量
            if (item.is_liked) {
              item.likes_number++;
            } else {
              item.likes_number--;
            }
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },
    collect(item) {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行收藏操作
      if (this.userNeedComplete) return;

      api
        .collection({
          diary_id: item.id,
        })
        .then((res) => {
          if (res.data.code == 200) {
            item.is_collected = !item.is_collected;
            // 数量
            if (item.is_collected) {
              item.favorite_number++;
            } else {
              item.favorite_number--;
            }
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },
    follow(item, flag) {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行关注操作
      if (this.userNeedComplete) return;

      api
        .follow({
          data: {
            nursing_home_id: item.nursing_home_id,
          },
          method: "POST",
        })
        .then((res) => {
          if (res.data.code == 200) {
            item.is_follow = flag;
            this.setFollow(item.nursing_home_id, flag);
          }
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        });
    },
    commentFabulous(commentId) {
      // 先检查隐私协议
      this.checkPrivacyAgreement();

      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行评论点赞操作
      if (this.userNeedComplete) return;

      api
        .commentLike({
          comment_id: commentId,
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.list[this.currentIndex].comments.forEach((comment) => {
              if (comment.id == commentId) {
                comment.is_liked = !comment.is_liked;
                if (comment.is_liked) {
                  comment.likes_number++;
                } else {
                  comment.likes_number--;
                }
              }
            });
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },
    loadData() {
      this.loading = true;
      api
        .getHomePlayList({
          page: this.currentPage,
          limit: 5,
          diary_id: this.diary_id,
          type: this.tabID,
        })
        .then((res) => {
          let result = res.data.data;
          if (result.type && result.type === "nursing_home") {
            // 跳转到详情页
            uni.navigateTo({
              url: "/pages/index/detail?id=" + result.nursing_home.id,
            });
          } else {
            this.loading = false;
            if (this.currentPage == 1) {
              this.list = res.data.data.list;
            } else {
              this.list = this.list.concat(res.data.data.list);
            }
            this.list = [
              ...new Map(this.list.map((item) => [item.id, item])).values(),
            ];
            this.totalPages = res.data.data.meta?.pagination?.total_pages || 1;
          }
        });
    },

    // 记录分享点击
    async recordShareClick() {
      try {
        // diary_id 不存在时，不记录分享点击
        if (!this.diary_id) {
          return;
        }
        const res = await api.shareClick({
          diary_id: this.diary_id,
          from_user_id: this.from,
        });
      } catch (error) {
        console.error("分享点击接口调用失败:", error);
      }
    },
    setFollow(id, status) {
      this.list.forEach((item) => {
        if (item.nursing_home_id == id) {
          item.is_follow = status;
        }
      });
      // 重置
      this.$store.commit("SET_FOLLOW_STATUS", 0);
      this.$store.commit("SET_FOLLOW_ID", 0);
    },

    // 检查用户信息是否完整
    checkUserInfoComplete(user) {
      if (!user) return;

      const hasAvatar = user.avatar_url && user.avatar_url !== "";
      const hasNickname =
        user.nickname &&
        user.nickname !== "" &&
        !user.nickname.startsWith("用户");

      if (!hasAvatar || !hasNickname) {
        // 用户信息不完整，标记需要补全
        this.userNeedComplete = true;
      }
      this.hasCheckedUserInfo = true;
    },

    // 检查并显示用户信息补全弹窗
    checkAndShowUserInfoModal() {
      const user = this.getUserInfo();
      if (user) {
        this.checkUserInfoComplete(user);
        if (this.userNeedComplete) {
          this.showUserInfoModal = true;
        }
      }
    },

    // 处理用户信息补全成功
    handleUserInfoUpdateSuccess() {
      // 刷新用户信息
      this.refreshUserInfo().then(() => {
        const refreshedUser = this.getUserInfo();
        if (refreshedUser) {
          this.user = refreshedUser;
          this.userNeedComplete = false;
        }
      });
    },

    // 处理弹窗关闭
    handleUserInfoModalClose() {
      this.showUserInfoModal = false;
    },
  },
  onShareAppMessage() {
    // 获取当前正在播放的视频信息
    const currentVideo = this.list[this.currentIndex];
    let from = this.user.id || "";
    // 调用分享接口
    if (currentVideo) {
      let path = `pages/index/index?diary_id=${currentVideo.id}&nursing_home_id=${currentVideo.nursing_home_id}&share=1&from=${from}`;
      api
        .share({
          diary_id: currentVideo.id,
        })
        .then((res) => {
          if (res.data.code === 200) {
            console.log("分享记录创建成功:", res.data.message);
            // 可以在这里添加分享成功的提示或其他逻辑
          } else {
            console.error("分享记录创建失败:", res.data.message);
          }
        })
        .catch((error) => {
          console.error("分享接口调用失败:", error);
        });
      return {
        title: currentVideo.title,
        path: path,
        imageUrl: currentVideo.cover_url,
      };
    }

    // 如果没有获取到视频信息，使用默认值
    return {
      title: "老邻日记",
      path: "/pages/index/index",
    };
  },
};
</script>

<style lang="scss">
.page-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #000;
}
.tab-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 750rpx;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0 0 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}

.custom-tabs {
  display: flex;
  align-items: center;
  width: 100%;
}

.tab-left,
.tab-right {
  flex-shrink: 0;
}

.tab-content {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.tab-item {
  font-size: 36rpx;
  color: #fff;
  margin: 0 20rpx;
  padding: 10rpx 0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.tab-item.tab-active {
  color: #f2ae1e;
  border-bottom: 4rpx solid #f2ae1e;
}

.prompt-text {
  color: #fff;
  font-size: 28rpx;
  line-height: 52rpx;
}
.prompt-text-wrap {
  padding: 250rpx 60rpx;
}
</style>
