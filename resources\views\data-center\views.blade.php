@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/data-center/user-views',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">

    <ul class="nav nav-underline mb-4" id="userDataTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="user-data-tab" data-bs-toggle="tab" data-bs-target="#userData" type="button" role="tab" aria-controls="userData" aria-selected="true">浏览数据</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab" aria-controls="statistics" aria-selected="false">统计信息</button>
      </li>
    </ul>

    @if (session('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif

    <!-- Tab Content -->
    <div class="tab-content" id="userDataTabsContent">
      <!-- 用户数据 Tab -->
      <div class="tab-pane fade show active" id="userData" role="tabpanel" aria-labelledby="user-data-tab">

        <!-- 筛选和搜索 -->
        <div class="card mb-4">
          <div class="card-body">
            @if($filteredUser || $filteredDiary || $searchUser || $searchVideo)
            <div class="alert alert-info mb-3">
              <i class="fas fa-filter me-2"></i>
              当前筛选条件：
              @if($filteredUser)
              <span class="badge bg-primary me-2">用户：{{ $filteredUser->nickname ?: '匿名用户' }}</span>
              @endif
              @if($filteredDiary)
              <span class="badge bg-success me-2">视频：{{ $filteredDiary->title }}</span>
              @endif
              @if($searchUser)
              <span class="badge bg-info me-2">搜索用户：{{ $searchUser }}</span>
              @endif
              @if($searchVideo)
              <span class="badge bg-warning me-2">搜索视频：{{ $searchVideo }}</span>
              @endif
              <a href="{{ route('data-center.views') }}" class="btn btn-sm btn-outline-secondary ms-2">清除筛选</a>
            </div>
            @endif
            <form method="GET">
              <div class="row g-3 mb-3">
                <div class="col-md-3">
                  <label class="form-label">时间范围</label>
                  <select name="date_range" class="form-select">
                    <option value="1" {{ $dateRange == '1' ? 'selected' : '' }}>今天</option>
                    <option value="7" {{ $dateRange == '7' ? 'selected' : '' }}>最近7天</option>
                    <option value="30" {{ $dateRange == '30' ? 'selected' : '' }}>最近30天</option>
                    <option value="90" {{ $dateRange == '90' ? 'selected' : '' }}>最近90天</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <label class="form-label">排序方式</label>
                  <select name="sort_by" class="form-select">
                    <option value="latest" {{ $sortBy == 'latest' ? 'selected' : '' }}>最新浏览</option>
                    <option value="duration" {{ $sortBy == 'duration' ? 'selected' : '' }}>浏览时长</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <label class="form-label">搜索用户</label>
                  <input type="text" name="search_user" class="form-control"
                    placeholder="搜索用户昵称"
                    value="{{ $searchUser }}">
                </div>
                <div class="col-md-3">
                  <label class="form-label">搜索视频</label>
                  <input type="text" name="search_video" class="form-control"
                    placeholder="搜索视频标题"
                    value="{{ $searchVideo }}">
                </div>
              </div>
              <div class="row">
                <div class="col-12 text-end">
                  <button type="submit" class="btn btn-primary">筛选</button>
                  @if($searchUser || $searchVideo)
                  <a href="{{ route('data-center.views', request()->except(['search_user', 'search_video'])) }}"
                    class="btn btn-outline-secondary ms-2">清除搜索</a>
                  @endif
                </div>
              </div>
            </form>
          </div>
        </div>


        <!-- 详细浏览记录 -->
        <div class="border-main rounded mt-1 bg-white p-3 p-lg-4">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="mb-0">详细浏览记录</h6>
              <small class="text-muted">每个用户浏览每个视频的详细记录</small>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>用户</th>
                      <th>视频标题</th>
                      <th>浏览时长</th>
                      <th>平台</th>
                      <th>浏览时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    @forelse ($browseRecords as $record)
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          @if($record->user)
                          @if($record->user->avatar_url)
                          <img src="{{ $record->user->full_avatar_url }}" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
                          @else
                          <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                            <i class="fas fa-user text-white" style="font-size: 14px;"></i>
                          </div>
                          @endif
                          <div>
                            <div class="fw-bold">
                              <a href="{{ route('data-center.views', ['user_id' => $record->user->id] + request()->except(['user_id', 'diary_id'])) }}"
                                class="text-decoration-none"
                                title="筛选此用户的浏览记录">
                                {{ $record->user->nickname ?: '匿名用户' }}
                              </a>
                            </div>
                            {{-- @if($record->user->mobile)
                                <small class="text-muted">{{ substr($record->user->mobile, 0, 3) }}****{{ substr($record->user->mobile, -4) }}</small>
                            @endif --}}
                          </div>
                          @else
                          <div class="text-muted">
                            <i class="fas fa-user-slash me-1"></i>
                            未登录用户
                          </div>
                          @endif
                        </div>
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          <div>
                            <div class="text-truncate" style="max-width: 250px;">
                              @if($record->diary)
                              <a href="{{ route('data-center.views', ['diary_id' => $record->diary->id] + request()->except(['user_id', 'diary_id'])) }}"
                                class="text-decoration-none"
                                title="筛选此视频的浏览记录">
                                {{ $record->diary->title }}
                              </a>
                              @else
                              <span class="text-muted">视频已删除</span>
                              @endif
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ $record->formatted_duration }}</span>
                      </td>
                      <td>
                        <span class="badge bg-dark">{{ $record->platform_name }}</span>
                      </td>
                      <td>
                        <div>{{ $record->created_at->format('m-d H:i') }}</div>
                        <small class="text-muted">{{ $record->created_at->diffForHumans() }}</small>
                      </td>
                    </tr>
                    @empty
                    <tr>
                      <td colspan="7" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <div>暂无浏览记录</div>
                      </td>
                    </tr>
                    @endforelse
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              @if($browseRecords->hasPages())
              <div class="d-flex justify-content-center mt-3">
                {{ $browseRecords->appends(request()->query())->links() }}
              </div>
              @endif
            </div>
          </div>
        </div>
      </div>
      <!-- 统计信息 Tab -->
      <div class="tab-pane fade" id="statistics" role="tabpanel" aria-labelledby="statistics-tab">
        <div class="mb-3 bg-white">
          <div class="row">
            <div class="col-lg-3 col-md-6">
              <div class="card border-primary">
                <div class="card-body text-center">
                  <h3 class="text-primary">{{ number_format($statistics['total_views']) }}</h3>
                  <p class="card-text">总浏览次数</p>
                  <small class="text-muted">
                    今日: {{ number_format($statistics['today_views']) }} |
                    昨日: {{ number_format($statistics['yesterday_views']) }}
                  </small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-success">
                <div class="card-body text-center">
                  <h3 class="text-success">{{ number_format($statistics['week_views']) }}</h3>
                  <p class="card-text">最近一周浏览次数</p>
                  <small class="text-muted">过去7天的浏览量</small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-info">
                <div class="card-body text-center">
                  <h3 class="text-info">{{ $statistics['total_duration'] }}</h3>
                  <p class="card-text">总时长(分钟)</p>
                  <small class="text-muted">平均: {{ $statistics['avg_duration'] }}秒</small>
                </div>
              </div>
            </div>
            <div class="col-lg-3 col-md-6">
              <div class="card border-warning">
                <div class="card-body text-center">
                  <h3 class="text-warning">{{ $statistics['week_duration'] }}</h3>
                  <p class="card-text">最近一周时长(分钟)</p>
                  <small class="text-muted">平均: {{ $statistics['week_avg_duration'] }}秒</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</div>
@endsection
