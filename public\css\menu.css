.logo {
    max-height: 50px;
    transition: all 0.3s;
}
@media (min-width: 992px) {
    .logo {
        max-height: 80px;
    }
}
.navbar-light.menu-fixed {
    background-color: rgba(255, 255, 255, 0.9);
}
.navbar-dark.menu-fixed {
    background-color: rgba(0, 0, 0, 0.9);
}
.menu-fixed .logo {
    max-height: 60px;
}
/* collapse-right 增加在nav上与navbar-collapse collapse同时使用 手机版（侧边出）样式菜单*/
@media (max-width: 992px) {
    .navbar .navbar-collapse.collapse-right {
        padding-top: 80px;
        padding-left: 20px;
        padding-right: 20px;
        height: 100% !important;
        position: fixed;
        top: 0;
        bottom: 0;
        background-color: #fff;
        right: 0;
        display: block;
        transition: all 0.3s ease-out;
        transform: translateX(100%);
    }
}
.navbar .collapse.collapse-right.show {
    right: 0;
    transition: all 0.3s ease-out;
    transform: translateX(0);
}
.navbar .navbar-toggler:focus {
  box-shadow: none;
}
/* navbar dark fix */
.navbar.navbar-dark .ddl-switch{
    color: rgba(255, 255, 255, 0.5);
}
.dropdown-menu{
    font-size: inherit;
}
.navbar-dark .dropdown-menu{
    background-color: transparent;
}
.navbar-dark .dropdown-item{
    color: rgba(255, 255, 255, 0.5);
}
@media (min-width: 992px) {
    .navbar-dark .dropdown-menu{
        background-color: rgba(0, 0, 0, 0.9);
    }

    .navbar-dark .dropdown-item{
        color: rgba(255, 255, 255, 0.9);
    }
    .navbar-dark .dropdown-item:hover,
    .navbar-dark .dropdown-item:focus {
        color: #fff;
        background-color: #000;
    }
}
@media (min-width: 992px) {
    .navbar .ddl-switch {
        display: none !important;
    }
    .dropdown-toggle::after {
        content: none;
    }
    .navbar ul li>.dropdown-menu {
        border: none;
        border-radius: 0;
        opacity: 0;
        top: 0;
        display: block;
        margin: 0;
        padding: 0;
        min-width: 160px;
        max-width: 160px;
        transition: all 0.17s ease-in-out;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transform-origin: 0 0;
        -webkit-transform-origin: 0 0;
        transform: rotateX(-90deg);
        -webkit-transform: rotateX(-90deg);
    }
    .navbar .navbar-nav li .dropdown-menu>li>a {
        font-size: 13px;
        padding: 10px 20px;
        transition: all 0.5s ease 0s;
    }
    .navbar .navbar-nav li .dropdown-menu>li:last-child>a {
        border-bottom: 0;
    }
    .navbar .navbar-nav li.dropdown:hover>.dropdown-menu {
        visibility: visible;
        opacity: 1;
        transform: rotateX(0deg);
        -webkit-transform: rotateX(0deg);
        transition: transform 0.4s, opacity 0.1s;
    }
    .navbar .navbar-collapse ul.navbar-nav>li>.dropdown-menu {
        margin-top: 10px;
        top: 100%;
    }
    .navbar .navbar-collapse>ul>li:last-child>.dropdown-menu,
    .navbar .navbar-collapse>ul>li:nth-last-child(2)>.dropdown-menu {
        left: auto;
        right: 0;
    }
    .navbar ul>li .dropdown-menu ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child>.dropdown-menu,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2)>.dropdown-menu,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul ul {
        left: auto;
    }
    .navbar ul>li>.dropdown-menu,
    .navbar ul>li .dropdown-menu ul,
    .navbar ul>li .dropdown-menu ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul ul ul {
        right: auto;
    }
    .navbar ul>li .dropdown-menu ul,
    .navbar ul>li .dropdown-menu ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul ul ul {
        left: 100%;
    }
    .navbar ul>li .dropdown-menu ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul,
    .navbar ul>li .dropdown-menu ul ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:last-child .dropdown-menu ul ul ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul,
    .navbar .navbar-collapse>ul>li:nth-last-child(2) .dropdown-menu ul ul ul ul ul ul ul ul ul {
        right: 100%;
    }
}
@media (max-width: 991px) {
    .navbar .navbar-nav>li>a {
        transition: all 1s ease 0s;
    }
    .navbar .collapse.show {
        display: block;
        overflow-y: auto;
    }
    .navbar .navbar-toggler {
        border: none;
        border-radius: 0;
        font-size: 20px;
        padding: 0;
        z-index: 1;
        outline: none;
        padding-right: 0;
        cursor: pointer;
    }
    .navbar .navbar-toggler .line {
        display: flex;
        width: 2.2rem;
        height: 2px;
        background: rgba(0, 0, 0, 0.9);
        transition: all 0.3s ease-out;
        animation: resize 1.3s ease-in-out infinite alternate !important;
        animation-duration: 1.3s !important;
        animation-timing-function: ease-in-out !important;
        animation-delay: 0s !important;
        animation-iteration-count: infinite !important;
        animation-direction: alternate !important;
        animation-fill-mode: none !important;
        animation-play-state: running !important;
        animation-name: resize !important;
    }
    .navbar.navbar-dark .navbar-toggler .line {
        background: rgba(255, 255, 255, 0.9);
    }
    .navbar .navbar-toggler .second-line {
        margin: 0.5rem 0;
    }
    .navbar-toggler[aria-expanded=true] .first-line {
        transform: rotate(25deg) translate3d(0.5rem, 0.5rem, 0);
        transform-origin: center;
    }
    .navbar-toggler[aria-expanded=true] .second-line {
        transform: scale3d(0, 1, 1);
    }
    .navbar-toggler[aria-expanded=true] .third-line {
        transform: rotate(-25deg) translate3d(0.5rem, -0.5rem, 0);
        transform-origin: center;
    }
    .navbar .ddl-switch {
        cursor: pointer;
        font-size: 18px;
        padding: 2px 6px;
        position: absolute;
        right: 0;
        top: 9px;
        z-index: 100;
    }
    .navbar .ddl-active>.ddl-switch:before {
        content: "\f106";
    }
    .navbar .navbar-nav>li.dropdown .dropdown-toggle::after {
        display: none;
    }
    .navbar .dropdown-menu {
        border: 0;
    }
    .navbar .navbar-nav>li:last-of-type {
        margin-bottom: 15px;
    }
}
/* 弹出搜索，搜索模式为输入框，可以将此代码注释 */
.search-fullscreen-wrapper {
  position: fixed;
  background: rgba(0, 0, 0, 0.80);
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #ffffff;
  z-index: 10000;
  display: none;
}
.search-fullscreen-wrapper .search-fullscreen-form {
  margin: 0 auto;
  margin-top: 300px;
  display: table;
}
.search-close-btn {
  position: absolute;
  top: 50px;
  right: 50px;
  cursor: pointer;
}
@media (min-width: 992px) {
  .search-close-btn {
    top: 100px;
    right: 100px;
  }
}
.search-fullscreen-wrapper .search-fullscreen-form input[type="search"] {
  font-size: 20px;
  line-height: 35px;
  height: auto;
  color: #ffffff;
  background: none;
  border: none;
  border-bottom: 3px solid #ffffff;
  padding-left: 10px;
  padding-right: 0;
  width: 700px;
  outline: none;
}
.search-fullscreen-wrapper .search-fullscreen-form #searchsubmit {
  display: none;
}
.search-fullscreen-wrapper .search-fullscreen-form input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}
.search-close-btn i {
  font-size: 28px;
}
.input-icon-addon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    color: #666;
    pointer-events: none;
}
.input-icon .form-control:not(:first-child),
.input-icon .form-select:not(:last-child) {
    padding-left: 2.5rem;
}
/******
 ****** Fixed siderbar
 ******/
.fixed-siderbar {
    position: fixed;
    top: 60%;
    right: 10px;
    width: 40px;
    z-index: 99;
}
.fixed-siderbar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}
.fixed-siderbar-menu li {
    cursor: pointer;
    margin-bottom: 10px;
}
@media (min-width: 768px) {
    .fixed-siderbar {
        right: 30px;
        width: 50px;
    }
}
@media (min-width: 1200px) {
    .fixed-siderbar {
        right: 120px;
        width: 65px;
    }
}

/******
 ****** Footer
 ******/
.footer-dark {
    background-color: #222;
}
.footer-dark,
.footer-dark p,
.footer-dark a {
    color: rgba(255, 255, 255, 0.8);
}
.footer-dark a:hover {
    color: rgba(255, 255, 255, 1);
}
.footer-light {
    background-color: #f2f3f7;
}
.footer-light,
.footer-light p,
.footer-light a {
    color: #222;
}
.footer-light a:hover {
    color: rgba(0, 0, 0, 0.8);
}
.back-to-top {
    display: none;
    cursor: pointer;
    position: fixed;
    bottom: 0;
    right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
    color: #fff;
    border-radius: 8px 8px 0 0;
    height: 48px;
    width: 48px;
    font-size: 12px;
}
.back-to-top:hover,
.back-to-top:focus {
    background-color: #0055c0;
}
