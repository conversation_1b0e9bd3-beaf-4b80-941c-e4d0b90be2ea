<?php

namespace App\Admin\Actions\Form;

use App\Models\Payment;
use App\Models\NursingHome;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Traits\LazyWidget;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Contracts\LazyRenderable;

class SetExpireAt extends Form implements LazyRenderable
{

    use LazyWidget; // 使用异步加载功能


    // 处理表单提交请求
    public function handle(array $input)
    {

        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        // 表单参数
        $expired_at = $input['expired_at'] ?? null;

        if (! $id || ! $expired_at) {
            return $this->response()->error('参数错误');
        }

        $payment = Payment::query()->find($id);


        try {
            // 更新养老机构的到期时间
            $nursingHome = NursingHome::findOrFail($payment->nursing_home_id);
            $nursingHome->expired_at = $expired_at;
            $nursingHome->save();

            return $this->response()->success('设置成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('设置失败: ' . $e->getMessage());
        }
    }

    // 构建表单
    public function form()
    {
        // $this->confirm('您确定要设置到期时间吗？', '设置后将更新养老机构的到期时间');

        $this->date('expired_at', '机构到期时间')
            ->required();
    }

}
