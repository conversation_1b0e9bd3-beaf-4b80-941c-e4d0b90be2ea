<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CommentReplyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'          => $this->id,
            'nickname'    => optional($this->user)->nickname,
            'avatar_url'  => optional($this->user)->full_avatar_url,
            'content'     => $this->content,
            'created_at'  => optional($this->created_at)->toDateTimeString(),
            'created_at_' => optional($this->created_at)->format('m-d'),
        ];
    }
}
