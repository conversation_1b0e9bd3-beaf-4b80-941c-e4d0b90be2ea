(function ($) {
  "use strict"; // Start of use strict

  var mySwiper = new Swiper(".swiper-slider", {
    direction: "horizontal", // 垂直切换选项
    width: window.innerWidth,
    loop: true,

    // 如果需要分页器
    pagination: {
      el: ".swiper-pagination",
    },

    // 如果需要前进后退按钮
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
  });
})(jQuery); // End of use strict

jQuery(document).ready(function () {
  "use strict";

  /**
   * 顶部导航栏固定
   */
  jQuery(window).bind("scroll", function () {
    // console.log("scrollTop:" + jQuery(this).scrollTop());
    let bodyWidth = document.body.clientWidth;
    let navbar = jQuery("body").find(".navbar");
    let navbarHeight = jQuery(".navbar").height();
    if (jQuery(this).scrollTop() > navbarHeight && bodyWidth > 0) {
      navbar.addClass("menu-fixed fixed-top bg-white");
      jQuery("#site-content").css({ "padding-top": navbarHeight });
    } else {
      navbar.removeClass("menu-fixed fixed-top bg-white");
      jQuery("#site-content").css({ "padding-top": 0 });
    }
  });

  /**
   * Anchor
   * 预设的锚点以及动画，可以在链接上带上anchor类，即会滚动至对应的href的section位置：
   */
  jQuery(".anchor a, a.anchor").click(function () {
    if (jQuery("." + jQuery(this).prop("href").split("#")[1]).offset()) {
      jQuery("html, body").animate(
        {
          scrollTop: jQuery(
            "." + jQuery(this).prop("href").split("#")[1]
          ).offset().top,
        },
        500
      );
    } else if (jQuery("#" + jQuery(this).prop("href").split("#")[1]).offset()) {
      jQuery("html, body").animate(
        {
          scrollTop: jQuery(
            "#" + jQuery(this).prop("href").split("#")[1]
          ).offset().top,
        },
        500
      );
    }
  });

  jQuery(function hashAnchor() {
    if (window.location.hash) {
      if (jQuery("." + window.location.hash.split("#")[1]).offset()) {
        jQuery("html, body").animate(
          {
            scrollTop: jQuery("." + window.location.hash.split("#")[1]).offset()
              .top,
          },
          500
        );
      } else if (jQuery(window.location.hash).offset()) {
        jQuery("html, body").animate(
          {
            scrollTop: jQuery(window.location.hash).offset().top,
          },
          500
        );
      }
    }
  });

  /**
   * Back To Top
   * 点击以后回到顶部
   */
  jQuery(".back-to-top").click(function (e) {
    e.preventDefault();
    jQuery("html, body").animate(
      {
        scrollTop: 0,
      },
      500
    );
    return false;
  });

  /**
   * Swiper Gallery
   * 相册的轮播
   */
  var galleryThumbs = new Swiper(".gallery-thumbs", {
    spaceBetween: 10,
    slidesPerView: 4,
    freeMode: true,
    watchSlidesVisibility: true,
    watchSlidesProgress: true,
  });
  var galleryTop = new Swiper(".gallery-top", {
    spaceBetween: 10,
    navigation: {
      nextEl: ".swiper-button-next",
      prevEl: ".swiper-button-prev",
    },
    thumbs: {
      swiper: galleryThumbs,
    },
  });

  /**
   * GLightbox
   * Params 'data-glightbox' 'data-gallery'
   *激活Glightbox
   */
  var lightbox = GLightbox();

  /**
   * Menu
   *用于小屏时点击箭头打开菜单时改变箭头的方向
   */
  jQuery(".ddl-switch").on("click", function () {
    var li = jQuery(this).parent();
    if (
      li.hasClass("ddl-active") ||
      li.find(".ddl-active").length !== 0 ||
      li.find(".dropdown-menu").is(":visible")
    ) {
      li.removeClass("ddl-active");
      li.children().find(".ddl-active").removeClass("ddl-active");
      li.children(".dropdown-menu").slideUp();
    } else {
      li.addClass("ddl-active");
      li.children(".dropdown-menu").slideDown();
    }
  });
  jQuery(document).off("click.bs.dropdown.data-api");

  // 初始化wow
  // var wow = new WOW({
  //   boxClass: "wow", // animated element css class (default is wow)
  //   animateClass: "animate__animated", // animation css class (default is animated)
  //   offset: 0, // distance to the element when triggering the animation (default is 0)
  //   mobile: true, // trigger animations on mobile devices (default is true)
  //   live: true, // act on asynchronously loaded content (default is true)
  //   callback: function (box) {
  //     // the callback is fired every time an animation is started
  //     // the argument that is passed in is the DOM node being animated
  //   },
  //   scrollContainer: null, // optional scroll container selector, otherwise use window,
  //   resetAnimation: true, // reset animation on end (default is true)
  // });
  // wow.init();
});

/**
 * Menu
 * 窗口宽度大于991不显示箭头和样式小于991时不显示样式
 */
jQuery(window).resize(function () {
  "use strict";
  var width = jQuery(window).width();
  if (jQuery(".ownavigation .navbar-nav li.ddl-active").length) {
    if (width > 991) {
      jQuery(".ownavigation .navbar-nav > li").removeClass("ddl-active");
      jQuery(".ownavigation .navbar-nav li .dropdown-menu").removeAttr("style");
    }
  } else {
    jQuery(".ownavigation .navbar-nav li .dropdown-menu").removeAttr("style");
  }
});

//Animation动画库，滚动至动画区域并激活
jQuery(document).ready(function () {
  jQuery(window).on("scroll", function () {
    var scrollHeight = jQuery(this).scrollTop();
    jQuery(".animate__trigger").each(function () {
      var windowHeight = jQuery(window).height(),
        sectionsOffest = jQuery(this).offset().top;
      sectionsTop = sectionsOffest - windowHeight + 200; /* 动画区域位置修正 */
      if (scrollHeight >= sectionsTop || scrollHeight >= sectionsOffest) {
        jQuery(this).addClass("animate_active");
      }
    });
  });
});

// 内容复制
// HTML代码示例：<span class="copy-enabled" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-custom-class="copy-tooltip" data-bs-title="已复制" data-bs-trigger="click">内容<i class="bi bi-copy"></i></span>
jQuery(document).ready(function ($) {
  $(".copy-enabled").click(function () {
    var tempInput = document.createElement("input");
    document.body.appendChild(tempInput);
    tempInput.value = $(this).text();
    tempInput.select();
    document.execCommand("copy");
    document.body.removeChild(tempInput);

    $(".copy-enabled").removeClass("copy-success");
    $(this).addClass("copy-success");
    $(".copy-enabled").tooltip("hide");
    $(this).tooltip("show");
  });
});

// 全局Toastr配置和工具函数
jQuery(document).ready(function ($) {

  // 全局Toastr配置
  if (typeof toastr !== 'undefined') {
    toastr.options = {
      "closeButton": true,
      "debug": false,
      "newestOnTop": true,
      "progressBar": true,
      "positionClass": "toast-top-right",
      "preventDuplicates": false,
      "onclick": null,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "5000",
      "extendedTimeOut": "1000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    };
  }
});
