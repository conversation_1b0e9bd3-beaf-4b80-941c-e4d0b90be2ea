<?php

namespace App\Admin\Exports;

use App\Admin\Repositories\Common;
use App\Models\NewsCategory as CategoryModels;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;

class NewsCategoryExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable, RegistersEventListeners;

    public $filter;

    public function __construct($filter = [])
    {
        $this->filter = $filter;
    }

    public function query()
    {
        return CategoryModels::when(isset($this->filter['status']), function ($query) {
            return $query->where('status', $this->filter['status']);
        });
    }

    public function headings(): array
    {
        return [
            [
                'id'           => '序号',
                'title'        => '标题',
                'sort_order'   => '排序',
                'status'       => '状态',
            ]
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->title,
            $row->sort_order,
            Common::$activeMap[$row->status],
        ];
    }
}
