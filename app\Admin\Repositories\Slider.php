<?php

namespace App\Admin\Repositories;

use App\Models\Slider as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Slider extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 位置：1=>居左，2=>居中，3=>居右
     */
    const POSITION_LEFT   = 1;
    const POSITION_CENTER = 2;
    const POSITION_RIGHT  = 3;
    public static $positionMap = [
        self:: POSITION_LEFT    => '居左',
        self:: POSITION_CENTER  => '居中',
        self:: POSITION_RIGHT   => '居右',
    ];

    /**
     * 明暗显示：0=>暗，1=>明
     */
    const SHADE_DARK  = 0;
    const SHADE_LIGHT = 1;
    public static $shadeMap = [
        self:: SHADE_DARK    => '暗',
        self:: SHADE_LIGHT   => '明',
    ];

}
