<?php

namespace App\Services;

use OSS\OssClient;
use OSS\Core\OssException;
use Illuminate\Support\Str;

class OssService
{
    protected $accessKeyId;
    protected $accessKeySecret;
    protected $bucket;
    protected $endpoint;
    protected $domain;

    public function __construct()
    {
        $this->accessKeyId = config('filesystems.disks.oss.access_key_id');
        $this->accessKeySecret = config('filesystems.disks.oss.access_key_secret');
        $this->bucket = config('filesystems.disks.oss.bucket');
        $this->endpoint = config('filesystems.disks.oss.endpoint');
        $this->domain = config('filesystems.disks.oss.domain');
    }

    public function getPresignedUrl(string $object, int $expires = 3600)
    {
        try {
            $ossClient = new OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint);
            $signedUrl = $ossClient->signUrl($this->bucket, $object, $expires, 'GET');

            return $signedUrl;
        } catch (OssException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function listObjectsInFolder(string $folderPath, int $maxKeys = 100)
    {
        try {
            $ossClient = new OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint);
            $options = [
                'delimiter' => '/',
                'prefix' => $folderPath,
                'max-keys' => $maxKeys,
            ];
            $objectList = $ossClient->listObjects($this->bucket, $options);

            return $objectList;
        } catch (OssException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function getUploadPolicy($dir = '', $expire = 3600)
    {
        try {
            // 确保文件夹存在
            $this->createVirtualDirectory($dir);

            $now = time();
            $expireTime = $now + $expire;
            $host = 'https://' . $this->bucket . '.' . $this->endpoint;
            $dir = $dir ? rtrim($dir, '/') . '/' : '';

            // 构建 Policy
            $policy = json_encode([
                'expiration' => gmdate('Y-m-d\TH:i:s\Z', $expireTime),
                'conditions' => [
                    ['bucket' => $this->bucket],
                    ['starts-with', '$key', $dir],
                ]
            ]);

            // 对 Policy 进行 Base64 编码
            $base64Policy = base64_encode($policy);

            // 计算签名
            $signature = base64_encode(hash_hmac('sha1', $base64Policy, $this->accessKeySecret, true));

            return [
                'accessid' => $this->accessKeyId,
                'host' => $host,
                'policy' => $base64Policy,
                'signature' => $signature,
                'expire' => $expireTime,
                'callback' => '', // 如果有回调函数，可以在这里设置
                'dir' => $dir,
                'url' => 'https://' . $this->domain . '/' . $dir
            ];
        } catch (OssException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private function createVirtualDirectory($dir)
    {
        if (empty($dir)) {
            return;
        }

        $dir = rtrim($dir, '/') . '/';

        try {
            $ossClient = new OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint);
            $ossClient->putObject($this->bucket, $dir, '');
        } catch (OssException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 后台只传获取配置
     */
    public function adminGetUploadPolicy($extension = 'mp4')
    {
        // 获取oss配置
        // $dir = "temp/" . date("Y/m", time()); // 测试时使用temp，避免触发转码任务。测试完成删除temp文件夹内容
        $dir = "videos/" . date("Y/m", time());
        $filename = time() . '_' . Str::random(10) . '.' . $extension;
        $full = $dir . '/' . $filename;
        $params = $this->getUploadPolicy($dir, 3600);
        $policy = $params['policy'];
        $OSSAccessKeyId = $params['accessid'];
        $signature = $params['signature'];

        // 设置回调参数
        $callback = [
            'callbackUrl'      => config('app.url') . '/oss-callback',
            'callbackBody'     => '{"filename":"' . $full . '"}',
            'callbackBodyType' => 'application/json'
        ];
        $callBackBase64 = base64_encode(json_encode($callback));
        return [
            'full' => $full,
            'filename' => $filename,
            'policy' => $policy,
            'OSSAccessKeyId' => $OSSAccessKeyId,
            'signature' => $signature,
            'callBackBase64' => $callBackBase64,
        ];
    }
}
