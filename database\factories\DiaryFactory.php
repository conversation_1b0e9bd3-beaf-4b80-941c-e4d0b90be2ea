<?php

namespace Database\Factories;

use App\Models\Diary;
use App\Models\NursingHome;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Diary>
 */
class DiaryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Diary::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'nursing_home_id' => NursingHome::factory(),
            'title' => $this->faker->sentence(6),
            'cover_url' => $this->faker->imageUrl(640, 480, 'nature'),
            'cover_vertical_url' => $this->faker->imageUrl(400, 640, 'nature'),
            'upload_video_url' => 'https://example.com/video.mp4',
            'video_url' => 'https://example.com/video.mp4',
            'likes_number' => $this->faker->numberBetween(0, 1000),
            'comment_number' => $this->faker->numberBetween(0, 100),
            'browse_number' => $this->faker->numberBetween(0, 5000),
            'favorite_number' => $this->faker->numberBetween(0, 500),
            'share_number' => $this->faker->numberBetween(0, 200),
            'is_recommend' => $this->faker->boolean(20), // 20% 概率推荐
            'publish_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'status' => 3, // 默认已发布
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * 表示日记为草稿状态
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
            'publish_at' => null,
        ]);
    }

    /**
     * 表示日记为已发布状态
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 3,
            'publish_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ]);
    }

    /**
     * 表示日记为推荐状态
     */
    public function recommended(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_recommend' => true,
        ]);
    }

    /**
     * 指定特定的养老院
     */
    public function forNursingHome(NursingHome $nursingHome): static
    {
        return $this->state(fn (array $attributes) => [
            'nursing_home_id' => $nursingHome->id,
        ]);
    }

    /**
     * 高人气日记
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'likes_number' => $this->faker->numberBetween(500, 2000),
            'comment_number' => $this->faker->numberBetween(50, 200),
            'browse_number' => $this->faker->numberBetween(2000, 10000),
            'favorite_number' => $this->faker->numberBetween(200, 1000),
            'share_number' => $this->faker->numberBetween(100, 500),
        ]);
    }
}
