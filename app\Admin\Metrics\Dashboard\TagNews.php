<?php

namespace App\Admin\Metrics\Dashboard;

use App\Models\News;
use App\Models\NewsTag;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Metrics\Round;

class TagNews extends Round
{
    /**
     * 初始化卡片内容
     */
    protected function init()
    {
        parent::init();

        $this->title('新闻标签统计');
        $news_tags = NewsTag::where('status', 1)->get()->pluck('title')->toArray();
        $this->chartLabels($news_tags);
        $this->dropdown([
            '7' => '近一周',
            '30' => '近一月',
            '365' => '近一年',
        ]);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return mixed|void
     */
    public function handle(Request $request)
    {
        $news_tags = NewsTag::where('status', 1)->get();
        $global_count = News::where('status', 1);

        $total = 0;

        switch ($request->get('option')) {
            case '365':
                $total = $global_count->whereBetween('created_at', [now()->subYear(), now()])->count();
                $card_content = [];
                $card_data = [];
                foreach ($news_tags as $value) {
                    $tag_news_count = $value->news()->whereBetween('news.created_at', [now()->subYear(), now()])->count();
                    $card_content[] = [
                        'title' => $value->title,
                        'count' => $tag_news_count
                    ];
                    $card_data[] = $tag_news_count;
                }
                break;
            case '30':
                $total = $global_count->whereBetween('created_at', [now()->subMonth(), now()])->count();
                $card_content = [];
                $card_data = [];
                foreach ($news_tags as $value) {
                    $tag_news_count = $value->news()->whereBetween('news.created_at', [now()->subMonth(), now()])->count();
                    $card_content[] = [
                        'title' => $value->title,
                        'count' => $tag_news_count
                    ];
                    $card_data[] = $tag_news_count;
                }
                break;
            case '7':
            default:
                $total = $global_count->whereBetween('created_at', [now()->subWeek(), now()])->count();
                $card_content = [];
                $card_data = [];
                foreach ($news_tags as $value) {
                    $tag_news_count = $value->news()->whereBetween('news.created_at', [now()->subWeek(), now()])->count();
                    $card_content[] = [
                        'title' => $value->title,
                        'count' => $tag_news_count
                    ];
                    $card_data[] = $tag_news_count;
                }
                break;
        }
        // 卡片内容
        $this->withContent($card_content);

        // 图表数据
        $this->withChart($card_data);

        // 总数
        $this->chartTotal('总数', $total);
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data)
    {
        return $this->chart([
            'series' => $data,
        ]);
    }

    /**
     * 卡片内容.
     *
     * @param int $finished
     * @param int $pending
     * @param int $rejected
     *
     * @return $this
     */
    public function withContent($card_content)
    {
        return $this->content(view('admin.dashboard.tag-card-list', ['data' => $card_content]));
    }
}
