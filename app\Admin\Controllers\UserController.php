<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Models\NursingHome;
use App\Admin\Repositories\User;
use App\Admin\Exports\UsersExport;
use App\Admin\Repositories\Common;
use App\Models\User as UserModels;
use App\Admin\Actions\Restore\Restore;
use App\Admin\Actions\Restore\BatchRestore;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(User::with(['nursingHomes', 'sales']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('full_avatar_url', '头像')->image('', 40, 40);
            $grid->column('real_name');
            $grid->column('nickname');
            $grid->column('mobile');
            // $grid->column('full_xcx_code', '小程序码')->image('', 40, 40);
            // $grid->column('gender')->using(User::$gender)->badge();
            // $grid->column('role')->using(User::$role)->badge();
            // $grid->nursingHomes('绑定养老院')->pluck('name')->label();
            $grid->column('nursing_home_id')->using(NursingHome::pluck('name', 'id')->toArray())->badge();
            // 绑定销售
            $grid->column('sales.nickname', '绑定销售');

            $grid->column('status')->using(Common::$activeMap)->badge(Common::$statusColor);
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('mobile')->width(3);
                $filter->like('nickname')->width(3);
                $filter->like('real_name')->width(3);
                $filter->between('birthdate')->date()->width(3);
                $filter->like('province')->width(3);
                $filter->like('city')->width(3);
                $filter->like('district')->width(3);
                $filter->like('address')->width(3);
                $filter->equal('role')
                    ->radio(['' => '全部'] + User::$role)
                    ->default(1)->width(3);
                $filter->equal('status')
                    ->radio(['' => '全部'] + Common::$activeMap)
                    ->default(1)->width(3);
                $filter->between('created_at')->datetime()->width(6);
                $filter->scope('trashed', '回收站')->onlyTrashed();
            });

            // 导出
            $grid->export(new UsersExport());

            $grid->quickSearch('id', 'nickname', 'mobile')
                ->placeholder('输入用户ID、昵称、手机号')
                ->auto(false);
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if (request('_scope_') == 'trashed') {
                    $actions->append(new Restore(UserModels::class));
                }
            });
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                if (request('_scope_') == 'trashed') {
                    $batch->add(new BatchRestore(UserModels::class));
                }
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(User::with(['sales', 'nursingHomes', 'managedNursingHome']), function (Form $form) {
            $form->text('real_name')->help('真实姓名，仅后台可见。');
            $form->text('nickname');
            $form->text('mobile');
            $form->display('sales.nickname', '绑定销售');

            $form->multipleSelect('nursingHomes', '绑定的养老院')
                ->options(NursingHome::all()->pluck('name', 'id'))
                ->customFormat(function ($v) {
                    if (!$v) {
                        return [];
                    }
                    return array_column($v, 'id');
                });

            $form->select('nursing_home_id')->options(function () {
                return NursingHome::all()->pluck('name', 'id');
            });

            $form->radio('role')->options(User::$role)->when([2, 4], function (Form $form) {
                // 机构超级管理员和机构员工 绑定的养老院
                $form->select('manage_nursing_home_id')->options(function () {
                    return NursingHome::all()->pluck('name', 'id');
                });
            })
                ->when(3, function (Form $form) {
                    // 销售显示小程序码
                    $form->display('xcx_code')->customFormat(function ($v) {
                        if (!$v) {
                            return '';
                        } else {
                            $v = getImageUrl($v);
                            $images = "<img class='img-rounded' src='$v' style='width: 100px;'>";
                            return $images;
                        }
                    });
                    $form->switch('refresh_xcx_code', '刷新小程序码')
                        ->default(0)->help('仅对"销售"角色生效');
                });


            // $form->text('name');
            $form->text('email');
            $form->password('password');
            $form->image('avatar_url')
                ->disk('oss')
                ->move('images/' . date("Y/m"))
                ->help("建议尺寸：256*256")->autoUpload()->uniqueName();
            $form->radio('gender')->options(User::$gender)->default(0);
            $form->date('birthdate');
            $form->distpicker(['province', 'city', 'district'], '省市区');
            $form->text('address');

            $form->display('expired_at');
            $form->switch('status');
            $form->ignore(['password']);
            $form->saving(function (Form $form) {
                $form->deleteInput('refresh_xcx_code');
                if (request('password')) {
                    $form->password = bcrypt(request('password'));
                }
            });

            $form->saved(function (Form $form) {
                $model = $form->repository()->model();
                if (request()->refresh_xcx_code && $model->role == 3) {
                    $model->update(['xcx_code' => null]);
                    $model->xcx_code_url;
                }
            });
        });
    }
}
