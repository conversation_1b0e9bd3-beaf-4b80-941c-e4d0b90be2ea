<?php

namespace App\Http\Controllers\Api;

use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends ApiController
{
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|max:20',
            'telephone' => 'required|max:20',
            'email' => 'nullable|email',
            'message' => 'nullable|max:200',
        ], [
            'name.required' => '姓名不能为空',
            'name.max' => '姓名不能超过20个字符',
            'telephone.required' => '电话不能为空',
            'telephone.max' => '电话不能超过20个字符',
            'email.email' => '邮箱格式不正确',
            'message.max' => '留言不能超过200个字符',
        ]);

        Contact::create($data);

        return $this->ok('提交成功');
    }
}
