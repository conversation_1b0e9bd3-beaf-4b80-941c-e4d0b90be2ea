<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class NewsTag extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'news_tags';

    protected $fillable = [
        'id',
        'title',
        'status',
    ];

    public function news()
    {
        return $this->belongsToMany(News::class, 'news_with_tags', 'news_tag_id', 'news_id')->withTimestamps();
    }
}
