<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DiaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $like_number_text = $this->likes_number > 9999 ? round($this->likes_number / 10000, 1) . '万' : $this->likes_number;

        return [
            'id'                 => $this->id,
            'nursing_home_id'    => $this->nursing_home_id,
            'nursing_home'       => $this->whenLoaded('nursingHome') ? new NursingHomeShortResource($this->nursingHome): '',
            'cover_url'          => $this->cover_url,
            'cover_vertical_url' => $this->cover_vertical_url,
            'image'              => $this->cover_vertical_url ?? $this->cover_url_vertical, // 瀑布流列表图片字段的键名
            'upload_video_url'   => $this->upload_video_url,
            'video_url'          => $this->video_url,
            'likes_number'       => $this->likes_number ?? 0,
            'like_number_text'   => $like_number_text,
            'comment_number'     => $this->comment_number ?? 0,
            'browse_number'      => $this->browse_number ?? 0,
            'favorite_number'    => $this->favorite_number ?? 0,
            'share_number'       => $this->share_number ?? 0,
            'is_recommend'       => $this->is_recommend,
            'publish_at'         => $this->publish_at,
            'is_follow'          => $this->is_follow ? 1 : 0,
            'is_collected'      => $this->is_collected,
            'status'             => $this->status,
            'created_at'         => optional($this->created_at)->toDateTimeString(),
            'updated_at'         => optional($this->updated_at)->toDateTimeString(),

            // 视频配置
            'src'           => $this->upload_video_url ?? $this->video_url,
            'vodUrl'        => $this->upload_video_url ?? $this->video_url,
            'coverImg'      => $this->cover_url,
            'coverShow'     => false,
            'object_fit'    => 'contain',
            'sliderShow'    => true,
            'rotateImgShow' => false,
            'fabulousShow'  => $this->is_liked,
            'followReally'  => $this->is_follow,
            'userName'      => optional($this->nursingHome)->name,
            'title'         => $this->title,
            'userImg'       => optional($this->nursingHome)->avatar_url,
        ];
    }
}
