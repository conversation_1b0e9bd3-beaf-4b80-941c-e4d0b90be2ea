<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDiariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('diaries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('nursing_home_id')->nullable()->comment('养老院');
            $table->string('title')->nullable()->comment('标题');
            $table->string('cover_url')->nullable()->comment('封面');
            $table->string('cover_vertical_url')->nullable()->comment('封面（竖屏）');
            $table->string('upload_video_url')->nullable()->comment('视频');
            $table->string('video_url')->nullable()->comment('视频地址');
            $table->unsignedBigInteger('likes_number')->default('0')->comment('点赞量');
            $table->unsignedBigInteger('comment_number')->default('0')->comment('评论量');
            $table->unsignedBigInteger('browse_number')->default('0')->comment('浏览量');
            $table->unsignedBigInteger('favorite_number')->default('0')->comment('收藏量');
            $table->unsignedBigInteger('share_number')->default('0')->comment('分享量');
            $table->tinyInteger('is_recommend')->default('0')->comment('是否推荐');
            $table->dateTime('publish_at')->nullable()->comment('发布日期');
            $table->tinyInteger('status')->default('0')->comment('状态');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('diaries');
    }
}
