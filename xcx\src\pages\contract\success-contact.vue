<template>
  <view class="page">
    <u-no-network></u-no-network>
    <u-toast ref="uToast" />
    <u-notify ref="uNotify" />

    <view class="loading" v-if="loading">
      <u-loading-page :loading="loading"></u-loading-page>
    </view>
    <view
      class="content container u-p-t-20 u-p-b-20 u-flex-col u-row-center"
      v-else-if="showSuccess"
    >
      <!-- 签约成功信息 -->
      <view class="u-col-center u-col-center u-row-center">
        <view class="mb-10 px-60 u-font-18 u-text-center">{{ contact_title_description }}</view>
      </view>
      <view class="u-flex-col u-col-center px-60 my-40">
        <view class="u-flex u-col-center u-row-center checkmark">
          <image
            :src="customer_service_wechat_qr_code"
            mode="widthFix"
            class="qrcode-image"
            :show-menu-by-longpress="true"
          />
        </view>
      </view>
      <view class="u-p-l-80 u-p-r-80 u-m-b-20">
        <u-row justify="center" gutter="20">
          <u-col span="6">
            <u-button
              color="#F2AE1E"
              shape="circle"
              text="返回"
              @click="goBack"
            ></u-button>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/common/api'
export default {
  data() {
    return {
      loading: false,
      showSuccess: false,
      signTimeText: '',
      customer_service_wechat_qr_code: '',
      contact_title_description: '',
    };
  },
  onLoad() {
    this.loadContractData();
    this.loadConfig();
  },
  methods: {
    async loadContractData() {
      this.loading = true;
      try {
        const res = await api.getContracts();
        if (res.data.status === 'success' && res.data.data) {
          const contract = res.data.data;

          // 检查是否已签约
          if (contract.status === 1) {
            this.showSuccess = true;
            this.signTimeText = contract.sign_at;
          } else {
            // 未签约，跳回第一步
            uni.showToast({
              title: '请先完成签约',
              icon: 'none',
              duration: 1500
            });
            setTimeout(() => {
              uni.redirectTo({
                url: '/pages/contract/form'
              });
            }, 1500);
          }
        } else {
          // 没有签约信息，跳回第一步
          uni.showToast({
            title: '请先填写签约信息',
            icon: 'none',
            duration: 1500
          });
          setTimeout(() => {
            uni.redirectTo({
              url: '/pages/contract/form'
            });
          }, 1500);
        }
      } catch (error) {
        console.error('加载签约信息失败:', error);
        // 发生错误时跳回第一步
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 1500
        });
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/contract/form'
          });
        }, 1500);
      } finally {
        this.loading = false;
      }
    },

    loadConfig() {
      api.getConfig("customer_service_wechat_qr_code").then(res => {
        this.customer_service_wechat_qr_code = res.data.data.value;
      });
      api.getConfig("contact_title_description").then(res => {
        this.contact_title_description = res.data.data.value;
      });
    },

    goBack() {
      uni.navigateBack();
    },

    toUserCenter() {
      uni.navigateTo({
        url: '/pages/user/index'
      });
    },
  },
};
</script>

<style lang="scss">
.content {
  min-height: 100vh;
  background-color: #F8F8F8;
  line-height: 1.6;
}
.btn-grey {
  background-color: #f2f2f2;
  border-color: #f2f2f2;
  color: #332c2b;
}
.qrcode-image {
  width: 560upx;
}
</style>
