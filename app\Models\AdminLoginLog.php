<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdminLoginLog extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'admin_login_logs';

    protected $fillable = [
        'admin_id',
        'ip',
    ];

    public function admin()
    {
        return $this->belongsTo(config('admin.auth.providers.admin.model'), 'admin_id');
    }
}
