<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserShareRecord;
use App\Models\User;
use App\Models\Diary;
use App\Models\NursingHome;
use Carbon\Carbon;

class UserShareTestDataSeeder extends Seeder
{
    /**
     * 为用户分享页面创建测试数据
     */
    public function run(): void
    {
        $this->command->info('开始创建用户分享测试数据...');

        // 获取或创建基础数据
        $nursingHome = NursingHome::first();
        if (!$nursingHome) {
            $this->command->info('创建测试养老院...');
            $nursingHome = NursingHome::factory()->create([
                'name' => '测试养老院',
                'status' => 1,
            ]);
        }

        $users = User::where('nursing_home_id', $nursingHome->id)->take(5)->get();
        if ($users->isEmpty()) {
            $this->command->info('创建测试用户...');
            $users = User::factory()->count(5)->create([
                'nursing_home_id' => $nursingHome->id,
            ]);
        }

        $diaries = Diary::where('nursing_home_id', $nursingHome->id)->take(10)->get();
        if ($diaries->isEmpty()) {
            $this->command->info('创建测试日记...');
            $diaries = Diary::factory()->count(10)->create([
                'nursing_home_id' => $nursingHome->id,
                'status' => 3, // 已发布
            ]);
        }

        $this->command->info("使用养老院: {$nursingHome->name}");
        $this->command->info("用户数量: {$users->count()}");
        $this->command->info("日记数量: {$diaries->count()}");

        // 创建今天的分享记录
        $this->command->info('创建今天的分享记录...');
        for ($i = 0; $i < 15; $i++) {
            UserShareRecord::create([
                'from_user_id' => $users->random()->id,
                'diary_id' => $diaries->random()->id,
                'nursing_home_id' => $nursingHome->id,
                'to_user_id' => rand(0, 1) ? $users->random()->id : null, // 50%概率有点击
                'created_at' => now()->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                'updated_at' => now(),
            ]);
        }

        // 创建本月的分享记录
        $this->command->info('创建本月的分享记录...');
        for ($i = 0; $i < 30; $i++) {
            UserShareRecord::create([
                'from_user_id' => $users->random()->id,
                'diary_id' => $diaries->random()->id,
                'nursing_home_id' => $nursingHome->id,
                'to_user_id' => rand(0, 2) ? $users->random()->id : null, // 33%概率有点击
                'created_at' => now()->startOfMonth()->addDays(rand(0, now()->day - 1))->addHours(rand(0, 23)),
                'updated_at' => now(),
            ]);
        }

        // 创建上个月的分享记录
        $this->command->info('创建上个月的分享记录...');
        for ($i = 0; $i < 25; $i++) {
            $lastMonth = now()->subMonth();
            UserShareRecord::create([
                'from_user_id' => $users->random()->id,
                'diary_id' => $diaries->random()->id,
                'nursing_home_id' => $nursingHome->id,
                'to_user_id' => rand(0, 3) ? $users->random()->id : null, // 25%概率有点击
                'created_at' => $lastMonth->startOfMonth()->addDays(rand(0, $lastMonth->daysInMonth - 1))->addHours(rand(0, 23)),
                'updated_at' => now(),
            ]);
        }

        // 创建最近7天的分享记录（用于趋势图）
        $this->command->info('创建最近7天的趋势数据...');
        for ($day = 6; $day >= 0; $day--) {
            $date = now()->subDays($day);
            $count = rand(3, 12); // 每天3-12条记录

            for ($i = 0; $i < $count; $i++) {
                UserShareRecord::create([
                    'from_user_id' => $users->random()->id,
                    'diary_id' => $diaries->random()->id,
                    'nursing_home_id' => $nursingHome->id,
                    'to_user_id' => rand(0, 2) ? $users->random()->id : null,
                    'created_at' => $date->copy()->addHours(rand(8, 22))->addMinutes(rand(0, 59)),
                    'updated_at' => now(),
                ]);
            }
        }

        // 创建一些热门内容（某些日记被分享很多次）
        $this->command->info('创建热门内容数据...');
        $popularDiaries = $diaries->take(3);
        foreach ($popularDiaries as $diary) {
            $shareCount = rand(8, 20);
            for ($i = 0; $i < $shareCount; $i++) {
                UserShareRecord::create([
                    'from_user_id' => $users->random()->id,
                    'diary_id' => $diary->id,
                    'nursing_home_id' => $nursingHome->id,
                    'to_user_id' => rand(0, 1) ? $users->random()->id : null,
                    'created_at' => now()->subDays(rand(1, 30))->addHours(rand(0, 23)),
                    'updated_at' => now(),
                ]);
            }
        }

        // 创建活跃用户数据（某些用户分享很多次）
        $this->command->info('创建活跃用户数据...');
        $activeUsers = $users->take(2);
        foreach ($activeUsers as $user) {
            $shareCount = rand(10, 25);
            for ($i = 0; $i < $shareCount; $i++) {
                UserShareRecord::create([
                    'from_user_id' => $user->id,
                    'diary_id' => $diaries->random()->id,
                    'nursing_home_id' => $nursingHome->id,
                    'to_user_id' => rand(0, 2) ? $users->random()->id : null,
                    'created_at' => now()->subDays(rand(1, 30))->addHours(rand(0, 23)),
                    'updated_at' => now(),
                ]);
            }
        }

        $totalRecords = UserShareRecord::where('nursing_home_id', $nursingHome->id)->count();
        $this->command->info("✅ 测试数据创建完成！");
        $this->command->info("📊 总分享记录数: {$totalRecords}");
        $this->command->info("🏠 养老院: {$nursingHome->name}");
        $this->command->info("🌐 现在可以访问分享数据页面查看效果了！");
    }
}
