<?php

namespace App\Admin\Repositories;

use App\Models\Payment as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Payment extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 支付状态 待付款
     */
    const STATUS_PENDING = 1;
    const STATUS_PAID = 2;
    const STATUS_CLOSED = 3;

    /**
     * 激活状态集合
     */
    public static $paymentStatusMap = [
        self::STATUS_PENDING        => '待付款',
        self::STATUS_PAID           => '已付款',
        self::STATUS_CLOSED         => '已关闭',
    ];

    /**
     * 付款状态颜色集合
     */
    public static $statusColor = [
        self::STATUS_PENDING => 'danger',
        self::STATUS_PAID    => 'success',
        self::STATUS_CLOSED  => 'default',
    ];

    /**
     * 支付方式集合
     */
    public static $payMethodMap = [
        1 => '微信支付',
        2 => '支付宝',
        3 => '银行卡',
        4 => '现金',
        5 => '其他',
    ];
}
