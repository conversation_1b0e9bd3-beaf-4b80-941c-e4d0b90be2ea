<?php

namespace App\Services;

use App\Models\ApiLog;
use Guz<PERSON>Http\Client;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Exception\RequestException;
use App\Admin\Repositories\ApiLog as ApiLogRepo;

class QywxNotificationService
{
    protected $webhookUrl;

    public function __construct()
    {
        $this->webhookUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fb77d48f-97eb-45a5-8af7-b23be0c8a354";
        // $this->webhookUrl = getenv('WEIXIN_WEBHOOK_URL');
        // if (empty($this->webhookUrl)) {
        //     throw new \Exception('Webhook URL is not set in environment variables');
        // }
    }

    /**
     * 设置 Webhook URL
     *
     * @param string $webhookUrl
     */
    public function setWebhookUrl($webhookUrl)
    {
        $this->webhookUrl = $webhookUrl;
    }

    /**
     * 发送 Markdown 类型的消息
     *
     * @param string $message
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendNotification($message)
    {
        $content = "<font color=\"warning\">{$message}</font>\n
            >时间：<font color=\"comment\">" . date("Y-m-d H:i:s") . "</font>";
        $data = [
            'msgtype' => 'markdown',
            'markdown' => [
                'content' => $content,
                'mentioned_list' => ['@all'],
            ]
        ];

        return $this->sendRequest($data);
    }

    /**
     * 发送普通文本类型的消息
     *
     * @param string $message
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendTextNotification($message)
    {
        $data = [
            'msgtype' => 'text',
            'text' => [
                'content' => $message,
                'mentioned_list' => ['@all'],
            ]
        ];

        return $this->sendRequest($data);
    }

    /**
     * 发送请求到企业微信
     *
     * @param array $data
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function sendRequest($data)
    {

        // 如果是本地环境，不发送消息
        if (app()->environment('local')) {
            return;
        }

        try {
            $client = new Client();
            $response = $client->request('POST', $this->webhookUrl, [
                'json' => $data,
            ]);

            // 接口日志
            ApiLog::create([
                'user_id'            => 0,
                'request_parameters' => json_encode($data),
                'return_parameters'  => $response->getBody()->getContents(),
                'type'               => ApiLogRepo::TYPE_QYWXTZ,
                'status'             => $response->getStatusCode() == 200 ? 1 : 0,
            ]);

            return $response;
        } catch (RequestException $requestException) {
            Log::error('企业微信通知请求失败|' . $requestException->getMessage());
            throw $requestException;
        } catch (\Exception $exception) {
            Log::error('企业微信通知失败|' . $exception->getMessage());
            throw $exception;
        }
    }
}
