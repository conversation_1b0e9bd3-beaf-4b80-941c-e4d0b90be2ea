<?php

if (!function_exists('is_institution_super_admin')) {
    /**
     * 检查当前用户是否是机构超级管理员
     *
     * @return bool
     */
    function is_institution_super_admin()
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        return $user->isInstitutionSuperAdmin();
    }
}

if (!function_exists('has_institution_permission')) {
    /**
     * 检查当前用户是否具有指定权限
     *
     * @param string $permission
     * @return bool
     */
    function has_institution_permission($permission)
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        // 超级管理员拥有所有权限
        if ($user->isInstitutionSuperAdmin()) {
            return true;
        }

        $nursingHomeId = $user->manage_nursing_home_id;
        
        if (!$nursingHomeId) {
            return false;
        }

        return $user->hasInstitutionPermission($nursingHomeId, $permission);
    }
}

if (!function_exists('can_manage_staff')) {
    /**
     * 检查是否可以管理员工
     *
     * @return bool
     */
    function can_manage_staff()
    {
        return has_institution_permission('can_manage_staff');
    }
}

if (!function_exists('can_manage_info')) {
    /**
     * 检查是否可以管理信息
     *
     * @return bool
     */
    function can_manage_info()
    {
        return has_institution_permission('can_manage_info');
    }
}

if (!function_exists('can_manage_videos')) {
    /**
     * 检查是否可以管理视频
     *
     * @return bool
     */
    function can_manage_videos()
    {
        return has_institution_permission('can_manage_videos');
    }
}

if (!function_exists('can_view_data')) {
    /**
     * 检查是否可以查看数据
     *
     * @return bool
     */
    function can_view_data()
    {
        return has_institution_permission('can_view_data');
    }
}

if (!function_exists('can_manage_finance')) {
    /**
     * 检查是否可以管理财务
     *
     * @return bool
     */
    function can_manage_finance()
    {
        return has_institution_permission('can_manage_finance');
    }
}