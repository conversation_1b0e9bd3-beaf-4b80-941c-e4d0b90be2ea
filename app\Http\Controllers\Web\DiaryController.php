<?php

namespace App\Http\Controllers\Web;

use Exception;
use App\Models\Diary;
use App\Models\NursingHome;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Admin\Repositories\Diary as DiaryRepo;

class DiaryController extends Controller
{
    /**
     * 列表
     *
     * @param Request $request
     */
    public function index(Request $request)
    {
        $user            = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;
        $status          = $request->status;

        $nursing_home = NursingHome::find($nursing_home_id);

        // 查询
        $result = Diary::with('nursingHome')
            ->when(isset($status), function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->where('nursing_home_id', $nursing_home_id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        $statusAll = DiaryRepo::$statusMap;


        // 添加统计查询
        $workCount = Diary::where('nursing_home_id', $nursing_home_id)->count();
        $boundUserCount = $nursing_home->UserNursingHomes()->count();
        $fanCount = $nursing_home->userFollows()->count();

        return view('diary.list', compact('result', 'statusAll', 'workCount', 'fanCount', 'boundUserCount', 'nursing_home'));
    }

    public function detail($id, Request $request)
    {
        $user_id = $request->user()->id;
        $result = Diary::with(['nursingHome'])->findOrFail($id);
        return view('diary.detail', compact('result'));
    }

    public function add()
    {
        $params = [];
        try {
            $accessKeyId = config('filesystems.disks.oss.access_key_id');
            $accessKeySecret = config('filesystems.disks.oss.access_key_secret');
            $bucket = config('filesystems.disks.oss.bucket');
            $endpoint = config('filesystems.disks.oss.endpoint');
            $domain = config('filesystems.disks.oss.domain');

            $expire = 3600;
            $now = time();
            $expireTime = $now + $expire;
            $host = 'https://' . $bucket . '.' . $endpoint;

            $dir = 'videos';
            $dir = $dir . '/' . date("Y/m") . '/';

            // 构建 Policy
            $policy = json_encode([
                'expiration' => gmdate('Y-m-d\TH:i:s\Z', $expireTime),
                'conditions' => [
                    ['bucket' => $bucket],
                    ['starts-with', '$key', $dir],
                ]
            ]);

            // 对 Policy 进行 Base64 编码
            $base64Policy = base64_encode($policy);

            // 计算签名
            $signature = base64_encode(hash_hmac('sha1', $base64Policy, $accessKeySecret, true));

            $params = [
                'accessid' => $accessKeyId,
                'host' => $host,
                'policy' => $base64Policy,
                'signature' => $signature,
                'expire' => $expireTime,
                'callback' => '', // 如果有回调函数，可以在这里设置
                'dir' => $dir,
                'url' => 'https://' . $domain . '/' . $dir
            ];
        } catch (Exception $e) {
            // 处理异常
            return redirect()->route('diary.list')->with(['message' => '获取上传凭证失败']);
        }
        return view('diary.add', [
            'params' => $params,
        ]);
    }

    public function edit(Request $request)
    {
        $id = $request->id;
        $result = null;
        if ($id) {
            $result = Diary::with(['nursingHome'])->findOrFail($id);
        }
        return view('diary.edit', compact('result'));
    }

    /**
     * 创建
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 验证请求数据
        $input = $request->validate([
            'title'              => 'required|string|max:255',
            'uploaded_video_url' => 'required',
        ], [
            'title.required'     => '标题必填',
            'uploaded_video_url' => '视频未上传成功',
        ]);

        $data = [
            'title'            => $input['title'],
            'upload_video_url' => $input['uploaded_video_url'],
            'status'           => 1,
            'nursing_home_id'  => $nursing_home_id,
            'publish_at'       => now(),
        ];

        // 创建新记录
        $result = Diary::create($data);
        return redirect()->route('diary.list')->with(['message' => '创建成功']);
    }

    public function update(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 查找日记记录
        $result = Diary::find($request->id);
        if (!$result) {
            return redirect()->route('diary.list')->with(['message' => '数据异常']);
        }

        // 检查权限
        if ($result->nursing_home_id != $nursing_home_id) {
            return redirect()->route('diary.edit', ['id', $result->id])->with(['message' => '权限不足']);
        }

        // 验证请求数据
        $validated = $request->validate([
            'title'            => 'required|string|max:255',
            'cover_url'        => 'nullable|file|mimes:jpeg,png,jpg,gif|max:2048',   // 允许上传图片
            'status'           => 'required',
        ], [
            'title.required' => '标题必填',
            'cover_url.mimes' => '封面图片格式不正确，仅支持 jpeg, png, jpg, gif, svg',
        ]);

        $data = [
            'title'           => $validated['title'],
            'status'          => $validated['status'],
            'nursing_home_id' => $nursing_home_id,
        ];

        // 上传封面图片
        if ($request->hasFile('cover_url') && $request->file('cover_url')->isValid()) {
            $coverUrl = $this->uploadImage($request->file('cover_url'));
            $data['cover_url'] = $coverUrl;  // 返回公共访问 URL
        } elseif ($result) {
            $data['cover_url'] = $result->cover_url;  // 如果没有新上传，保留旧的封面
        }

        // 更新现有记录
        $result->update($data);
        return redirect()->route('diary.list')->with(['message' => '更新成功']);
    }

    public function del(Request $request, $id)
    {
        $user = $request->user();

        $result = Diary::where('nursing_home_id', $user->nursing_home_id)->find($id);
        // 检查权限
        if (!$result) {
            return redirect()->route('diary.list')->with(['message' => '删除失败']);
        }

        $result->delete();
        return redirect()->route('diary.list')->with(['message' => '删除成功']);
    }

    public function uploadImage($file)
    {
        $folder_name = 'cover/' . date("Y/m");
        $full_image_path = '';
        if ($file) {
            // 获取文件的后缀名，因图片从剪贴板里黏贴时后缀名为空，所以此处确保后缀一直存在
            $extension = strtolower($file->getClientOriginalExtension()) ?: 'png';
            $filename = time() . '_' . Str::random(10) . '.' . $extension;
            $image_path = $file->storeAs($folder_name, $filename, 'oss');
            $full_image_path = Storage::disk('oss')->url($image_path);
        }
        return $full_image_path;
    }
}
