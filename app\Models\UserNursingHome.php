<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class UserNursingHome extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'user_nursing_homes';
    protected $fillable = [
        'user_id',
        'nursing_home_id',
    ];

    /**
     * 关联到用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联到养老机构
     */
    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class, 'nursing_home_id');
    }
}
