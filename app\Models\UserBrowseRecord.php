<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserBrowseRecord extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'user_browse_records';

    protected $fillable = [
        'user_id',
        'diary_id',
        'nursing_home_id',
        'browse_duration',
        'ip_address',
        'user_agent',
        'device_type',
        'platform',
        'browse_started_at',
        'browse_ended_at',
    ];

    protected $casts = [
        'browse_started_at' => 'datetime',
        'browse_ended_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联日记
     */
    public function diary(): BelongsTo
    {
        return $this->belongsTo(Diary::class);
    }

    /**
     * 关联养老院
     */
    public function nursingHome(): BelongsTo
    {
        return $this->belongsTo(NursingHome::class);
    }

    /**
     * 获取浏览时长（格式化）
     */
    public function getFormattedDurationAttribute(): string
    {
        $duration = $this->browse_duration;

        if ($duration < 60) {
            return $duration . '秒';
        } elseif ($duration < 3600) {
            $minutes = floor($duration / 60);
            $seconds = $duration % 60;
            return $minutes . '分' . ($seconds > 0 ? $seconds . '秒' : '');
        } else {
            $hours = floor($duration / 3600);
            $minutes = floor(($duration % 3600) / 60);
            $seconds = $duration % 60;
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分' : '') . ($seconds > 0 ? $seconds . '秒' : '');
        }
    }

    /**
     * 获取设备类型中文名称
     */
    public function getDeviceTypeNameAttribute(): string
    {
        $deviceTypes = [
            'mobile' => '手机',
            'desktop' => '电脑',
            'tablet' => '平板',
        ];

        return $deviceTypes[$this->device_type] ?? '未知';
    }

    /**
     * 获取平台中文名称
     */
    public function getPlatformNameAttribute(): string
    {
        $platforms = [
            'iOS' => 'iOS',
            'Android' => 'Android',
            'Web' => '网页',
            'WeChat' => '微信',
            'MiniProgram' => '小程序',
        ];

        return $platforms[$this->platform] ?? '未知';
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按日记筛选
     */
    public function scopeByDiary($query, $diaryId)
    {
        return $query->where('diary_id', $diaryId);
    }

    /**
     * 作用域：按养老院筛选
     */
    public function scopeByNursingHome($query, $nursingHomeId)
    {
        return $query->where('nursing_home_id', $nursingHomeId);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：今日浏览记录
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * 作用域：本月浏览记录
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }
}
