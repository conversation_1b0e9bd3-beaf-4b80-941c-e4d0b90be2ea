<?php

namespace App\Models;

use App\Admin\Repositories\Feedback as RepositoriesFeedback;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class Feedback extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'feedbacks';

    protected $fillable = [
        'user_id',
        'email',
        'message',
        'image_urls',
        'status'
    ];

    protected $casts = [
        'image_urls' => 'array'
    ];

    public function getFullImageUrlsAttribute()
    {
        if($this->image_urls){
            return getImageUrls(json_encode($this->image_urls));
        }
        return [];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * status text
     */
    public function getStatusTextAttribute()
    {
        return RepositoriesFeedback::$statusMap[$this->status] ?? '';
    }
}
