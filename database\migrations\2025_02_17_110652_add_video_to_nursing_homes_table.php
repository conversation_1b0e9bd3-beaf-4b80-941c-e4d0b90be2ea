<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->string('province')->nullable()->comment('省份')->after('avatar_url');
            $table->string('city')->nullable()->comment('城市')->after('province');
            $table->string('district')->nullable()->comment('地区')->after('city');
            $table->string('upload_video_url')->nullable()->comment('视频')->after('image_urls');
            $table->string('video_url')->nullable()->comment('视频地址')->after('upload_video_url');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->dropColumn('upload_video_url');
            $table->dropColumn('video_url');
        });
    }
};
