<?php

namespace App\Admin\Controllers;

use App\Models\News;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Box;
use App\Models\NewsCategory;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use App\Admin\Metrics\Dashboard;
use App\Http\Controllers\Controller;
use App\Admin\Widgets\Charts\BarChart;
use App\Admin\Widgets\Charts\NewsRecommend;
use App\Admin\Widgets\Charts\NewsStatistics;

class StatisticController extends Controller
{

    public function index(Content $content)
    {
        return $content
            ->header('数据统计')
            ->body(function (Row $row) {

                // 基础卡片示例
                $row->column(4, function (Column $column) {
                    $column->row(new Dashboard\CardNewsCount());
                });
                $row->column(4, function (Column $column) {
                    $column->row(new Dashboard\NewUsers());
                });
                $row->column(4, function (Column $column) {
                    $column->row(new Dashboard\NewsCount());
                });

                // 多环形图卡片-新闻分类统计
                $row->column(6, function (Column $column) {
                    $column->row(new Dashboard\CategoryNews());
                });
                $row->column(6, function (Column $column) {
                    $column->row(new Dashboard\TagNews());
                });

                $row->column(12, function (Column $column) {
                    $column->row(Card::make('最近一周新闻推荐量统计', NewsStatistics::make()));
                });
            });
    }


    public function pie(Content $content)
    {
        $title = '饼图示例';

        return $content->title($title)
            ->description('这是一个饼图示例，用来展示数据统计')
            ->breadcrumb(['text' => $title])
            ->body(function (Row $row) {
                $bar = NewsRecommend::make()
                    ->fetching('data.month=$("#filter-month").val();$("#month").loading()')
                    ->fetched('$("#month").loading(false)')
                    ->click('#filter-btn');

                $box = Box::make(' ', $bar)
                    ->id('month')
                    ->tool(admin_view('admin.statistics.month'));

                $row->column(12, $box);
            });
    }

    public function bar(Content $content)
    {

        return $content->title('柱图示例')
            ->body(function (Row $row) {
                $bar = BarChart::make()
                    ->fetching('data.filter_date=$("#filter-date").val();$("#bar-chart").loading()')
                    ->fetched('$("#bar-chart").loading(false)')
                    ->click('#filter-btn');

                $box = Box::make(' ', $bar)
                    ->id('bar-chart')
                    ->tool(admin_view('admin.statistics.bar'));

                $row->column(12, $box);
            });
    }

    public function table(Request $request, Content $content)
    {

        $filter_date = $request->get('filter_date', date('Y-m-d'));

        $result = [];
        $news_category = NewsCategory::where('status', 1)
            ->orderBy('sort_order', 'ASC')
            ->get();

        foreach ($news_category as $category) {

            $news_count1 = News::where('is_recommend', 1)
                ->where('news_category_id', $category->id)
                ->whereDate('created_at', $filter_date)
                ->count();
            $news_count2 = News::where('is_recommend', 0)
                ->where('news_category_id', $category->id)
                ->whereDate('created_at', $filter_date)
                ->count();

            $result[$category->id][1] = $news_count1;
            $result[$category->id][2] = $news_count2;
        }

        return $content->header('表格示例')
            ->body(admin_view('admin.statistics.table', compact('result')));
    }
}
