<?php

namespace App\Admin\Repositories;

use App\Models\Feedback as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Feedback extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    const STATUS_UNREAD= 0;
    const STATUS_READ = 1;
    /**
     * 是否已读状态集合
     */
    public static $statusMap = [
        self::STATUS_UNREAD => '未读',
        self::STATUS_READ => '已读',
    ];

    /**
     * 状态颜色集合
     */
    public static $statusColor = [
        self::STATUS_UNREAD => 'danger',
        self::STATUS_READ => 'success'
    ];
}
