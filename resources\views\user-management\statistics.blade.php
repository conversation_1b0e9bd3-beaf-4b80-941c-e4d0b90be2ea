@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/data-center/user-data',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">

    <ul class="nav nav-tabs mb-4" id="userDataTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="user-data-tab" data-bs-toggle="tab" data-bs-target="#userData" type="button" role="tab" aria-controls="userData" aria-selected="true">用户数据统计</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="statistics-tab" data-bs-toggle="tab" data-bs-target="#statistics" type="button" role="tab" aria-controls="statistics" aria-selected="false">汇总统计</button>
      </li>
    </ul>

    @if (session('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif

    <!-- Tab Content -->
    <div class="tab-content" id="userDataTabsContent">
      <!-- 用户数据 Tab -->
      <div class="tab-pane fade show active" id="userData" role="tabpanel" aria-labelledby="user-data-tab">

        <!-- 筛选和搜索 -->
        <div class="card mb-4">
          <div class="card-body">
            <form method="GET">
              <div class="row g-3 mb-3">
                <div class="col-md-3">
                  <label class="form-label">时间范围</label>
                  <select name="date_range" class="form-select">
                    <option value="7" {{ $dateRange == '7' ? 'selected' : '' }}>最近7天</option>
                    <option value="30" {{ $dateRange == '30' ? 'selected' : '' }}>最近30天</option>
                    <option value="90" {{ $dateRange == '90' ? 'selected' : '' }}>最近90天</option>
                    <option value="365" {{ $dateRange == '365' ? 'selected' : '' }}>最近一年</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <label class="form-label">排序方式</label>
                  <select name="sort_by" class="form-select">
                    <option value="total_views" {{ $sortBy == 'total_views' ? 'selected' : '' }}>总浏览次数</option>
                    <option value="engagement_score" {{ $sortBy == 'engagement_score' ? 'selected' : '' }}>参与度评分</option>
                    <option value="total_interactions" {{ $sortBy == 'total_interactions' ? 'selected' : '' }}>总互动数</option>
                    <option value="total_shares" {{ $sortBy == 'total_shares' ? 'selected' : '' }}>分享次数</option>
                    <option value="total_collections" {{ $sortBy == 'total_collections' ? 'selected' : '' }}>收藏次数</option>
                    <option value="last_interaction_at" {{ $sortBy == 'last_interaction_at' ? 'selected' : '' }}>最近互动</option>
                  </select>
                </div>
                <div class="col-md-4">
                  <label class="form-label">搜索用户</label>
                  <input type="text" name="search" class="form-control" placeholder="用户名/昵称/备注名" value="{{ $search }}">
                </div>
                <div class="col-md-2">
                  <label class="form-label">&nbsp;</label>
                  <div class="d-grid">
                    <button type="submit" class="btn btn-primary">筛选</button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        <!-- 用户统计列表 -->
        <div class="border-main rounded mt-1 bg-white p-3 p-lg-4">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h6 class="mb-0">用户数据统计列表</h6>
              <small class="text-muted">基于 {{ $nursingHome->name }} 的数据统计</small>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>用户</th>
                      <th>参与度评分</th>
                      <th>浏览数据</th>
                      <th>分享数据</th>
                      <th>收藏数据</th>
                      <th>点赞数据</th>
                      <th>评论数据</th>
                      <th>总互动数</th>
                      <th>首次互动</th>
                      <th>最近互动</th>
                    </tr>
                  </thead>
                  <tbody>
                    @forelse ($paginatedUsers as $stat)
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <img src="{{ $stat['user']->full_avatar_url }}" alt="头像" class="rounded-circle me-2" width="32" height="32">
                          <div>
                            <div class="fw-bold">
                              <a href="{{ route('data-center.views') }}?user_id={{ $stat['user']->id }}" class="text-decoration-none text-dark" title="查看详细浏览数据">
                                {{ $stat['user']->nickname ?: $stat['user']->name }}
                              </a>
                            </div>
                            <small class="text-muted">{{ $stat['user_note']->nickname ?? '' }}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="badge bg-{{ $stat['engagement_score'] >= 100 ? 'danger' : ($stat['engagement_score'] >= 50 ? 'warning' : ($stat['engagement_score'] >= 20 ? 'info' : 'secondary')) }}">
                          {{ $stat['engagement_score'] }}
                        </span>
                      </td>
                      <td>
                        <div><strong>{{ number_format($stat['total_views']) }}</strong> 次</div>
                        <small class="text-muted">{{ round($stat['total_duration']/60, 1) }} 分钟</small>
                        <br>
                        <small class="text-muted">平均 {{ $stat['avg_duration'] }} 秒</small>
                      </td>
                      <td>
                        <span class="badge bg-success">{{ number_format($stat['total_shares']) }}</span>
                      </td>
                      <td>
                        <span class="badge bg-primary">{{ number_format($stat['total_collections']) }}</span>
                      </td>
                      <td>
                        <span class="badge bg-warning">{{ number_format($stat['total_likes']) }}</span>
                      </td>
                      <td>
                        <span class="badge bg-info">{{ number_format($stat['total_comments']) }}</span>
                      </td>
                      <td>
                        <strong class="text-primary">{{ number_format($stat['total_interactions']) }}</strong>
                      </td>
                      <td>
                        @if($stat['first_interaction_at'])
                        <small>{{ $stat['first_interaction_at'] }}</small>
                        @else
                        <span class="text-muted">-</span>
                        @endif
                      </td>
                      <td>
                        @if($stat['last_interaction_at'])
                        <small>{{ $stat['last_interaction_at'] }}</small>
                        <br>
                        <small class="text-muted">{{ $stat['last_interaction_at'] }}</small>
                        @else
                        <span class="text-muted">-</span>
                        @endif
                      </td>
                    </tr>
                    @empty
                    <tr>
                      <td colspan="10" class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <div>暂无用户数据</div>
                      </td>
                    </tr>
                    @endforelse
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              @if($paginatedUsers->hasPages())
              <div class="d-flex justify-content-center mt-3">
                {{ $paginatedUsers->links() }}
              </div>
              @endif
            </div>
          </div>
        </div>
      </div>

      <!-- 统计信息 Tab -->
      <div class="tab-pane fade" id="statistics" role="tabpanel" aria-labelledby="statistics-tab">
        <div class="row mb-4">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
              <div class="card-body text-center">
                <h3 class="text-primary">{{ number_format($summaryStats['total_users']) }}</h3>
                <p class="card-text">总用户数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
              <div class="card-body text-center">
                <h3 class="text-info">{{ number_format($summaryStats['total_views']) }}</h3>
                <p class="card-text">总浏览次数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
              <div class="card-body text-center">
                <h3 class="text-success">{{ number_format($summaryStats['total_shares']) }}</h3>
                <p class="card-text">总分享次数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
              <div class="card-body text-center">
                <h3 class="text-warning">{{ number_format($summaryStats['total_collections']) }}</h3>
                <p class="card-text">总收藏次数</p>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-danger">
              <div class="card-body text-center">
                <h3 class="text-danger">{{ number_format($summaryStats['total_likes']) }}</h3>
                <p class="card-text">总点赞次数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-secondary">
              <div class="card-body text-center">
                <h3 class="text-secondary">{{ number_format($summaryStats['total_comments']) }}</h3>
                <p class="card-text">总评论次数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-dark">
              <div class="card-body text-center">
                <h3 class="text-dark">{{ number_format($summaryStats['total_interactions']) }}</h3>
                <p class="card-text">总互动数</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
              <div class="card-body text-center">
                <h3 class="text-primary">{{ round($summaryStats['total_duration']/60, 1) }}</h3>
                <p class="card-text">总浏览时长(分钟)</p>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-body text-center">
                <h3 class="text-info">{{ $summaryStats['avg_engagement'] }}</h3>
                <p class="card-text">平均用户参与度评分</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
@endsection

@push('styles')
<style>
  .badge {
    font-size: 0.75em;
  }

  .table td {
    vertical-align: middle;
  }
</style>
@endpush