<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class ChinaArea extends Model
{
	use HasDateTimeFormatter;
    protected $table = 'china_areas';
    public $timestamps = false;
    protected $fillable = [
        'pid',
        'name',
        'level',
        'zip_code',
        'pinyin',
        'lat',
        'lng'
    ];

    // 关联市
    public function cities()
    {
        return $this->hasMany(self::class, 'pid', 'id');
    }

    // 关联区
    public function counties()
    {
        return $this->hasMany(self::class, 'pid', 'id');
    }

}
