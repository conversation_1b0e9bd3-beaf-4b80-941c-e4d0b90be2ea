<?php

namespace App\Admin\Widgets\Charts;

use Carbon\Carbon;
use App\Models\News;
use App\Models\NewsCategory;
use Illuminate\Http\Request;
use App\Models\ScheduleSetting;
use Dcat\Admin\Widgets\ApexCharts\Chart;

class BarChart extends Chart
{
    protected $filter_date;

    public function __construct($filter_date = null, $containerSelector = null, $options = [])
    {
        parent::__construct($containerSelector, $options);

        $this->setUpOptions();

        $this->$filter_date;
    }


    /**
     * 初始化图表配置
     */
    //初始化方法，主要是调用$this->options()方法，执行整个option的初始化操作。
    public function handle(Request $request)
    {
        $filter_date = $request->get('filter_date') ?? date('Y-m-d');

        $this->options([
            "chart" => [
                "height" => 600,
                "type"   => "bar",
                'stacked' => true,
            ],
            "fill" => [
                "opacity" => 0.8
            ],
            'plotOptions' => [
                "bar" => [
                    "horizontal" => true,
                ]
            ],
        ]);

        $datesArray = [];
        for ($i = 1; $i <= 7; $i++) {
            $startDate = Carbon::parse($filter_date)->subWeek();
            $datesArray[] = $startDate->addDay($i)->format('Y-m-d');
        }

        // 影院的场次
        $series = [];
        $news_category = NewsCategory::where('status', 1)
            ->orderBy('sort_order', 'ASC')
            ->get();

        foreach ($news_category as $category) {
            $serie = [
                'name' => $category->title,
                'data' => [],
            ];

            foreach ($datesArray as $date) {
                $order_count = News::where('status', 1)
                    ->where('news_category_id', $category->id)
                    ->whereDate('created_at', $date)
                    ->count();
                $serie['data'][] = $order_count;
            }

            $series[] = $serie;
        }

        $this->option("series", $series);
        $this->option("labels", $datesArray);
    }

    /**
     * 这里返回需要异步传递到 handle 方法的参数
     *
     * @return array
     */
    public function parameters(): array
    {
        return [
            'filter_date' => $this->filter_date,
        ];
    }
}
