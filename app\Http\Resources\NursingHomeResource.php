<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NursingHomeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $is_valid = $this->expired_at > date('Y-m-d H:i:s') ? 1 : 0;

        return [
            'id'                        => $this->id,
            'name'                      => $this->name,
            'avatar_url'                => $this->avatar_url,
            'share_url'                 => "https://diary.laoyangapp.com/m/yly/" . $this->id,
            'province'                  => $this->province ?? '',
            'city'                      => $this->city ?? '',
            'milestones'                => $this->whenLoaded('milestones') ? MilestoneResource::collection($this->milestones) : [],
            'district'                  => $this->district ?? '',
            'address'                   => $this->address ?? '',
            'longitude'                 => $this->longitude,
            'latitude'                  => $this->latitude,
            'mobile'                    => $this->mobile,
            'description'               => $this->description,
            'image_urls'                => $this->image_urls ?? [],
            'price_image_urls'          => $this->price_image_urls ?? [],
            'transportation_image_urls' => $this->transportation_image_urls ?? [],
            'honor_image_urls'          => $this->honor_image_urls ?? [],
            'other_image_urls'          => $this->other_image_urls ?? [],
            'is_follow'                 => $this->is_follow ? 1 : 0,
            'is_my'                     => $this->is_my ? 1    : 0,
            'follow_number'             => $this->follow_number ?? 0,
            'upload_video_url'          => $this->upload_video_url,
            'video_url'                 => $this->video_url,
            'video_thumb_url'           => $this->video_thumb_url,
            'status'                    => $this->status,
            'expired_at'                => optional($this->expired_at)->toDateTimeString(),
            'is_valid'                  => $is_valid,
            'created_at'                => optional($this->created_at)->toDateTimeString(),
            'updated_at'                => optional($this->updated_at)->toDateTimeString(),
        ];
    }
}
