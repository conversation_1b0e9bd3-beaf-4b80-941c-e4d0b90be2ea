<?php

namespace App\Http\Controllers\Api;

use App\Models\Slider;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\SliderResource;

class SliderController extends ApiController
{

    /**
     * 列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $limit = $request->limit ?? 5;
        $result = Slider::where('status', 1)->paginate($limit);
        return $this->success(SliderResource::collection($result));
    }

    /**
     * 详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $result = Slider::find($id);
        if ($result) {
            return $this->success(new SliderResource($result), '请求成功');
        }
        $this->errorBadRequest('暂无数据');
    }
}
