<?php

namespace App\Models;

use Illuminate\Support\Str;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Overtrue\LaravelWeChat\EasyWeChat;
use Illuminate\Notifications\Notifiable;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON>vel\Fortify\TwoFactorAuthenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sales_id',
        'xcx_code',
        'expired_at',
        'name',
        'mobile',
        'email',
        'password',
        'nickname',
        'real_name',
        'avatar_url',
        'gender',
        'birthdate',
        'province',
        'city',
        'district',
        'address',
        'role',
        'nursing_home_id',
        'manage_nursing_home_id',
        'status',
        'wx_nickname',
        'wx_avatar',
        'wx_openid',
        'xcx_openid',
        'unionid',
        'oauth_scope',
        'deleted_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    protected $casts = [
        'expired_at' => 'datetime',
    ];

    /**
     * 激活的用户
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function getFullAvatarUrlAttribute()
    {
        return getImageUrl($this->avatar_url, asset('images/placeholder-avatar.png'));
    }

    public function feedbacks()
    {
        return $this->hasMany(Feedback::class);
    }

    public function collections()
    {
        return $this->hasMany(UserCollection::class);
    }

    /**
     * 养老院用户，关联的养老院
     */
    public function nursingHome()
    {
        return $this->hasOne(NursingHome::class);
    }

    /**
     * 管理的养老院
     */
    public function managedNursingHome()
    {
        return $this->belongsTo(NursingHome::class, 'manage_nursing_home_id');
    }

    /**
     * 普通用户，绑定的养老院
     */
    public function nursingHomes()
    {
        return $this->belongsToMany(NursingHome::class, 'user_nursing_homes', 'user_id', 'nursing_home_id')
            ->withTimestamps();
    }

    public function likes()
    {
        return $this->hasMany(UserLike::class);
    }

    public function follows()
    {
        return $this->hasMany(UserFollow::class);
    }

    /**
     * 用户浏览记录
     */
    public function browseRecords()
    {
        return $this->hasMany(UserBrowseRecord::class);
    }

    /**
     * 机构对该用户的备注
     */
    public function nursingHomeNotes()
    {
        return $this->hasMany(NursingHomeUserNote::class);
    }

    // 销售
    public function sales()
    {
        return $this->hasOne(User::class, 'id', 'sales_id');
    }

    // 生成小程序二维码
    public function getXcxCodeUrlAttribute()
    {
        $id = $this->id;
        $xcx_code = $this->xcx_code;
        if ($xcx_code) {
            return getImageUrl($this->xcx_code);
        } else {

            $page = 'pages/index/index';
            $scene = 'user_id=' . $id;

            $app = EasyWeChat::miniApp();
            try {
                // 获取不限制的小程序码
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene' => $scene,
                    'page'  => $page,
                    'width' => 800,
                    'check_path' => config('app.env') == 'production', // 默认是true，检查page 是否存在，为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在， 但page 有数量上限（60000个）请勿滥用
                    'env_version' => config('app.env') == 'production' ? 'release' : 'trial', // 判断环境 production 生成正式版
                ]);
                $name = $id . now()->format('YmdHis') . Str::random(5) . '.png';
                // 判断是否有文件夹，没有则创建
                if (!file_exists(storage_path('app/public/xcxcode'))) {
                    mkdir(storage_path('app/public/xcxcode'), 0777, true);
                }
                $response->saveAs(storage_path('app/public/xcxcode/' . $name));
                $this->update(['xcx_code' => '/xcxcode/' . $name]);
                return getImageUrl($this->xcx_code);
            } catch (\Throwable $e) {
                Log::info($e->getMessage());
                return '';
            }
        }
    }

    public function getFullXcxCodeAttribute()
    {
        if ($this->xcx_code) {
            return getImageUrl($this->xcx_code);
        } else {
            return '';
        }
    }

    /**
     * Get the institution user permissions for the user.
     */
    public function institutionUserPermissions()
    {
        return $this->hasMany(InstitutionUserPermission::class);
    }

    /**
     * Check if the user is an institution super admin.
     *
     * @return bool
     */
    public function isInstitutionSuperAdmin()
    {
        return $this->role === 2; // ROLE_NURSINGHOME 机构超级管理员
    }

    /**
     * Check if the user has a specific permission for a nursing home.
     *
     * @param int $nursingHomeId
     * @param string $permission
     * @return bool
     */
    public function hasInstitutionPermission($nursingHomeId, $permission)
    {
        // 超级管理员拥有所有权限
        if ($this->isInstitutionSuperAdmin()) {
            return true;
        }

        $permissionRecord = $this->institutionUserPermissions()
            ->where('nursing_home_id', $nursingHomeId)
            ->first();

        if ($permissionRecord) {
            return $permissionRecord->{$permission};
        }

        return false;
    }

    /**
     * Check if the user can manage staff for a nursing home.
     *
     * @param int $nursingHomeId
     * @return bool
     */
    public function canManageStaff($nursingHomeId)
    {
        return $this->hasInstitutionPermission($nursingHomeId, 'can_manage_staff');
    }

    /**
     * Check if the user can manage info for a nursing home.
     *
     * @param int $nursingHomeId
     * @return bool
     */
    public function canManageInfo($nursingHomeId)
    {
        return $this->hasInstitutionPermission($nursingHomeId, 'can_manage_info');
    }

    /**
     * Check if the user can manage videos for a nursing home.
     *
     * @param int $nursingHomeId
     * @return bool
     */
    public function canManageVideos($nursingHomeId)
    {
        return $this->hasInstitutionPermission($nursingHomeId, 'can_manage_videos');
    }

    /**
     * Check if the user can view data for a nursing home.
     *
     * @param int $nursingHomeId
     * @return bool
     */
    public function canViewData($nursingHomeId)
    {
        return $this->hasInstitutionPermission($nursingHomeId, 'can_view_data');
    }

    /**
     * Check if the user can manage finance for a nursing home.
     *
     * @param int $nursingHomeId
     * @return bool
     */
    public function canManageFinance($nursingHomeId)
    {
        return $this->hasInstitutionPermission($nursingHomeId, 'can_manage_finance');
    }
}
