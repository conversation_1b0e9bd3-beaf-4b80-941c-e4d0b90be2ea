<?php

use Dcat\Admin\Admin;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use App\Admin\Controllers\HomeController;
use App\Admin\Controllers\NewsController;
use App\Admin\Controllers\PageController;
use App\Admin\Controllers\ToolController;
use App\Admin\Controllers\UserController;
use App\Admin\Controllers\DiaryController;
use App\Admin\Controllers\ConfigController;
use App\Admin\Controllers\SliderController;
use App\Admin\Controllers\SmsLogController;
use App\Admin\Controllers\CommentController;
use App\Admin\Controllers\ContactController;
use App\Admin\Controllers\InvoiceController;
use App\Admin\Controllers\NewsTagController;
use App\Admin\Controllers\PaymentController;
use App\Admin\Controllers\ContractController;
use App\Admin\Controllers\FeedbackController;
use App\Admin\Controllers\StatisticController;
use App\Admin\Controllers\NursingHomeController;
use App\Admin\Controllers\NewsCategoryController;
use App\Admin\Controllers\AdminLoginLogController;
use App\Admin\Controllers\UserShareRecordController;
use App\Admin\Controllers\UserBrowseRecordController;
use App\Admin\Controllers\NursingHomeContactController;
use App\Admin\Controllers\InstitutionUserPermissionController;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->get('/', [HomeController::class, 'index']);
    $router->resource('users', UserController::class);
    $router->resource('pages', PageController::class);
    $router->resource('news', NewsController::class);
    $router->get('news-categories/export', [NewsCategoryController::class, 'export'])->name('news-categories.export');
    $router->resource('news-categories', NewsCategoryController::class);
    $router->resource('news-tags', NewsTagController::class);
    $router->resource('sliders', SliderController::class);
    $router->resource('configs', ConfigController::class);
    $router->resource('contacts', ContactController::class);
    $router->resource('feedbacks', FeedbackController::class);
    $router->resource('sms-logs', SmsLogController::class);
    $router->resource('payments', PaymentController::class);
    $router->resource('invoices', InvoiceController::class);
    $router->resource('diaries', DiaryController::class);
    $router->resource('comments', CommentController::class);
    $router->resource('nursing-homes', NursingHomeController::class);
    $router->resource('nursing-home-contacts', NursingHomeContactController::class);
    $router->resource('user-browse-records', UserBrowseRecordController::class);
    $router->resource('user-share-records', UserShareRecordController::class);
    $router->resource('contracts', ContractController::class);
    $router->resource('institution-user-permissions', InstitutionUserPermissionController::class); // 机构用户权限管理


    // 工具
    $router->get('tools', [ToolController::class, 'index']);
    $router->any('tools/{method?}', [ToolController::class, 'process']);

    $router->get('environment', [HomeController::class, 'environment']);

    // 数据统计
    $router->get('statistics', [StatisticController::class, 'index']);
    $router->get('statistics/pie', [StatisticController::class, 'pie']);
    $router->get('statistics/bar', [StatisticController::class, 'bar']);
    $router->get('statistics/table', [StatisticController::class, 'table']);

    // admin login log
    $router->resource('admin-login-logs', AdminLoginLogController::class);


});
