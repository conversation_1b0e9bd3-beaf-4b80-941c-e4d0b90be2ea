<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class NursingHomeContact extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'nursing_home_contacts';
    protected $fillable = [
        'nursing_home_id',
        'reason',
        'name',
        'phone',
        'message',
    ];

    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class);
    }
}
