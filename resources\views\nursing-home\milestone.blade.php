@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/nursing-home/milestone',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <div class="tab-title mb-4">
      <a class="tab-title-first" href="{{route('nursing-home.index')}}">机构信息</a>
      <h5>里程碑管理</h5>
    </div>
    @if (session('message'))
    <div class="alert alert-success">
      {{ session('message') }}
    </div>
    @endif
    @if ($errors->any())
    <div class="alert alert-danger">
      <ul>
        @foreach ($errors->all() as $error)
        <li>{{ $error }}</li>
        @endforeach
      </ul>
    </div>
    @endif

    <section class="border-main rounded mt-1 bg-white p-3 p-lg-5">
      <h6><i class="bi bi-info-circle me-2"></i>大家都对你们的发展轨迹感兴趣，请大方的说出你们一步一脚印的里程碑吧！</h6>
      <form class="form-wrap mt-4" method="post" enctype="multipart/form-data" action="{{ route('nursing-home.milestoneUpdate') }}">
        @csrf
        <div class="milestones-container">
          @if (isset($milestones) && count($milestones) > 0)
            @foreach ($milestones as $index => $milestone)
            <!-- 已添加的里程碑 -->
            <div class="milestone-item position-relative mb-4">
              <div class="row mb-4">
                <label for="title_{{ $index }}" class="col-sm-2 col-form-label">里程碑时间</label>
                <div class="col-sm-5">
                  <input type="text" name="milestones[{{ $index }}][title]" id="title_{{ $index }}" class="form-control" value="{{ old('milestones.'.$index.'.title', $milestone->title) }}">
                </div>
              </div>
              <div class="row">
                <label for="description_{{ $index }}" class="col-sm-2 col-form-label">里程碑事件</label>
                <div class="col-sm-5">
                  <textarea name="milestones[{{ $index }}][description]" id="description_{{ $index }}" class="form-control">{{ old('milestones.'.$index.'.description', $milestone->description) }}</textarea>
                </div>
              </div>
              <button type="button" class="remove-milestone"><i class="bi bi-trash3"></i></button>
            </div>
            @endforeach
          @else
          <!-- 里程碑添加模版 -->
          <div class="milestone-item position-relative mb-4">
            <div class="row mb-4">
              <label for="title_0" class="col-sm-2 col-form-label">里程碑时间</label>
              <div class="col-sm-5">
                <input type="text" name="milestones[0][title]" id="title_0" class="form-control">
              </div>
            </div>
            <div class="row">
              <label for="description_0" class="col-sm-2 col-form-label">里程碑事件</label>
              <div class="col-sm-5">
                <textarea name="milestones[0][description]" id="description_0" class="form-control"></textarea>
              </div>
            </div>
            <button type="button" class="remove-milestone"><i class="bi bi-trash3"></i></button>
          </div>
          @endif
        </div>

        <div class="row mb-4">
          <div class="col-sm-7">
            <button type="button" class="py-2 px-3 add-milestone"><i class="bi bi-plus-circle me-2"></i>添加里程碑</button>
          </div>
          <!-- 提示信息 -->
          <div class="col-sm-5">
            <div class="alert alert-info">
              <p><i class="bi bi-info-circle me-2"></i>您可以通过拖动排序来调整里程碑的顺序。</p>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-sm-7">
            <button type="submit" class="btn btn-primary text-nowrap btn-lg px-5">提交</button>
          </div>
        </div>
      </form>
    </section>
  </div>
</div>
@endsection

@section('js')
<script src="{{ asset('js/Sortable.min.js') }}"></script>
<script>
  jQuery(document).ready(function($) {
    let milestoneIndex = $('.milestone-item').length; // 当前里程碑的数量

    // 初始化排序功能
    new Sortable(document.querySelector('.milestones-container'), {
      animation: 150,
      handle: '.milestone-item',
    });

    // 添加里程碑
    $('.add-milestone').on('click', function() {
      const newItem = `
              <div class="milestone-item position-relative mb-4">
                  <div class="row mb-4">
                      <label for="title_${milestoneIndex}" class="col-sm-2 col-form-label">里程碑时间</label>
                      <div class="col-sm-5">
                          <input type="text" name="milestones[${milestoneIndex}][title]" id="title_${milestoneIndex}" class="form-control">
                      </div>
                  </div>
                  <div class="row mb-2">
                      <label for="description_${milestoneIndex}" class="col-sm-2 col-form-label">里程碑事件</label>
                      <div class="col-sm-5">
                          <textarea name="milestones[${milestoneIndex}][description]" id="description_${milestoneIndex}" class="form-control"></textarea>
                      </div>
                  </div>
                  <button type="button" class="remove-milestone"><i class="bi bi-trash3"></i></button>
              </div>
          `;
      $('.milestones-container').append(newItem);
      milestoneIndex++;
    });

    // <i class="bi bi-trash3"></i>里程碑
    $('.milestones-container').on('click', '.remove-milestone', function() {
      $(this).closest('.milestone-item').remove();
    });
  });
</script>
@endsection
