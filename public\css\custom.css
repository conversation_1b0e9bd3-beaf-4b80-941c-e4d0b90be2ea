img {
    width: auto;
    height: auto;
    max-width: 100%;
}

/* 头像样式 - 确保头像尺寸正确显示 */
.user-list-table img[width][height],
.user-list-table img.rounded-circle {
    width: 32px !important;
    height: 32px !important;
    max-width: 32px !important;
    max-height: 32px !important;
    object-fit: cover;
    flex-shrink: 0;
}

/* 用户详情页面的头像 */
img[width="80"][height="80"] {
    width: 80px !important;
    height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    object-fit: cover;
}

/* 有明确尺寸属性的图片 */
img[width][height] {
    object-fit: cover;
}

.w-100 {
    width: 100%;
}
.h-100 {
    height: 100%;
}
.navbar-brand .img-fluid {
    max-height: 40px;
}
.home-banner .img-fluid {
    max-height: 480px;
}
.navbar {
    padding: 0;
}

.main-navbar .nav-item a,
.main-navbar .nav-item span {
    color: #000;
    min-width: 80px;
    text-align: center;
}

@media (min-width: 768px) {
    .main-navbar .nav-item a{
        line-height: 2.8rem;
        font-size: 0.92rem;
    }
    .main-navbar .nav-item a:hover,
    .main-navbar .nav-item a:focus {
        background-color: #f5f5f5;
    }
}

.main-navbar .dropdown-item a,
.main-navbar .dropdown-item span,
.main-navbar .dropdown-item a,
.main-navbar a.dropdown-item{
    line-height: 1 !important;
    padding: 0.5rem;
}

.nav-tiem-login .nav-link {
    color: #fff !important;
    background-color: #f2ae1e;
}

.nav-tiem-login .nav-link:hover,
.nav-tiem-login .nav-link:focus {
    background-color: #000;
}

.bg-light {
    background-color: #f8f8f8 !important;
}
.text-bg-light {
    background-color: #f8f8f8 !important;
}

.btn-outline-secondary {
    --bs-btn-color: #333;
    --bs-btn-border-color: #ccc;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #333;
    --bs-btn-hover-border-color: #333;
    --bs-btn-focus-shadow-rgb: 108, 117, 125;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #333;
    --bs-btn-active-border-color: #333;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #ccc;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #ccc;
    --bs-gradient: none;
}

th,
td {
    vertical-align: middle;
}
.table-hover > tbody > tr:hover > * {
    --bs-table-bg-state: rgba(
        var(--bs-light-rgb),
        var(--bs-bg-opacity)
    ) !important;
}
.form-control {
    appearance: auto;
    font-size: 13px;
}

@media (min-width: 992px) {
    .diary-thumb.img-thumbnail {
        max-height: 120px;
        padding: 0;
    }
}

.video-wrapper {
    background-color: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    /* justify-content: center; */
}
.video-play {
    font-size: 50px;
}

.text-right {
    text-align: right;
}

.login-banner {
    background-image: url(../images/bg-login.jpg);
    background-color: #000;
    background-repeat: no-repeat;
    background-position: left center;
    background-size: cover;
    min-height: 618px;
}

@media (min-width: 1280px) {
    .login-banner {
        min-height: 618px;
        background-size: contain;
    }
}

.btn-login {
    background-color: #f2ae1e;
    border: 0 !important;
    border-radius: 4px;
}

.btn-login:hover {
    background-color: #000;
}

.logo-login {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    max-width: 100px;
}

.underline {
    text-decoration: underline;
}

.auth-avator-img {
    max-height: 32px;
}

.nav-list .nav-link {
    color: #666;
    font-size: 0.92rem;
}
.nav-list .nav-link.active,
.nav-list .nav-link:hover,
.nav-list .nav-link:focus {
    font-weight: bold;
    color: #f2ae1e;
    border-bottom: 1px solid #f2ae1e;
}

.min-w-160 {
    min-width: 160px;
}

@media (min-width: 992px) {
    .modal-default {
        --bs-modal-width: 420px;
    }
}

/* 分享视频页面 */

/* 新增视频容器样式 */
.video-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding-top: 90vh; /* 16:9 Aspect Ratio */
    background: #000;
}
#video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}
.video-mask {
    cursor: pointer;
    transition: opacity 0.3s;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s;
}
.video-mask.hidden {
    opacity: 0;
    pointer-events: none;
}
.video-info {
    right: 0;
    bottom: 0;
    left: 0;
    color: #fff;
    background: -moz-linear-gradient(
        top,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
    );
    background: -webkit-gradient(
        left top,
        left bottom,
        color-stop(0%, rgba(0, 0, 0, 0)),
        color-stop(100%, rgba(0, 0, 0, 0.5))
    );
    background: -webkit-linear-gradient(
        top,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
    );
    background: -o-linear-gradient(
        top,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
    );
    background: -ms-linear-gradient(
        top,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
    );
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
    );
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#000000', endColorstr='#000000', GradientType=0 );
}

.video-info h1,
.video-info h2 {
    color: #fff;
}
.video-info h1 {
    font-size: 20px !important;
    font-weight: normal !important;
}

.video-info h2 {
    font-size: 16px !important;
    font-weight: normal !important;
}

/* 落地页 */
.weixin-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9998;
}

.weixin-modal {
    position: fixed;
    color: #fff;
    left: 10%;
    right: 10%;
    top: 0;
    z-index: 9999;
    max-width: 100%;
}

.weixin-modal img {
    display: block;
    margin: 0 auto;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(3, 100px);
    gap: 10px;
}
.image-item {
    width: 100px;
    height: 100px;
    border: 1px dashed #999;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    border-radius: 4px;
}
.image-item img {
    max-width: 100%;
    max-height: 100%;
}
.image-item.placeholder {
    background-color: #eee;
    color: #666;
    font-size: 18px;
    font-weight: bold;
    border-radius: 4px;
}
.image-item .remove-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: red;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
}
.image-item:hover .remove-btn {
    opacity: 1;
}

.tab-title {
    display: flex;
    align-items: center;
}
.tab-title a {
    color: #000;
    font-weight: 700;
    font-size: 20px;
}
.tab-title > * {
    line-height: 1;
    margin-left: 30px;
    padding-bottom: 6px;
    border-bottom: 3px solid transparent;
}
.tab-title h5 {
    margin-bottom: 0;
    color: #f2ae1e;
    border-bottom: 2px solid #f2ae1e;
}
.tab-title > .tab-title-first {
    margin-left: 0;
}
.milestone-item {
    border: #ddd dashed 2px;
    padding: 30px;
    border-radius: 8px;
}
.remove-milestone {
    position: absolute;
    right: 20px;
    top: 20px;
    border: 0;
}
.add-milestone {
    border: 0;
    height: 50px;
    border-radius: 6px;
}

.page-yly .avatar {
    max-width: 64px;
}

.yly-info {
    font-size: 16px;
}

.el-dialog.dialog-img {
    max-width: 80%;
}

/* sidebar-menu */
.sidebar-menu,
.sidebar-sub-menu {
    list-style: none;
    font-size: 16px;
    padding-left: 0;
}
.sidebar-sub-menu {
    padding-left: 24px;
}

.sidebar-menu .active span,
.sidebar-menu .active .icon {
    color: #f2ae1e;
}
