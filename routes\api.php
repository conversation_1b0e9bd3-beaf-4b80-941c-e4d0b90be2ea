<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AreaController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\FileController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Api\NewsController;
use App\Http\Controllers\Api\PageController;
use App\Http\Controllers\Api\TestController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\DiaryController;
use App\Http\Controllers\Api\ShareController;
use App\Http\Controllers\Api\AuthMbController;
use App\Http\Controllers\Api\ConfigController;
use App\Http\Controllers\Api\ImagesController;
use App\Http\Controllers\Api\SliderController;
use App\Http\Controllers\Api\AuthXcxController;
use App\Http\Controllers\Api\CommentController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\NewsTagController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\ContractController;
use App\Http\Controllers\Api\FeedbackController;
use App\Http\Controllers\Api\NursingHomeController;
use App\Http\Controllers\Api\NewsCategoryController;
use App\Http\Controllers\Api\NursingHomeContactController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 测试
Route::get('test', [TestController::class, 'index']);

// 注册
Route::post('register', [AuthController::class, 'register']);

// 首页推荐、关注、我的
Route::get('home/playlist', [HomeController::class, 'homePlayList']);

/**
 * 授权管理
 */
Route::group(['prefix' => 'auth'], function () {
    // 假冒登录
    Route::get('test', [AuthController::class, 'test']);
    Route::get('fake', [AuthController::class, 'fake']);

    // 用户名密码登录
    Route::post('username/password/login', [AuthController::class, 'usernamePasswordLogin']);
    // 手机号密码登录
    Route::post('mobile/password/login', [AuthController::class, 'mobilePasswordLogin']);
    // 邮箱密码登录
    Route::post('email/password/login', [AuthController::class, 'emialPasswordLogin']);
    // 小程序授权
    Route::post('xcx', [AuthController::class, 'xcxAuth']);
    // 小程序绑定手机
    Route::post('xcx/bind/mobile', [AuthController::class, 'xcxBindMobile']);
    // 微信H5授权
    Route::get('wx', [AuthController::class, 'wxAuth']);
    // 微信绑定手机
    Route::post('wx/bind/mobile', [AuthController::class, 'wxBindMobile']);
});
// 小程序登录 - openid作为唯一标识
Route::post('xcx/login/unique/openid', [AuthXcxController::class, 'loginByUniqueOpenid']);
// 小程序绑定手机 - openid作为唯一标识
Route::post('xcx/bind/unique/openid', [AuthXcxController::class, 'bindByUniqueOpenid']);
// 小程序登录 - mobile作为唯一标识
Route::post('xcx/login/unique/mobile', [AuthXcxController::class, 'loginByUniqueMobile']);
// 小程序手机登录 - mobile作为唯一标识
Route::post('xcx/bind/unique/mobile', [AuthXcxController::class, 'bindByUniqueMobile']);

// 需要登录后的接口
Route::middleware('auth:sanctum')->group(function () {

    // 用户接口
    Route::apiResource('users', UserController::class);
    // 当前用户
    Route::get('user-detail', [UserController::class, 'current']);

    // 青少年密码设置
    Route::post('user/password/set', [UserController::class, 'setPassword']);
    // 青少年密码校验
    Route::post('user/password/check', [UserController::class, 'checkPassword']);

    // 支付
    Route::get('order/{id}/payment', [PaymentController::class, 'payByWechat']);

    // 登出
    Route::get('logout', [AuthController::class, 'logout']);

    // 需要绑定手机的接口
    Route::middleware('mobile')->group(function () {
        // 意见反馈
        Route::post('feedback', [FeedbackController::class, 'create']);
        Route::apiResource('feedback', FeedbackController::class)->only('index', 'show', 'destroy');
    });
});


// 配置
Route::apiResource('configs', ConfigController::class)->only(['index', 'show']);
// 批量查询
Route::post('config/search', [ConfigController::class, 'search']);

// 页面
Route::apiResource('pages', PageController::class)->only(['show']);

// 滑块
Route::apiResource('sliders', SliderController::class)->only(['index', 'show']);

// 新闻
Route::apiResource('news', NewsController::class)->only(['index', 'show']);
// 推荐
Route::get('/news-recommend', [NewsController::class, 'recommend']);
// 分类
Route::apiResource('news-categories', NewsCategoryController::class)->only(['index', 'show']);
// 标签
Route::apiResource('news-tags', NewsTagController::class)->only(['index', 'show']);

/*
 * 通用接口
 */
// 获取jssdk
Route::get('jssdk', [AuthController::class, 'jssdk']);
// 上传图片
Route::post('images', [ImagesController::class, 'index']);
// 上传文件
Route::post('upload', [FileController::class, 'index']);

// 联系
Route::post('contacts', [ContactController::class, 'store']);

/*
 * 手机号登录NEW
 */
// 发送验证码
Route::get('mobile/send/code', [AuthMbController::class, 'sendVerifyCode']);
// 手机号登录
Route::post('mobile/login', [AuthMbController::class, 'mobileLogin']);

// 支付回调通知
Route::post('payment/wechat/notify', [PaymentController::class, 'wechatNotify']);

// 地区、城市相关
Route::get('areas', [AreaController::class, 'index']);
Route::get('areas/tree', [AreaController::class, 'tree']);
Route::get('areas/city', [AreaController::class, 'city']);

// 养老院下的日记
Route::get('nursing-home/diaries', [NursingHomeController::class, 'diaries']);
// 养老院基本信息
Route::get('nursing-home/basic', [NursingHomeController::class, 'basic']);
// 养老院列表和详情
Route::apiResource('nursing-homes', NursingHomeController::class)->only(['index', 'show']);

// 日记
Route::apiResource('diaries', DiaryController::class)->only(['index', 'show']);
Route::get('diaries/{id}/playlist', [DiaryController::class, 'playlist']);

// 养老院
Route::middleware('auth:sanctum')->group(function () {
    // 我的养老院列表
    Route::get('my-nursing-homes', [NursingHomeController::class, 'myNursingHomeList']);
    // 我的养老院
    Route::post('my-nursing-homes', [NursingHomeController::class, 'myNursingHome']);
    // 切换养老院
    Route::post('user/nursing-home/change', [UserController::class, 'changeNursingHome']);
    // 用户养老院列表
    Route::get('user/nursing-home/list', [UserController::class, 'userNursingHomeList']);

    // 用户关注列表
    Route::get('nursing-home/follows', [NursingHomeController::class, 'followList']);
    // 关注养老院
    Route::post('nursing-home/follow', [NursingHomeController::class, 'follow']);
    Route::get('nursing-homes/tree', [NursingHomeController::class, 'tree']);

    // 联系
    Route::apiResource('nursing-home-contacts', NursingHomeContactController::class)->only(['store']);

    // 员工接受邀请
    Route::post('nursing-home/staff/invite', [NursingHomeController::class, 'handleQrInvite']);

    // 点赞
    Route::post('diaries/like', [DiaryController::class, 'like']);
    // 收藏
    Route::post('diaries/favorite', [DiaryController::class, 'collection']);
    // 【废弃】分享
    Route::post('diaries/share', [DiaryController::class, 'share']);
    // 浏览记录
    Route::post('diaries/browse', [DiaryController::class, 'recordBrowse']);

    // 转发相关接口
    Route::post('diaries/share/create', [ShareController::class, 'createShare']);
    Route::post('diaries/share/click', [ShareController::class, 'handleShareClick']);

    // 机构主页分享相关接口
    Route::post('nursing-homes/share/create', [ShareController::class, 'createNursingHomeShare']);
    Route::post('nursing-homes/share/click', [ShareController::class, 'handleNursingHomeShareClick']);

    Route::post('diaries/comment', [CommentController::class, 'comment']); // 评论
    Route::delete('diaries/comment', [CommentController::class, 'deleteComment']); // 删除评论
    Route::get('diaries/{id}/comments', [CommentController::class, 'comments']); // 评论列表

    // 签约
    Route::get('contracts', [ContractController::class, 'index']);
    Route::post('contracts', [ContractController::class, 'store']);
    Route::put('contracts', [ContractController::class, 'update']);
});
