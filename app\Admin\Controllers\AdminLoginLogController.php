<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use App\Admin\Repositories\AdminLoginLog;
use Dcat\Admin\Http\Controllers\AdminController;

class AdminLoginLogController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AdminLoginLog(['admin']), function (Grid $grid) {

            $grid->paginate(10);
            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('admin.username', '管理员');
            $grid->column('ip');
            $grid->column('created_at', '登录时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('id')->width(3);
                $filter->equal('admin.username', '管理员')->width(3);
                $filter->equal('ip')->width(3);
                $filter->date('created_at', '登录时间')->width(3);
            });

            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableActions();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdminLoginLog(), function (Show $show) {
            $show->field('id');
            $show->field('admin_id');
            $show->field('ip');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }
}
