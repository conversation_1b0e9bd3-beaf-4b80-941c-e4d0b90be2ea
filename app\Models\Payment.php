<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'payments';

    protected $fillable = [
        'no',
        'nursing_home_id',
        'pay_method',
        'total_amount',
        'paid_at',
        'transaction_no',
        'payer_name',
        'payer_mobile',
        'status',
        'remark',
        'start_at',
        'end_at',
    ];

    protected $casts = [
        'total_amount' => 'float',
        'paid_at' => 'datetime',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

    public function nursingHome()
    {
        return $this->belongsTo(NursingHome::class);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class);
    }

    // 创建生成订单号
    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->no) {
                    return false;
                }
            }
        });
    }

    // 生成订单编号
    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('no', $no)->exists()) {
                return $no;
            }
            usleep(100);
        }
        \Log::warning(sprintf('find payment no failed'));

        return false;
    }
}
