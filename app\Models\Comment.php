<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'comments';

    protected $fillable = [
        'user_id',
        'diary_id',
        'comment_id',
        'first_comment_id',
        'second_comment_id',
        'content',
        'status',
        'deleted_at',
        'nursing_home_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function diary()
    {
        return $this->belongsTo(Diary::class);
    }

    // 父评论
    public function comment()
    {
        return $this->belongsTo(Comment::class, 'comment_id');
    }

    // 一级评论
    public function scopeMainComment($query)
    {
        return $query->whereNull('comments.comment_id');
    }

    // 二级评论
    public function secondComments()
    {
        return $this->hasMany(Comment::class, 'first_comment_id');
    }

    // 三级评论
    public function thirdComments()
    {
        return $this->hasMany(Comment::class, 'second_comment_id');
    }

    // 子级评论（通用）
    public function subComments()
    {
        return $this->hasMany(Comment::class, 'comment_id');
    }

    // 子级评论（1条）
    public function subCommentsShow()
    {
        return $this->hasMany(Comment::class, 'comment_id')->limit(1)->orderBy('created_at', 'desc');
    }
}
