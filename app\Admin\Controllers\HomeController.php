<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Layout\Row;
use Illuminate\Support\Arr;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use App\Http\Controllers\Controller;

class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header('主页')
            ->body(function (Row $row) {
                // HTML信息卡片
                $row->column(12, function (Column $column) {
                    $column->row(view('admin.dashboard.info'));
                });
            });
    }

    public function environment(Content $content)
    {
        return $content
            ->header('环境信息')
            ->body(function (Row $row) {
                $row->column(12, function (Column $column) {

                    $envs = [
                        ['name' => 'PHP version',       'value' => 'PHP/' . PHP_VERSION],
                        ['name' => 'Laravel version',   'value' => app()->version()],
                        ['name' => 'CGI',               'value' => php_sapi_name()],
                        ['name' => 'Uname',             'value' => php_uname()],
                        ['name' => 'Server',            'value' => Arr::get($_SERVER, 'SERVER_SOFTWARE')],

                        ['name' => 'Cache driver',      'value' => config('cache.default')],
                        ['name' => 'Session driver',    'value' => config('session.driver')],
                        ['name' => 'Queue driver',      'value' => config('queue.default')],

                        ['name' => 'Timezone',          'value' => config('app.timezone')],
                        ['name' => 'Locale',            'value' => config('app.locale')],
                        ['name' => 'Env',               'value' => config('app.env')],
                        ['name' => 'URL',               'value' => config('app.url')],
                    ];

                    $column->row(view('admin.dashboard.environment', compact('envs')));
                });
            });
    }
}
