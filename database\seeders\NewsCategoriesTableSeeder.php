<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class NewsCategoriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('news_categories')->delete();
        
        \DB::table('news_categories')->insert(array (
            0 => 
            array (
                'id' => 1,
                'parent_id' => 0,
                'title' => '分类一',
                'sort_order' => 0,
                'status' => 1,
                'created_at' => '2022-11-02 16:45:43',
                'updated_at' => '2022-11-02 17:01:52',
            ),
            1 => 
            array (
                'id' => 2,
                'parent_id' => 0,
                'title' => '分类二',
                'sort_order' => 1,
                'status' => 1,
                'created_at' => '2022-11-02 16:45:57',
                'updated_at' => '2022-11-02 17:01:52',
            ),
            2 => 
            array (
                'id' => 3,
                'parent_id' => 0,
                'title' => '分类三',
                'sort_order' => 2,
                'status' => 1,
                'created_at' => '2022-11-02 17:01:03',
                'updated_at' => '2022-11-02 17:01:03',
            ),
        ));
        
        
    }
}