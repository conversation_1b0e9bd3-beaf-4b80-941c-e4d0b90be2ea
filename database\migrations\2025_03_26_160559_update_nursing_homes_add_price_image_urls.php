<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->text('price_image_urls')->nullable()->comment('价目表')->after('image_urls');
            $table->text('transportation_image_urls')->nullable()->comment('交通')->after('price_image_urls');
            $table->text('honor_image_urls')->nullable()->comment('荣誉')->after('transportation_image_urls');
            $table->text('other_image_urls')->nullable()->comment('其他')->after('honor_image_urls');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('nursing_homes', function (Blueprint $table) {
            $table->dropColumn('price_image_urls');
            $table->dropColumn('transportation_image_urls');
            $table->dropColumn('honor_image_urls');
            $table->dropColumn('other_image_urls');
        });
    }
};
