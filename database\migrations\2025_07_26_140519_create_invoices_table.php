<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('payment_id')->nullable()->comment('付款记录ID');
            $table->string('invoice_title')->nullable()->comment('发票抬头');
            $table->string('tax_number')->nullable()->comment('税号');
            $table->string('address')->nullable()->comment('地址');
            $table->string('phone')->nullable()->comment('电话');
            $table->string('bank_name')->nullable()->comment('开户行');
            $table->string('bank_account')->nullable()->comment('银行账户');
            $table->decimal('amount')->nullable()->comment('金额');
            $table->tinyInteger('status')->default(1)->comment('状态');
            $table->string('remark')->nullable()->comment('备注');
            $table->timestamps();
            $table->softDeletes();
            
            $table->foreign('payment_id')->references('id')->on('payments')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
};
