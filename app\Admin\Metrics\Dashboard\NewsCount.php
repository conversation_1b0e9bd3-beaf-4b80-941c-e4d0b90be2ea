<?php

namespace App\Admin\Metrics\Dashboard;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Widgets\Metrics\Line;

class NewsCount extends Line
{
    /**
     * 初始化卡片内容
     */
    public function init()
    {
        parent::init();
        // 卡片内容宽度
        $this->contentWidth(3, 9);
        // 标题
        $this->title('新闻统计');
        // 设置下拉选项
        $this->dropdown([
            '7'   => '近一周',
            '30'  => '近一月',
            '365' => '近一年',
        ]);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return mixed|void
     */
    public function handle(Request $request)
    {
        $global_count = News::where('status', 1);
        $global_filter = News::selectRaw("COUNT(status) AS news_count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
            ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
            ->where('status', 1);

        switch ($request->get('option')) {
            case '365':
                $total = $global_count->whereBetween('created_at', [now()->subYear(), now()])->count();

                $dateList = [];
                for ($i = 12; $i >= 1; $i--) {
                    $dateList[] = [
                        'start' => now()->subMonth($i),
                        'end' => now()->subMonth($i - 1)
                    ];
                }
                $result = [];
                foreach ($dateList as $value) {
                    $result_filter = News::selectRaw("COUNT(status) AS news_count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
                        ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
                        ->where('status', 1)->whereBetween('created_at', [$value['start'], $value['end']])->get();
                    $result[] = $result_filter->sum('news_count');
                }
                break;
            case '30':
                $total = $global_count->whereBetween('created_at', [now()->subMonth(), now()])->count();
                $result_filter = $global_filter->whereBetween('created_at', [now()->subMonth(), now()])->get();

                $dateList = [];
                for ($i = 29; $i >= 0; $i--) {
                    $dateList[] = now()->subDay($i)->toDateString();
                }
                $result = [];
                foreach ($dateList as $value) {
                    $item = $result_filter->firstWhere('day', $value);
                    $result[] = $item ? $item->news_count : 0;
                }
                break;
            case '7':
            default:
                $total = $global_count->whereBetween('created_at', [now()->subWeek(), now()])->count();
                $result_filter = $global_filter->whereBetween('created_at', [now()->subWeek(), now()])->get();

                $dateList = [];
                for ($i = 6; $i >= 0; $i--) {
                    $dateList[] = now()->subDay($i)->toDateString();
                }
                $result = [];
                foreach ($dateList as $value) {
                    $item = $result_filter->firstWhere('day', $value);
                    $result[] = $item ? $item->news_count : 0;
                }
                break;
        }
        // 卡片内容
        $this->withContent($total);

        // 图表数据
        $this->withChart([
            [
                'name' => '数量',
                'data' => $result,
            ],
        ]);

        // 图表类别
        $this->withCategories($dateList);
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data)
    {
        return $this->chart([
            'series' => $data
        ]);
    }

    /**
     * 设置图表类别.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withCategories(array $data)
    {
        return $this->option('xaxis.categories', $data);
    }

    /**
     * 设置卡片内容.
     *
     * @param string $value
     *
     * @return $this
     */
    public function withContent($value)
    {
        $minHeight = '183px';

        return $this->content(
            <<<HTML
                <div class="d-flex justify-content-between align-items-center mt-4" style="margin-bottom: 2px">
                    <h2 class="ml-1 font-lg-1">{$value}</h2>
                    <span class="mb-4 mr-1 text-80">总数</span>
                </div>
            HTML
        );
    }
}
