<?php

namespace App\Http\Controllers\Web;

use Image;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\ApiController;
use Rolandstarke\Thumbnail\Facades\Thumbnail;

class ImagesController extends ApiController
{
    public function index(Request $request)
    {

        $allowed_ext = ["png", "jpg", "gif", 'jpeg'];
        $folder_name = 'nursing-home/' . date("Y/m");

        $image_path = '';
        if ($request->file('file')) {
            $file = $request->file('file');

            $full_image_path = '';
            if ($file) {
                // 获取文件的后缀名，因图片从剪贴板里黏贴时后缀名为空，所以此处确保后缀一直存在
                $extension = strtolower($file->getClientOriginalExtension()) ?: 'png';

                // 如果上传的不是图片将终止操作
                if (!in_array($extension, $allowed_ext)) {
                    return false;
                }

                $filename = time() . '_' . Str::random(10) . '.' . $extension;
                $image_path = $file->storeAs($folder_name, $filename, 'oss');
                $full_image_path = \Storage::disk('oss')->url($image_path);
            }

            $return_data = [
                'image'      => $full_image_path,
            ];
            return $this->success($return_data);
        }
    }
}
