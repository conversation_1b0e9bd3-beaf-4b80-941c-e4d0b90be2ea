<?php

namespace App\Http\Resources;

use App\Services\ConfigService;
use Illuminate\Http\Resources\Json\JsonResource;

class ConfigResource extends JsonResource
{
    /**
     * Undocumented function
     *
     * @param [type] $request
     */
    public function toArray($request)
    {
        $configService = new ConfigService();
        $getValues = $configService->getValue($this);

        return [
            'id'         => $this->id,
            'title'      => $this->title,
            'key'        => $this->key,
            'type'       => $this->type,
            'value'      => $getValues['value'],
            'value_text' => $getValues['value_text'],
            'created_at' => optional($this->created_at)->toDateTimeString(),
            'updated_at' => optional($this->updated_at)->toDateTimeString(),
        ];
    }
}
