<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'invoices';

    protected $fillable = [
        'no',
        'payment_id',
        'invoice_title',
        'tax_number',
        'address',
        'phone',
        'bank_name',
        'bank_account',
        'amount',
        'status',
        'remark',
        'invoice_file',
    ];

    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }
}
