<?php

namespace App\Http\Controllers\Api;

use App\Models\Page;
use App\Http\Resources\PageResource;

class PageController extends ApiController
{
    /**
     * 详情
     *
     * @param  string  $permalink
     * @return \Illuminate\Http\Response
     */
    public function show($permalink)
    {
        $result = Page::where('permalink', $permalink)->first();
        if($result){
            return $this->success(new PageResource($result));
        }
        $this->errorBadRequest('暂无该页面');
    }
}
