<?php
namespace App\Http\Controllers\Web\M;

use App\Models\UserFollow;
use App\Models\NursingHome;
use App\Http\Controllers\Web\WebController;

class YlyController extends WebController
{
    /**
     * 详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function detail($id)
    {
        $item = NursingHome::findOrFail($id);

        // 关注数量
        $item->follow_count = UserFollow::where('nursing_home_id', $id)->count();

        return view('m.yly', compact('item'));
    }
}
