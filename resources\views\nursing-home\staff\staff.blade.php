@extends('layouts.web')

@section('sidebar')
    @include('web.partials.sidebar', [
        'activeMenu' => '/institution/staff',
    ])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <h5 class="mb-4">员工管理</h5>

    <!-- 员工列表 -->
    <section class="border-main rounded mt-1 bg-white p-3 p-lg-5">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">员工列表</h6>
        <a href="{{ route('institution.staff.invite') }}" class="btn btn-primary">邀请员工</a>
      </div>

      <div class="table-responsive">
        <table class="table table-hover user-list-table">
          <thead>
            <tr>
              <th>员工</th>
              <th>管理员工</th>
              <th>管理信息</th>
              <th>管理视频</th>
              <th>查看数据</th>
              <th>财务管理</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            @forelse($staffMembers as $permission)
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div>
                    <div class="fw-bold">{{ $permission->user->nickname ?? '未知用户' }}</div>
                    <small class="text-muted">{{ $permission->user->mobile ?? '' }}</small>
                  </div>
                </div>
              </td>
              <td>
                @if($permission->can_manage_staff)
                  <span class="badge bg-success">是</span>
                @else
                  <span class="badge bg-danger">否</span>
                @endif
              </td>
              <td>
                @if($permission->can_manage_info)
                  <span class="badge bg-success">是</span>
                @else
                  <span class="badge bg-danger">否</span>
                @endif
              </td>
              <td>
                @if($permission->can_manage_videos)
                  <span class="badge bg-success">是</span>
                @else
                  <span class="badge bg-danger">否</span>
                @endif
              </td>
              <td>
                @if($permission->can_view_data)
                  <span class="badge bg-success">是</span>
                @else
                  <span class="badge bg-danger">否</span>
                @endif
              </td>
              <td>
                @if($permission->can_manage_finance)
                  <span class="badge bg-success">是</span>
                @else
                  <span class="badge bg-danger">否</span>
                @endif
              </td>
              <td>
                <a href="{{ route('institution.staff.edit', $permission) }}" class="btn btn-sm btn-outline-secondary">编辑</a>
                <form action="{{ route('institution.staff.remove', $permission) }}" method="POST" class="d-inline" onsubmit="return confirm('确定要移除该员工吗？');">
                  @csrf
                  @method('DELETE')
                  <button type="submit" class="btn btn-sm btn-outline-danger">移除</button>
                </form>
              </td>
            </tr>
            @empty
            <tr>
              <td colspan="7" class="text-center">暂无员工</td>
            </tr>
            @endforelse
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="d-flex justify-content-center mt-4">
        {{ $staffMembers->links() }}
      </div>
    </section>
  </div>
</div>
@endsection
