<?php

namespace App\Admin\Widgets\Charts;

use App\Models\News;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Widgets\ApexCharts\Chart;

class NewsStatistics extends Chart
{
    public function __construct($containerSelector = null, $options = [])
    {
        parent::__construct($containerSelector, $options);

        $this->setUpOptions();
    }

    /**
     * 初始化图表配置
     */
    protected function setUpOptions()
    {
        $color = Admin::color();

        $colors = [$color->danger(), $color->success()];

        $this->options([
            'colors' => $colors,
            'chart' => [
                'type' => 'bar',
                'height' => 500
            ],
            'plotOptions' => [
                'bar' => [
                    'horizontal' => true,
                    'dataLabels' => [
                        'position' => 'top',
                    ],
                ]
            ],
            'dataLabels' => [
                'enabled' => true,
                'offsetX' => -6,
                'style' => [
                    'fontSize' => '12px',
                    'colors' => ['#fff']
                ]
            ],
            'stroke' => [
                'show' => true,
                'width' => 1,
                'colors' => ['#fff']
            ],
            'xaxis' => [
                'categories' => [],
            ],
        ]);
    }

    /**
     * 处理图表数据
     */
    protected function buildData()
    {
        $news_data_filter = News::selectRaw("COUNT(status) AS news_count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
            ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
            ->where('status', 1)->where('is_recommend', 0)->whereBetween('created_at', [now()->subWeek(), now()])->get();
        $news_data_recommend_filter = News::selectRaw("COUNT(status) AS news_count, DATE_FORMAT(created_at,'%Y-%m-%d') AS day")
            ->groupBy(DB::raw("DATE_FORMAT(created_at,'%Y-%m-%d')"))
            ->where('status', 1)->where('is_recommend', 1)->whereBetween('created_at', [now()->subWeek(), now()])->get();

        $dateList = [];
        $dayListShort = [];
        for ($i = 6; $i >= 0; $i--) {
            $dateList[] = now()->subDay($i)->toDateString();
            $dayListShort[] = now()->subDay($i)->format('m.d');
        }
        $news_data1 = [];
        $news_data2 = [];
        foreach ($dateList as $value) {
            $item1 = $news_data_filter->firstWhere('day', $value);
            $item2 = $news_data_recommend_filter->firstWhere('day', $value);
            $news_data1[] = $item1 ? $item1->news_count : 0;
            $news_data2[] = $item2 ? $item2->news_count : 0;
        }
        // 执行你的数据查询逻辑
        $data = [
            [
                'name' => '非推荐新闻',
                'data' => $news_data1
            ],
            [
                'name' => '推荐新闻',
                'data' => $news_data2
            ]
        ];
        $this->withData($data);
        $this->withCategories($dayListShort);
    }

    /**
     * 设置图表数据
     *
     * @param array $data
     *
     * @return $this
     */
    public function withData(array $data)
    {
        return $this->option('series', $data);
    }

    /**
     * 设置图表类别.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withCategories(array $data)
    {
        return $this->option('xaxis.categories', $data);
    }

    /**
     * 渲染图表
     *
     * @return string
     */
    public function render()
    {
        $this->buildData();

        return parent::render();
    }
}
