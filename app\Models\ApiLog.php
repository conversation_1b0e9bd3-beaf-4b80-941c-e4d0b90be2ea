<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class ApiLog extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'api_logs';

    protected $fillable = [
        'id',
        'user_id',
        'request_parameters',
        'return_parameters',
        'type',
        'status',
    ];
}
