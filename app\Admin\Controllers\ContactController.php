<?php

namespace App\Admin\Controllers;

use App\Models\Contact;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Metrics\Card;

class ContactController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Contact(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('telephone');
            $grid->column('email');
            $grid->column('message')->display(function ($message) {
                return \Str::limit($message, 20);
            })->expand(function () {
                return new Card(nl2br($this->message));
            });
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name')->width(3);
                $filter->like('telephone')->width(3);
                $filter->like('email')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Contact(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('telephone');
            $show->field('email');
            $show->field('message');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Contact(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('telephone');
            $form->text('email');
            $form->textarea('message');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
