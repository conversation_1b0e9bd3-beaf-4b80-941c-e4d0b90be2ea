<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;

class News extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;
    protected $fillable = [
        'title',
        'news_category_id',
        'banner_url',
        'excerpt',
        'content',
        'read_count',
        'is_recommend',
        'status',
        'deleted_at',
    ];

    /**
     * 过滤启用的状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function getFullBannerUrlAttribute()
    {
        return getImageUrl($this->banner_url);
    }

    public function getFullBannerThumbUrlAttribute()
    {
        return getThumbUrl($this->banner_url, '480', '266');
    }

    public function getFullBannerThumbBigUrlAttribute()
    {
        return getThumbUrl($this->banner_url, '480', '300');
    }

    // // 图片处理示例：单图
    // public function getFullImageUrlAttribute()
    // {
    //     return getImageUrl($this->image_url);
    // }

    // // 图片处理示例：单图裁剪
    // public function getFullThumbUrlAttribute()
    // {
    //     return getThumbUrl($this->image_url, '540', '300');
    // }
    // public function getFullBannerThumbBigUrlAttribute()
    // {
    //     return getThumbUrl($this->banner_url, '480', '300');
    // }

    // // 图片处理示例：多图裁剪
    // public function getFullThumbUrlsAttribute()
    // {
    //     if($this->image_urls){
    //         return getThumbUrl(json_encode($this->image_urls), '540', '300', true);
    //     }
    //     return [];
    // }

    public function category()
    {
        return $this->belongsTo(NewsCategory::class, 'news_category_id');
    }

    public function tags()
    {
        return $this->belongsToMany(NewsTag::class, 'news_with_tags', 'news_id', 'news_tag_id')
            ->withTimestamps();
    }
}
