<?php

namespace App\Http\Controllers\Web;

use App\Models\News;
use App\Models\Slider;
use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;

class HomeController extends WebController
{
    public function index(Request $request)
    {
        $slider = Slider::active()->orderBy('sort_order', 'asc')->get();
        return view('home', compact('slider'));
    }

    /**
     * oss-callback
     *
     * https://help.aliyun.com/zh/oss/developer-reference/callback
     *
     * @param Request $request
     * @return void
     */
    public function ossCallback(Request $request)
    {
        \Log::info('oss-callback', [$request->all()]);

        $filename = $request->input('filename');

        // {
        //     "status": true,
        //     "data": {
        //         "id": "https:\/\/diary-oss.laoyangapp.com\/images\/2024\/11\/5a727e73c2fb26ee36d15914569122d0.jpg",
        //         "name": "5a727e73c2fb26ee36d15914569122d0.jpg",
        //         "path": "5a727e73c2fb26ee36d15914569122d0.jpg",
        //         "url": "https:\/\/diary-oss.laoyangapp.com\/images\/2024\/11\/5a727e73c2fb26ee36d15914569122d0.jpg"
        //     }
        // }

        $callBack = [
            'status' => true,
            'data'   => [
                'id'   => 'https://diary-oss.laoyangapp.com/' . $filename,
                'name' => $filename,
                'path' => $filename,
                'url'  => 'https://diary-oss.laoyangapp.com/' . $filename
            ],
        ];

        return response()->json($callBack);
    }
}
