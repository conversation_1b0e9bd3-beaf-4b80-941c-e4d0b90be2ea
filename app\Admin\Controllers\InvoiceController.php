<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Invoice;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class InvoiceController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Invoice(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('payment_id');
            $grid->column('invoice_title');
            $grid->column('tax_number');
            $grid->column('address');
            $grid->column('phone');
            $grid->column('bank_name');
            $grid->column('bank_account');
            $grid->column('amount');
            $grid->column('status')->using(Invoice::$statusMap);
            $grid->column('remark');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Invoice(), function (Show $show) {
            $show->field('id');
            $show->field('payment_id');
            $show->field('invoice_title');
            $show->field('tax_number');
            $show->field('address');
            $show->field('phone');
            $show->field('bank_name');
            $show->field('bank_account');
            $show->field('amount');
            $show->field('status');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Invoice(), function (Form $form) {
            $form->display('id');
            $form->text('payment_id');
            $form->text('invoice_title');
            $form->text('tax_number');
            $form->text('address');
            $form->text('phone');
            $form->text('bank_name');
            $form->text('bank_account');
            $form->text('amount');
            $form->select('status', '状态')
                ->options(Invoice::$statusMap)
                ->default(Invoice::STATUS_PENDING)
                ->help('发票状态');
            $form->text('remark');
            $form->file('invoice_file', '发票文件')
                ->disk('public')
                ->saveFullUrl()
                ->help('上传发票文件');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
