<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Web\WebController;
use App\Models\User;
use App\Models\Diary;
use App\Models\UserShareRecord;

class UserShareController extends WebController
{

    /**
     * 分享数据页面 - 基于用户分享记录重构
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $nursing_home_id = $user->manage_nursing_home_id;

        // 获取查询参数
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 20);
        $dateRange = $request->get('date_range', '7'); // 默认7天
        $sortBy = $request->get('sort_by', 'latest'); // latest, shares, clicks
        $searchUser = $request->get('search_user', ''); // 搜索用户
        $searchVideo = $request->get('search_video', ''); // 搜索视频
        $userId = $request->get('user_id'); // URL参数筛选用户
        $diaryId = $request->get('diary_id'); // URL参数筛选视频

        // 计算日期范围
        $startDate = match ($dateRange) {
            '1' => now()->startOfDay(),
            '7' => now()->subDays(6)->startOfDay(),
            '30' => now()->subDays(29)->startOfDay(),
            '90' => now()->subDays(89)->startOfDay(),
            default => now()->subDays(6)->startOfDay()
        };

        // 基础查询 - 获取该养老院的分享记录
        $shareRecordsQuery = UserShareRecord::with(['fromUser', 'toUser', 'diary', 'nursingHome'])
            ->where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate);

        // URL参数筛选
        $filteredUser = null;
        $filteredDiary = null;

        if ($userId) {
            $filteredUser = User::find($userId);
            if ($filteredUser) {
                $shareRecordsQuery->where('from_user_id', $userId);
            }
        }

        if ($diaryId) {
            $filteredDiary = Diary::find($diaryId);
            if ($filteredDiary) {
                $shareRecordsQuery->where('diary_id', $diaryId);
            }
        }

        // 搜索功能 - 分别处理用户搜索和视频搜索
        if ($searchUser) {
            $shareRecordsQuery->whereHas('fromUser', function ($query) use ($searchUser) {
                $query->where('nickname', 'like', "%{$searchUser}%");
                // ->orWhere('real_name', 'like', "%{$searchUser}%")
                // ->orWhere('mobile', 'like', "%{$searchUser}%");
            });
        }

        if ($searchVideo) {
            $shareRecordsQuery->whereHas('diary', function ($query) use ($searchVideo) {
                $query->where('title', 'like', "%{$searchVideo}%");
            });
        }

        // 排序
        switch ($sortBy) {
            case 'shares':
                // 按分享次数排序 - 需要子查询统计每个记录对应的总分享数
                $shareRecordsQuery->orderBy('created_at', 'desc');
                break;
            case 'clicks':
                // 优先显示有点击的记录
                $shareRecordsQuery->orderByRaw('to_user_id IS NOT NULL DESC, created_at DESC');
                break;
            default:
                $shareRecordsQuery->orderBy('created_at', 'desc');
        }

        // 分页获取分享记录
        $shareRecords = $shareRecordsQuery->paginate($perPage);

        // 统计数据
        $statistics = $this->getShareStatistics($nursing_home_id, $startDate);

        return view('data-center.shares', compact(
            'shareRecords',
            'statistics',
            'dateRange',
            'sortBy',
            'searchUser',
            'searchVideo',
            'filteredUser',
            'filteredDiary'
        ));
    }

    /**
     * 获取分享统计数据
     */
    private function getShareStatistics($nursing_home_id, $startDate)
    {
        $baseQuery = UserShareRecord::where('nursing_home_id', $nursing_home_id);

        return [
            // 总分享次数（包括视频分享和机构主页分享）
            'total_shares' => $baseQuery->clone()->where('created_at', '>=', $startDate)->count(),

            // 今日分享次数
            'today_shares' => $baseQuery->clone()->whereDate('created_at', today())->count(),

            // 昨日分享次数
            'yesterday_shares' => $baseQuery->clone()->whereDate('created_at', now()->subDay())->count(),

            // 最近一周分享次数
            'week_shares' => $baseQuery->clone()
                ->where('created_at', '>=', now()->subDays(6)->startOfDay())
                ->count(),

            // 总点击次数（有to_user_id的记录）
            'total_clicks' => $baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('to_user_id')
                ->count(),

            // 今日点击次数
            'today_clicks' => $baseQuery->clone()
                ->whereDate('created_at', today())
                ->whereNotNull('to_user_id')
                ->count(),

            // 最近一周点击次数
            'week_clicks' => $baseQuery->clone()
                ->where('created_at', '>=', now()->subDays(6)->startOfDay())
                ->whereNotNull('to_user_id')
                ->count(),

            // 点击率
            'click_rate' => $this->calculateClickRate($nursing_home_id, $startDate),

            // 最近一周点击率
            'week_click_rate' => $this->calculateWeeklyClickRate($nursing_home_id),

            // 活跃分享用户数
            'active_sharers' => $baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->distinct('from_user_id')
                ->count(),

            // 被分享的视频数（只统计视频分享，不包括机构主页分享）
            'shared_videos' => $baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->where('share_type', UserShareRecord::SHARE_TYPE_VIDEO)
                ->whereNotNull('diary_id')
                ->distinct('diary_id')
                ->count(),

            // 机构主页分享次数
            'nursing_home_shares' => $baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->where('share_type', UserShareRecord::SHARE_TYPE_NURSING_HOME)
                ->count(),

            // 机构主页分享点击次数
            'nursing_home_clicks' => $baseQuery->clone()
                ->where('created_at', '>=', $startDate)
                ->where('share_type', UserShareRecord::SHARE_TYPE_NURSING_HOME)
                ->whereNotNull('to_user_id')
                ->count(),
        ];
    }

    /**
     * 计算点击率
     */
    private function calculateClickRate($nursing_home_id, $startDate)
    {
        $totalShares = UserShareRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate)
            ->count();

        if ($totalShares == 0) {
            return 0;
        }

        $totalClicks = UserShareRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('to_user_id')
            ->count();

        return round(($totalClicks / $totalShares) * 100, 2);
    }

    /**
     * 计算最近一周点击率
     */
    private function calculateWeeklyClickRate($nursing_home_id)
    {
        $weekStartDate = now()->subDays(6)->startOfDay();

        $weekShares = UserShareRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $weekStartDate)
            ->count();

        if ($weekShares == 0) {
            return 0;
        }

        $weekClicks = UserShareRecord::where('nursing_home_id', $nursing_home_id)
            ->where('created_at', '>=', $weekStartDate)
            ->whereNotNull('to_user_id')
            ->count();

        return round(($weekClicks / $weekShares) * 100, 2);
    }
}
