<?php

// config for antto/sms
return [
    'code' => [
        'template'      => env('SMS_TEMPELATE_ID', 'SMS_478640401'),   // 验证码模板
        'length'        => 4,                         // 验证码长度
        'valid_minutes' => 10,                        // 验证码有效时间 分钟
        'with_minutes'  => false,                      // 验证码模板是否带有有效期变量
        'interval'      => 60,                        // 重发间隔 秒
    ],

    // 手机号正则
    'mobile_regex' => '/^((\+?86)|(\+86))?1\d{10}$/',

    'easy_sms' => [
        'timeout'  => 5.0,

        // 默认发送配置
        'default'  => [
            // 网关调用策略，默认：顺序调用
            'strategy' => \Overtrue\EasySms\Strategies\OrderStrategy::class,

            // 默认可用的发送网关
            'gateways' => [
                env('SMS_GATEWAY', 'errorlog')
            ],
        ],

        // 可用的网关配置
        'gateways' => [
            'errorlog' => [
                'file' => storage_path('logs/sms.log'),
            ],
            'tinree' => [
                'accesskey' => env('TINREE_KEY'),      // 平台分配给用户的accesskey
                'secret'    => env('TINREE_SECRET'),   // 平台分配给用户的secret
                'sign'      => env('TINREE_SIGN'),     // 平台上申请的接口短信签名或者签名ID
            ],
            'aliyun' => [
                'access_key_id'     => 'LTAI5tMCGj8sjrU4zGhxgvHp',
                'access_key_secret' => '******************************',
                'sign_name'         => '老邻日记',
            ],
        ],
    ],

    // 发送记录写入数据库
    'dblog' => [
        'enable' => env('SMS_DBLOG', true),
        'table' => 'sms_log',
    ],

    'content' => '您的验证码为：%s，请勿泄露于他人！',
];
