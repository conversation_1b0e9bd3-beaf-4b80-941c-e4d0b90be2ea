@extends('layouts.web')

@section('sidebar')
@include('web.partials.sidebar', [
'activeMenu' => '/user-management',
])
@endsection

@section('content')
<div class="container-fluid p-lg-4 bg-light">
  <div class="container">
    <div class="flex justify-between items-center mb-4">
      <h5 class="mb-0">用户详情</h5>
      <a href="{{ route('user-management.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left mr-2"></i>
        返回列表
      </a>
    </div>

    <!-- 页面标题栏 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <!-- 用户头像 -->
          <img src="{{ $targetUser->full_avatar_url }}" class="w-16 rounded-full mr-2" alt="用户头像">
          <div>
            <h1 class="text-xl font-bold text-gray-900">{{ $targetUser->nickname }}</h1>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户信息概览卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex items-center mb-4">
        <i class="bi bi-person mr-2 text-blue-600" style="font-size: 20px;"></i>
        <p class="text-lg font-semibold text-gray-800">基本信息</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex flex-col gap-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">备注名</span>
            <span class="text-sm font-medium text-gray-900">{{ $userNote->nickname ?? '未设置' }}</span>
          </div>
        </div>
        <div class="flex flex-col gap-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">注册时间</span>
            <span class="text-sm font-medium text-gray-900">{{ $targetUser->created_at->format('Y-m-d H:i') }}</span>
          </div>
        </div>

        <!-- <div class="flex flex-col gap-3">
          <div class="flex justify-between">
            <span class="text-sm text-gray-500">最后登录</span>
            <span class="text-sm font-medium text-gray-900">{{ $targetUser->last_login_at ? $targetUser->last_login_at->format('Y-m-d H:i') : '从未登录' }}</span>
          </div>
        </div> -->

        <div class="flex flex-col gap-3">
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-500">备注</span>
            <span class="text-sm font-medium text-gray-900 text-right max-w-[200px]">{{ $userNote->notes ?? '无备注' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex items-center mb-4">
        <i class="bi bi-bar-chart mr-2 text-green-600"></i>
        <p class="text-lg font-semibold text-gray-800">数据统计</p>
      </div>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
              <i class="bi bi-eye text-white"></i>
            </div>
            <div>
              <p class="text-sm text-gray-600">浏览次数</p>
              <p class="text-xl font-bold text-gray-900">{{ number_format($userStats['total_views']) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-600">分享次数</p>
              <p class="text-xl font-bold text-gray-900">{{ number_format($userStats['total_shares']) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
              <i class="bi bi-bookmark text-white"></i>
            </div>
            <div>
              <p class="text-sm text-gray-600">收藏数</p>
              <p class="text-xl font-bold text-gray-900">{{ number_format($userStats['total_collections']) }}</p>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
              <i class="bi bi-lightning text-white"></i>
            </div>
            <div>
              <p class="text-sm text-gray-600">参与度评分</p>
              <p class="text-xl font-bold text-gray-900">{{ number_format($userStats['engagement_score'], 1) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 趋势分析 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
          <i class="bi bi-graph-up mr-2 text-indigo-600"></i>
          <p class="text-lg font-semibold text-gray-800">互动趋势分析</p>
        </div>
        <select class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option>最近7天</option>
          <option>最近30天</option>
          <option>最近90天</option>
        </select>
      </div>
      <div class="bg-gray-50 p-4 rounded-lg">
        <div id="trendChart" style="height: 350px;"></div>
      </div>
    </div>

    <!-- 互动详情选项卡 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="border-b border-gray-200">
        <ul class="nav nav-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="views-tab" data-bs-toggle="tab" data-bs-target="#views-tab-pane" type="button" role="tab" aria-controls="views-tab-pane" aria-selected="true">
              最近浏览
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="shares-tab" data-bs-toggle="tab" data-bs-target="#shares-tab-pane" type="button" role="tab" aria-controls="shares-tab-pane" aria-selected="false">
              最近分享
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="collections-tab" data-bs-toggle="tab" data-bs-target="#collections-tab-pane" type="button" role="tab" aria-controls="collections-tab-pane" aria-selected="false">
              最近收藏
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="likes-tab" data-bs-toggle="tab" data-bs-target="#likes-tab-pane" type="button" role="tab" aria-controls="likes-tab-pane" aria-selected="false">
              最近点赞
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments-tab-pane" type="button" role="tab" aria-controls="comments-tab-pane" aria-selected="false">
              最近评论
            </button>
          </li>
        </ul>
      </div>

      <!-- 内容区域 -->
      <div class="tab-content p-6">
        <!-- 最近浏览 -->
        <div class="tab-pane fade show active" id="views-tab-pane" role="tabpanel" aria-labelledby="views-tab">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($interactionDetails['recent_views'] as $view)
            <div class="border border-gray-200 rounded-lg p-4 transition-shadow duration-200 hover:shadow-md">
              <div class="flex items-start justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $view->diary->title ?? '未命名日记' }}</h4>
                <span class="ml-2 text-xs text-gray-500">浏览</span>
              </div>
              <p class="text-xs text-gray-400 mb-1">{{ $view->created_at->diffForHumans() }}</p>
              <p class="text-xs text-gray-500">{{ $view->created_at->format('Y-m-d H:i:s') }}</p>
            </div>
            @empty
            <div class="col-span-full text-center py-8">
              <i class="bi bi-eye-slash text-3xl text-gray-400 mb-2"></i>
              <p class="text-sm text-gray-500">暂无浏览记录</p>
            </div>
            @endforelse
          </div>
        </div>

        <!-- 最近分享 -->
        <div class="tab-pane fade" id="shares-tab-pane" role="tabpanel" aria-labelledby="shares-tab">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($interactionDetails['recent_shares'] as $share)
            <div class="border border-gray-200 rounded-lg p-4 transition-shadow duration-200 hover:shadow-md">
              <div class="flex items-start justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $share->diary->title ?? '未命名日记' }}</h4>
                <span class="ml-2 text-xs text-green-600">分享</span>
              </div>
              <p class="text-xs text-gray-400 mb-1">{{ $share->created_at->diffForHumans() }}</p>
              <p class="text-xs text-gray-500">{{ $share->created_at->format('Y-m-d H:i:s') }}</p>
            </div>
            @empty
            <div class="col-span-full text-center py-8">
              <i class="bi bi-share text-white"></i>
              <p class="text-sm text-gray-500">暂无分享记录</p>
            </div>
            @endforelse
          </div>
        </div>

        <!-- 最近收藏 -->
        <div class="tab-pane fade" id="collections-tab-pane" role="tabpanel" aria-labelledby="collections-tab">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($interactionDetails['recent_collections'] as $collection)
            <div class="border border-gray-200 rounded-lg p-4 transition-shadow duration-200 hover:shadow-md">
              <div class="flex items-start justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $collection->diary->title ?? '未命名日记' }}</h4>
                <span class="ml-2 text-xs text-yellow-600">收藏</span>
              </div>
              <p class="text-xs text-gray-400 mb-1">{{ $collection->created_at->diffForHumans() }}</p>
              <p class="text-xs text-gray-500">{{ $collection->created_at->format('Y-m-d H:i:s') }}</p>
            </div>
            @empty
            <div class="col-span-full text-center py-8">
              <i class="bi bi-bookmark text-3xl text-gray-400 mb-2"></i>
              <p class="text-sm text-gray-500">暂无收藏记录</p>
            </div>
            @endforelse
          </div>
        </div>

        <!-- 最近点赞 -->
        <div class="tab-pane fade" id="likes-tab-pane" role="tabpanel" aria-labelledby="likes-tab">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($interactionDetails['recent_likes'] as $like)
            <div class="border border-gray-200 rounded-lg p-4 transition-shadow duration-200 hover:shadow-md">
              <div class="flex items-start justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900 truncate">{{ $like->diary->title ?? '未命名日记' }}</h4>
                <span class="ml-2 text-xs text-red-600">点赞</span>
              </div>
              <p class="text-xs text-gray-400 mb-1">{{ $like->created_at->diffForHumans() }}</p>
              <p class="text-xs text-gray-500">{{ $like->created_at->format('Y-m-d H:i:s') }}</p>
            </div>
            @empty
            <div class="col-span-full text-center py-8">
              <i class="bi bi-heart text-3xl text-gray-400 mb-2"></i>
              <p class="text-sm text-gray-500">暂无点赞记录</p>
            </div>
            @endforelse
          </div>
        </div>

        <!-- 最近评论 -->
        <div class="tab-pane fade" id="comments-tab-pane" role="tabpanel" aria-labelledby="comments-tab">
          <div class="flex flex-col gap-4">
            @forelse($interactionDetails['recent_comments'] as $comment)
            <div class="border border-gray-200 rounded-lg p-4 transition-shadow duration-200 hover:shadow-md">
              <div class="flex justify-between items-start mb-2">
                <h4 class="text-sm font-medium text-gray-900">{{ $comment->diary->title ?? '未命名日记' }}</h4>
                <span class="text-xs text-blue-600">评论</span>
              </div>
              <p class="text-xs text-gray-400 mb-2">{{ $comment->created_at->diffForHumans() }}</p>
              <p class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md mb-2">{{ Str::limit($comment->content, 150) }}</p>
              <p class="text-xs text-gray-500">{{ $comment->created_at->format('Y-m-d H:i:s') }}</p>
            </div>
            @empty
            <div class="text-center py-8">
              <i class="bi bi-chat text-3xl text-gray-400 mb-2"></i>
              <p class="text-sm text-gray-500">暂无评论记录</p>
            </div>
            @endforelse
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    try {
      // 准备趋势图表数据
      const trendData = @json($dailyTrends ?? []);
      console.log('图表数据:', trendData);

      // 检查是否有数据
      if (!trendData || Object.keys(trendData).length === 0) {
        const chartDom = document.getElementById('trendChart');
        chartDom.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500 text-sm">暂无数据</div>';
        return;
      }

      const dates = Object.keys(trendData);
      const views = dates.map(date => trendData[date].views || 0);
      const shares = dates.map(date => trendData[date].shares || 0);

      console.log('日期:', dates);
      console.log('浏览数据:', views);
      console.log('分享数据:', shares);

      // 确保图表容器存在
      const chartDom = document.getElementById('trendChart');
      if (!chartDom) {
        console.error('图表容器未找到');
        return;
      }

      // 初始化 ECharts 实例
      const trendChart = echarts.init(chartDom);

      // 配置图表选项
      const option = {
        title: {
          text: '用户互动趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['浏览次数', '分享次数'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          minInterval: 1
        },
        series: [{
            name: '浏览次数',
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 2
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.3,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(54, 162, 235, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(54, 162, 235, 0.1)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
            data: views
          },
          {
            name: '分享次数',
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: {
              width: 2
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.3,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                  offset: 0,
                  color: 'rgba(255, 99, 132, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(255, 99, 132, 0.1)'
                }
              ])
            },
            emphasis: {
              focus: 'series'
            },
            data: shares
          }
        ]
      };

      // 渲染图表
      trendChart.setOption(option);

      // 响应窗口大小变化
      window.addEventListener('resize', () => {
        trendChart.resize();
      });

      console.log('图表初始化成功');
    } catch (error) {
      console.error('图表初始化失败:', error);
      const chartDom = document.getElementById('trendChart');
      if (chartDom) {
        chartDom.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500 text-sm">图表加载失败</div>';
      }
    }
  });

  // Bootstrap tab functionality is handled by Bootstrap's native tab component
  // No custom JavaScript needed for tab switching

  // 时间范围选择器
  document.addEventListener('DOMContentLoaded', function() {
    const timeSelect = document.querySelector('select');
    if (timeSelect) {
      timeSelect.addEventListener('change', function() {
        console.log('时间范围已切换为:', this.value);
      });
    }
  });
</script>
@endsection
