<template>
  <view class="page-content">
    <bind-phone-modal />

    <view class="custom-nav">
      <view class="nav-buttons">
        <view class="capsule">
          <view class="button back" @click="goBack">
            <u-icon name="arrow-left" color="#fff" size="22px"></u-icon>
            <view class="tab-left logo-wrap mt-20 px-20">
                <image
                  src="@/static/images/logo.png"
                  style="width: 76rpx; height: 76rpx"
                />
            </view>
          </view>
        </view>
      </view>
    </view>

    <y-video-slide
      ref="videoSlide"
      v-if="list.length > 0"
      video-height="calc(100vh - 20px)"
      :data="list"
      :videoIndex.sync="currentIndex"
      @refresh="refresh"
      @loadMore="loadMore"
      @share="share"
      @fabulous="fabulous"
      @follow="follow"
      @collect="collect"
      @commentFabulous="commentFabulous"
    >
    </y-video-slide>

    <!-- 隐私协议 -->
    <privacy-popup-modal ref="privacyComponent"></privacy-popup-modal>

    <!-- 补全资料弹出 -->
    <complete-user-info
      :show="showUserInfoModal"
      @close="handleUserInfoModalClose"
      @success="handleUserInfoUpdateSuccess"
    />

  </view>
</template>

<script>
import api from "@/common/api";
import PrivacyPopupModal from '@/components/privacy-popup/privacy-popup-modal.vue';
import CompleteUserInfo from "@/components/complete-user-info/complete-user-info.vue";
export default {
  components: {
    PrivacyPopupModal,
    CompleteUserInfo,
  },
  data() {
    return {
      list: [],
      currentPage: 1,
      totalPages: 0,
      currentIndex: 0,
      id: "",
      nursing_home_id: "",
      shareVideoInfo: {},
      shareType: 0,
      from: "",
      user: {},
      showUserInfoModal: false,
      hasCheckedUserInfo: false,
      userNeedComplete: false,
    };
  },
  onLoad(options) {
    // 分享的参数：id=21&nursing_home_id=8&share=1&from=1144
    this.id = options.id || 0;
    this.shareType = options.share || 0;
    this.nursing_home_id = options.nursing_home_id || "";
    this.from = options.from || "";
    // 记录分享成功 延迟2秒后执行
    setTimeout(() => {
      this.recordShareClick();
    }, 2000);

    this.loadData();
    this.user = this.getUserInfo();
  },
  onHide() {
    // 页面隐藏时清除浏览记录定时器
    if (this.$refs.videoSlide && this.$refs.videoSlide.clearBrowseTimer) {
      this.$refs.videoSlide.clearBrowseTimer();
    }
  },
  onUnload() {
    // 页面卸载时清除浏览记录定时器
    if (this.$refs.videoSlide && this.$refs.videoSlide.clearBrowseTimer) {
      this.$refs.videoSlide.clearBrowseTimer();
    }
  },
  onReady() {},
  methods: {
    goBack() {
      if (this.shareType == 1) {
        // 返回首页
        uni.reLaunch({
          url: "/pages/index/index",
        });
        return;
      } else {
        // 返回上一级页面
        uni.navigateBack({
          delta: 1,
        });
      }
    },

    // 获取用户信息（优先级：store > user > userInfo）
    getUserInfo() {
      const storeUser = this.$store.getters.getUser;
      const storageUser = uni.getStorageSync("user");
      const userInfo = uni.getStorageSync("userInfo");

      // 优先使用store中的数据，然后是storage，最后是userInfo
      let user = storeUser;
      if (!user || Object.keys(user).length === 0) {
        user = storageUser;
      }
      if (!user || Object.keys(user).length === 0) {
        user = userInfo;
      }
      this.user = user;

      return user;
    },

    goHomepage() {},

    // 记录分享点击
    async recordShareClick() {
      try {
        const res = await api.shareClick({
          diary_id: this.id,
          from_user_id: this.from,
        });
      } catch (error) {
        console.error("分享点击接口调用失败:", error);
      }
    },

    refresh() {
      console.log("松开刷新");
      this.currentPage = 1;
      this.currentIndex = 0;
      this.list = [];
      this.loadData();
    },

    loadMore() {
      if (this.totalPages > this.currentPage) {
        this.currentPage++;
        this.loadData();
      } else {
        uni.showToast({
          title: "没有更多了",
          icon: "none",
        });
      }
    },

    share(item) {
      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行分享操作
      if (this.userNeedComplete) return;

      this.shareVideoInfo = item;
    },

    // 点赞
    fabulous(item) {
      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行点赞操作
      if (this.userNeedComplete) return;

      api
        .like({
          diary_id: item.id,
        })
        .then((res) => {
          if (res.data.code == 200) {
            item.is_liked = !item.is_liked;
            // 数量
            if (item.is_liked) {
              item.likes_number++;
            } else {
              item.likes_number--;
            }
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },

    // 收藏
    collect(item) {
      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行收藏操作
      if (this.userNeedComplete) return;

      api
        .collection({
          diary_id: item.id,
        })
        .then((res) => {
          if (res.data.code == 200) {
            item.is_collected = !item.is_collected;
            // 数量
            if (item.is_collected) {
              item.favorite_number++;
            } else {
              item.favorite_number--;
            }
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },

    // 关注
    follow(item, flag) {
      // 先检查用户信息是否完整
      this.checkAndShowUserInfoModal();

      // 如果需要补全信息，不执行关注操作
      if (this.userNeedComplete) return;

      api
        .follow({
          data: {
            nursing_home_id: item.nursing_home_id,
          },
          method: "POST",
        })
        .then((res) => {
          if (res.data.code == 200) {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
            item.is_follow = flag;
            this.setFollow(item.nursing_home_id, flag);
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        });
    },

    // commentFabulous(comment) {
    //   console.log(comment);
    // },
    loadData() {
      api
        .getDiariesPlayList(
          {
            page: this.currentPage,
            limit: 5,
            nursing_home_id: this.nursing_home_id,
          },
          this.id
        )
        .then((res) => {
          this.loading = false;
          if (this.currentPage == 1) {
            this.list = res.data.data.list;
          } else {
            this.list = this.list.concat(res.data.data.list);
          }
          this.totalPages = res.data.data.meta?.pagination?.total_pages || 1;
        });
    },

    // 检查用户信息是否完整
    checkUserInfoComplete(user) {
      if (!user) return;

      const hasAvatar = user.avatar_url && user.avatar_url !== "";
      const hasNickname =
        user.nickname &&
        user.nickname !== "" &&
        !user.nickname.startsWith("用户");

      if (!hasAvatar || !hasNickname) {
        // 用户信息不完整，标记需要补全
        this.userNeedComplete = true;
      }
      this.hasCheckedUserInfo = true;
    },

    // 检查并显示用户信息补全弹窗
    checkAndShowUserInfoModal() {
      // 重置状态
      this.userNeedComplete = false;

      const user = this.getUserInfo();
      if (user) {
        this.checkUserInfoComplete(user);
        if (this.userNeedComplete) {
          this.showUserInfoModal = true;
        }
      }
    },

    // 处理用户信息补全成功
    handleUserInfoUpdateSuccess() {
      // 刷新用户信息
      this.refreshUserInfo().then(() => {
        const refreshedUser = this.getUserInfo();
        if (refreshedUser) {
          this.user = refreshedUser;
          this.userNeedComplete = false;
        }
      });
    },

    // 处理弹窗关闭
    handleUserInfoModalClose() {
      this.showUserInfoModal = false;
    },

    // 刷新用户信息
    refreshUserInfo() {
      return new Promise((resolve) => {
        // 这里可以调用API刷新用户信息，或者从store中获取最新数据
        const user = this.$store.getters.getUser;
        if (user && Object.keys(user).length > 0) {
          this.user = user;
          uni.setStorageSync("user", user);
        }
        resolve();
      });
    },
  },

  onShareAppMessage() {
    // 获取当前正在播放的视频信息
    const currentVideo = this.list[this.currentIndex];
    let from = this.user.id || "";
    // 调用分享接口
    if (currentVideo) {
      let path = `pages/index/index?diary_id=${currentVideo.id}&nursing_home_id=${currentVideo.nursing_home_id}&share=1&from=${from}`;
      api
        .share({
          diary_id: currentVideo.id,
        })
        .then((res) => {
          if (res.data.code === 200) {
            console.log("分享记录创建成功:", res.data.message);
            // 可以在这里添加分享成功的提示或其他逻辑
          } else {
            console.error("分享记录创建失败:", res.data.message);
          }
        })
        .catch((error) => {
          console.error("分享接口调用失败:", error);
        });
      return {
        title: currentVideo.title,
        path: path,
        imageUrl: currentVideo.cover_url,
      };
    }

    // 如果没有获取到视频信息，使用默认值
    return {
      title: "老邻日记",
      path: "/pages/index/index",
    };
  },
};
</script>

<style lang="scss">
.page-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #000;
  /* align-items: center; */
  /* justify-content: center; */
}
.tab-wrap {
  position: fixed;
  left: 0;
  top: 0;
  width: 750rpx;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0 0 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.5) 0%,
    rgba(0, 0, 0, 0) 100%
  );
}
.prompt-text {
  color: #fff;
  font-size: 28rpx;
  line-height: 52rpx;
}
.prompt-text-wrap {
  padding: 250rpx 60rpx;
}
.custom-nav {
  position: fixed;
  top: 49px;
  left: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  z-index: 100;
}
.nav-buttons {
  display: flex;
  align-items: center;
}
.capsule {
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 5px;
}
.button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px 10px;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
}
</style>
