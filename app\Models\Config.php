<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Config extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;
    protected $fillable = [
        'title',
        'key',
        'type',
        'value1',
        'value2',
        'value3',
        'value4',
        'value5',
        'value6',
        'value7',
        'value8',
        'value9',
        'deleted_at'
    ];

    protected $casts= [
        'value4' => 'array',
        'value6' => 'array',
        'value7' => 'date',
        'value8' => 'datetime',
    ];

    public function getFullImageUrlAttribute()
    {
        return getImageUrl($this->value3);
    }

    public function getFullImageUrlsAttribute()
    {
        if($this->value4){
            return getImageUrls(json_encode($this->value4));
        }
        return [];
    }
}
