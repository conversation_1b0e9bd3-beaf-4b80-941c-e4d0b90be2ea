<?php

namespace App\Http\Resources;

use Illuminate\Support\Str;
use Illuminate\Http\Resources\Json\JsonResource;

class DiaryListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $like_number_text = $this->likes_number > 9999 ? round($this->likes_number / 10000, 1) . '万' : $this->likes_number;

        // 标题字数超过34个字，显示展开
        $show_title_expand = Str::length($this->title) > 34 ? true : false;

        return [
            'id'                => $this->id.'', //改为了字符串
            'share_url'         => config('app.url') . '/m/play/' . $this->id,
            'nursing_home_id'   => $this->nursing_home_id,
            'author'            => $this->whenLoaded('nursingHome') ? new NursingHomeShortResource($this->nursingHome) : '',
            'cover_url'         => $this->cover_url,
            'likes_number'      => $this->likes_number ?? 0,
            'like_number_text'  => $like_number_text,
            'comment_number'    => $this->comment_number ?? 0,
            'browse_number'     => $this->browse_number ?? 0,
            'favorite_number'   => $this->favorite_number ?? 0,
            'share_number'      => $this->share_number ?? 0,
            'is_recommend'      => $this->is_recommend,
            'publish_at'        => $this->publish_at,
            'is_follow'         => $this->is_follow ? 1 : 0,
            'is_liked'          => $this->is_liked,
            'is_collected'      => $this->is_collected ?? 0,
            'status'            => $this->status,
            'created_at'        => optional($this->created_at)->toDateTimeString(),
            'updated_at'        => optional($this->updated_at)->toDateTimeString(),
            'src'               => $this->upload_video_url ?? $this->video_url,
            'userName'          => optional($this->nursingHome)->name,
            'title'             => $this->title,
            'title_short'       => Str::length($this->title) > 34 ? Str::limit($this->title, 66) : $this->title,
            'show_title_expand' => $show_title_expand,
            'userImg'           => optional($this->nursingHome)->avatar_url,
        ];
    }
}
