<?php

namespace App\Admin\Repositories;

use App\Models\Contract as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class Contract extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 设定签约状态
     */
    const STATUS_PENDING = 0;
    const STATUS_SIGNED = 1;
    const STATUS_CANCELLED = 2;

    /**
     * 签约状态集合
     */
    public static $statusMap = [
        self::STATUS_PENDING => '待签约',
        self::STATUS_SIGNED => '已签约',
        self::STATUS_CANCELLED => '已取消',
    ];

    /**
     * 签约状态颜色集合
     */
    public static $statusColor = [
        self::STATUS_PENDING => 'warning',
        self::STATUS_SIGNED => 'success',
        self::STATUS_CANCELLED => 'danger',
    ];
}
